import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import RevenueCatService, { RevenueCatCustomerInfo } from './RevenueCatService';

export type SubscriptionTier = 'monthly' | 'yearly';

export interface SubscriptionState {
  tier: SubscriptionTier | null;
  isActive: boolean;
  expirationDate: Date | null;
  isTrialActive: boolean;
  trialEndDate: Date | null;
  features: string[];
  originalPurchaseDate: Date | null;
  latestPurchaseDate: Date | null;
}

interface SubscriptionContextType {
  subscription: SubscriptionState;
  isLoading: boolean;
  subscribe: (tier: SubscriptionTier) => Promise<boolean>;
  cancelSubscription: () => Promise<boolean>;
  restorePurchases: () => Promise<boolean>;
  hasFeature: (feature: string) => boolean;
  isPremiumUser: () => boolean;
  isTrialUser: () => boolean;
  getDaysRemaining: () => number;
  getTrialDaysRemaining: () => number;
}

const defaultSubscription: SubscriptionState = {
  tier: null,
  isActive: false,
  expirationDate: null,
  isTrialActive: false,
  trialEndDate: null,
  features: [],
  originalPurchaseDate: null,
  latestPurchaseDate: null,
};

// Feature sets for each tier
const tierFeatures = {
  monthly: [
    'unlimited_scans',
    'advanced_ai',
    'full_history',
    'offline_mode',
    'priority_support',
    'no_ads',
    'export_data',
    'cloud_sync'
  ],
  yearly: [
    'unlimited_scans',
    'advanced_ai',
    'full_history',
    'offline_mode',
    'priority_support',
    'no_ads',
    'export_data',
    'cloud_sync',
    'extended_offline',
    'advanced_analytics',
    'custom_collections',
    'early_features'
  ]
};

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const [subscription, setSubscription] = useState<SubscriptionState>(defaultSubscription);
  const [isLoading, setIsLoading] = useState(true);
  const revenueCatService = RevenueCatService.getInstance();

  useEffect(() => {
    loadSubscriptionState();
  }, []);

  const loadSubscriptionState = async () => {
    try {
      setIsLoading(true);

      // Try to get subscription state from RevenueCat first
      if (revenueCatService.isRevenueCatInitialized()) {
        const customerInfo = await revenueCatService.getCustomerInfo();
        const newSubscription = convertRevenueCatToSubscriptionState(customerInfo);
        setSubscription(newSubscription);

        // Save to local storage as backup
        await saveSubscriptionState(newSubscription);
      } else {
        // Fallback to local storage if RevenueCat is not available
        const savedSubscription = await AsyncStorage.getItem('subscription_state');
        if (savedSubscription) {
          const parsed = JSON.parse(savedSubscription);
          // Convert date strings back to Date objects
          if (parsed.expirationDate) {
            parsed.expirationDate = new Date(parsed.expirationDate);
          }
          if (parsed.trialEndDate) {
            parsed.trialEndDate = new Date(parsed.trialEndDate);
          }
          if (parsed.originalPurchaseDate) {
            parsed.originalPurchaseDate = new Date(parsed.originalPurchaseDate);
          }
          if (parsed.latestPurchaseDate) {
            parsed.latestPurchaseDate = new Date(parsed.latestPurchaseDate);
          }

          setSubscription(parsed);
        }
      }
    } catch (error) {
      console.error('Error loading subscription state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const convertRevenueCatToSubscriptionState = (customerInfo: RevenueCatCustomerInfo): SubscriptionState => {
    return {
      tier: customerInfo.tier,
      isActive: customerInfo.isActive,
      expirationDate: customerInfo.expirationDate,
      isTrialActive: customerInfo.isTrialActive,
      trialEndDate: customerInfo.trialEndDate,
      features: customerInfo.features,
      originalPurchaseDate: customerInfo.originalPurchaseDate,
      latestPurchaseDate: customerInfo.latestPurchaseDate,
    };
  };

  const saveSubscriptionState = async (newState: SubscriptionState) => {
    try {
      await AsyncStorage.setItem('subscription_state', JSON.stringify(newState));
    } catch (error) {
      console.error('Error saving subscription state:', error);
    }
  };

  const subscribe = async (tier: SubscriptionTier): Promise<boolean> => {
    try {
      if (revenueCatService.isRevenueCatInitialized()) {
        // Use RevenueCat for real purchases
        const packages = await revenueCatService.getAvailablePackages();
        const targetPackage = packages.find(pkg =>
          pkg.identifier === tier ||
          pkg.product.identifier.includes(tier)
        );

        if (!targetPackage) {
          console.error('No package found for tier:', tier);
          return false;
        }

        const result = await revenueCatService.purchasePackage(targetPackage);

        if (result.success && result.customerInfo) {
          const newSubscription = convertRevenueCatToSubscriptionState(result.customerInfo);
          setSubscription(newSubscription);
          await saveSubscriptionState(newSubscription);
          return true;
        }

        return false;
      } else {
        // Fallback to mock subscription for development/testing
        const now = new Date();
        let expirationDate: Date;
        let isTrialActive = false;
        let trialEndDate: Date | null = null;

        // Set expiration based on tier
        if (tier === 'monthly') {
          expirationDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
        } else { // yearly
          expirationDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 365 days
          // Add 3-day trial for yearly subscription
          isTrialActive = true;
          trialEndDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days
        }

        const newSubscription: SubscriptionState = {
          tier,
          isActive: true,
          expirationDate,
          isTrialActive,
          trialEndDate,
          features: tierFeatures[tier],
          originalPurchaseDate: now,
          latestPurchaseDate: now,
        };

        setSubscription(newSubscription);
        await saveSubscriptionState(newSubscription);

        return true;
      }
    } catch (error) {
      console.error('Error subscribing:', error);
      return false;
    }
  };

  const cancelSubscription = async (): Promise<boolean> => {
    try {
      const newSubscription: SubscriptionState = {
        ...subscription,
        isActive: false,
        tier: null,
        features: [],
      };

      setSubscription(newSubscription);
      await saveSubscriptionState(newSubscription);
      
      return true;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return false;
    }
  };

  const restorePurchases = async (): Promise<boolean> => {
    try {
      if (revenueCatService.isRevenueCatInitialized()) {
        // Use RevenueCat to restore purchases
        const result = await revenueCatService.restorePurchases();

        if (result.success && result.customerInfo) {
          const newSubscription = convertRevenueCatToSubscriptionState(result.customerInfo);
          setSubscription(newSubscription);
          await saveSubscriptionState(newSubscription);
          return true;
        }

        return result.success;
      } else {
        // Fallback to reloading local state
        await loadSubscriptionState();
        return true;
      }
    } catch (error) {
      console.error('Error restoring purchases:', error);
      return false;
    }
  };

  const hasFeature = (feature: string): boolean => {
    return subscription.features.includes(feature);
  };

  const isPremiumUser = (): boolean => {
    return subscription.isActive || subscription.isTrialActive;
  };

  const isTrialUser = (): boolean => {
    return subscription.isTrialActive && !subscription.isActive;
  };

  const getDaysRemaining = (): number => {
    if (!subscription.expirationDate) return 0;
    const now = new Date();
    const diff = subscription.expirationDate.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  };

  const getTrialDaysRemaining = (): number => {
    if (!subscription.trialEndDate || !subscription.isTrialActive) return 0;
    const now = new Date();
    const diff = subscription.trialEndDate.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  };

  return (
    <SubscriptionContext.Provider
      value={{
        subscription,
        isLoading,
        subscribe,
        cancelSubscription,
        restorePurchases,
        hasFeature,
        isPremiumUser,
        isTrialUser,
        getDaysRemaining,
        getTrialDaysRemaining,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export default SubscriptionContext;
