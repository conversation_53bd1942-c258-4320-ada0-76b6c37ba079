import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
  Dimensions,
  ActivityIndicator,
  ColorValue,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Settings as SettingsIcon, User, Bell, Camera, Globe, Palette, ChevronRight, 
  Crown, Zap, Star, RefreshCw, Sliders, Download, Trash2, Type, 
  LayoutGrid, Shield, Volume2, Vibrate, Eye, HelpCircle, Mail, MessageSquare, 
  ExternalLink, Info, LucideIcon
} from 'lucide-react-native';
import { useSubscription } from '@/components/SubscriptionContext';
import SubscriptionModal from '@/components/SubscriptionModal';
import SettingsPopup from '@/components/SettingsPopup';
import LargeTextModal from '@/components/LargeTextModal';
import ThemeSelectionModal from '@/components/ThemeSelectionModal';
import LayoutSettingsModal from '@/components/LayoutSettingsModal';
import DataManager from '@/components/DataManager';
import { useSettings, UserSettings } from '@/components/SettingsContext';
import { useTheme, ThemeColors } from '@/components/ThemeContext';
import HapticService from '@/components/HapticService';

// Define Prop Types for Components
interface SettingRowProps {
  icon?: LucideIcon;
  title: string;
  subtitle?: string;
  value?: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
  colors: ThemeColors;
  isDestructive?: boolean;
  disabled?: boolean;
}

interface SwitchRowProps extends Omit<SettingRowProps, 'rightComponent' | 'onPress' | 'value'> {
  value: boolean;
  onValueChange: (value: boolean) => void;
}

interface SettingSectionProps {
  title: string;
  children: React.ReactNode;
  colors: ThemeColors;
}


const SettingRow: React.FC<SettingRowProps> = ({ icon: Icon, title, subtitle, value, onPress, rightComponent, colors, isDestructive = false, disabled = false }) => (
  <TouchableOpacity 
    style={[styles.settingRow, { borderBottomColor: colors.border, opacity: disabled ? 0.5 : 1 }]} 
    onPress={onPress}
    disabled={!onPress || disabled}
    activeOpacity={0.7}>
    <View style={styles.settingLeft}>
      {Icon && (
        <View style={[styles.settingIcon, { backgroundColor: colors.backgroundSecondary }]}>
          <Icon size={20} color={isDestructive ? colors.error : colors.textSecondary} />
        </View>
      )}
      <View style={styles.settingText}>
        <Text style={[styles.settingTitle, { color: isDestructive ? colors.error : colors.text }]}>{title}</Text>
        {subtitle && <Text style={[styles.settingSubtitle, { color: colors.textSecondary }]}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingRight}>
      {value && <Text style={[styles.settingValue, { color: colors.textSecondary }]}>{value}</Text>}
      {rightComponent}
      {onPress && !rightComponent && <ChevronRight size={16} color={colors.textSecondary} />}
    </View>
  </TouchableOpacity>
);

const SwitchRow: React.FC<SwitchRowProps> = ({ value, onValueChange, ...rest }) => (
  <SettingRow 
    {...rest} 
    onPress={() => onValueChange(!value)}
    rightComponent={
      <Switch 
        value={value} 
        onValueChange={onValueChange}
        trackColor={{ false: rest.colors.backgroundSecondary, true: rest.colors.primary + '80' }} 
        thumbColor={value ? rest.colors.primary : '#FFFFFF'}
        ios_backgroundColor={rest.colors.backgroundSecondary}
      />
    }
  />
);

const SettingSection: React.FC<SettingSectionProps> = ({ title, children, colors }) => (
  <View style={styles.section}>
    <Text style={[styles.sectionTitle, { color: colors.text }]}>{title}</Text>
    <View style={[styles.sectionContent, { backgroundColor: colors.card, shadowColor: colors.shadow }]}>
      {children}
    </View>
  </View>
);

export default function SettingsScreen() {
  const { settings, updateSettings, isLoading: settingsLoading } = useSettings();
  const [modalVisible, setModalVisible] = useState<string | null>(null);
  const [storageInfo, setStorageInfo] = useState<{ cacheSize: number } | null>(null);
  const { subscription } = useSubscription();
  const { colors } = useTheme();
  const dataManager = DataManager.getInstance();
  const hapticService = HapticService.getInstance();

  useEffect(() => {
    const loadStorage = async () => {
      const info = await dataManager.getStorageInfo();
      setStorageInfo(info);
    };
    loadStorage();
  }, []);

  const handleUpdateSettings = async (newSettings: Partial<UserSettings>) => {
    await hapticService.triggerSelectionFeedback();
    try {
      await updateSettings(newSettings);
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings.');
    }
  };

  const handleClearCache = () => {
    Alert.alert('Clear Cache', 'This will remove temporary files. Are you sure?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Clear', style: 'destructive', onPress: async () => {
          await dataManager.clearCache();
          await hapticService.triggerNotificationFeedback('success');
          const info = await dataManager.getStorageInfo();
          setStorageInfo(info);
          Alert.alert('Success', 'Cache has been cleared.');
      }},
    ]);
  };

  const handleResetData = () => {
    Alert.alert('Reset All Data', 'This is irreversible and will delete all your scans and preferences. Continue?', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Reset', style: 'destructive', onPress: async () => {
            await dataManager.clearAllData();
            // You might want to navigate the user away or restart the app
            Alert.alert('Data Reset', 'All your data has been permanently deleted.');
        }},
    ]);
  };

  const getSubscriptionBadge = () => {
    switch (subscription.tier) {
      case 'pro': return { text: 'PRO', icon: Zap, gradient: [colors.accent, colors.primary] };
      case 'expert': return { text: 'EXPERT', icon: Crown, gradient: [colors.success, colors.accent] };
      default: return { text: 'FREE', icon: Star, gradient: [colors.textSecondary, colors.border] };
    }
  };

  if (settingsLoading || !settings) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading Settings...</Text>
      </View>
    );
  }

  const badge = getSubscriptionBadge();
  const BadgeIcon = badge.icon;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <LinearGradient colors={[colors.background, colors.backgroundSecondary] as [ColorValue, ColorValue]} style={styles.gradient}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Settings</Text>
        </View>

        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
          <TouchableOpacity style={[styles.subscriptionCard, { shadowColor: colors.shadow }]} onPress={() => setModalVisible('subscription')}>
            <LinearGradient colors={badge.gradient as [ColorValue, ColorValue]} style={styles.subscriptionGradient}>
              <View style={styles.subscriptionHeader}>
                  <BadgeIcon size={20} color="#FFFFFF" />
                  <Text style={styles.subscriptionBadgeText}>{badge.text} Member</Text>
              </View>
              <Text style={styles.subscriptionTitle}>{subscription.tier === 'free' ? 'Unlock Premium' : 'Premium Active'}</Text>
              <ChevronRight size={24} color="rgba(255,255,255,0.7)" style={styles.subscriptionChevron} />
            </LinearGradient>
          </TouchableOpacity>

          <SettingSection title="Appearance" colors={colors}>
            <SettingRow icon={Palette} title="Theme" value={settings.theme} onPress={() => setModalVisible('theme')} colors={colors} />
            <SettingRow icon={Type} title="Text Size" value={settings.textSize} onPress={() => setModalVisible('textSize')} colors={colors} />
            <SettingRow icon={LayoutGrid} title="Layout" onPress={() => setModalVisible('layout')} colors={colors} />
          </SettingSection>

          <SettingSection title="Experience" colors={colors}>
            <SwitchRow icon={Vibrate} title="Haptic Feedback" value={settings.accessibility.hapticFeedback} onValueChange={(v: boolean) => handleUpdateSettings({ accessibility: { ...settings.accessibility, hapticFeedback: v } })} colors={colors} />
            <SwitchRow icon={Volume2} title="Sound Effects" value={settings.accessibility.soundEnabled} onValueChange={(v: boolean) => handleUpdateSettings({ accessibility: { ...settings.accessibility, soundEnabled: v } })} colors={colors} />
            <SwitchRow icon={Eye} title="High Contrast" value={settings.accessibility.highContrast} onValueChange={(v: boolean) => handleUpdateSettings({ accessibility: { ...settings.accessibility, highContrast: v } })} colors={colors} />
          </SettingSection>

          <SettingSection title="AI & Camera" colors={colors}>
            <SettingRow icon={Sliders} title="AI Settings" subtitle={`Confidence: ${settings.identification.confidenceThreshold}%`} onPress={() => setModalVisible('ai')} colors={colors} />
            <SwitchRow icon={Camera} title="Save Originals to Photos" value={settings.camera.saveOriginals} onValueChange={(v: boolean) => handleUpdateSettings({ camera: { ...settings.camera, saveOriginals: v } })} colors={colors} />
          </SettingSection>

          <SettingSection title="Privacy & Data" colors={colors}>
            <SwitchRow icon={Globe} title="Location Sharing" value={settings.privacy.shareLocation} onValueChange={(v: boolean) => handleUpdateSettings({ privacy: { ...settings.privacy, shareLocation: v } })} colors={colors} />
            <SwitchRow icon={User} title="Public Profile" value={settings.privacy.publicProfile} onValueChange={(v: boolean) => handleUpdateSettings({ privacy: { ...settings.privacy, publicProfile: v } })} colors={colors} />
            <SwitchRow icon={Shield} title="Usage Analytics" subtitle="Help improve the app" value={settings.privacy.dataCollection} onValueChange={(v: boolean) => handleUpdateSettings({ privacy: { ...settings.privacy, dataCollection: v } })} colors={colors} />
            <SettingRow icon={Download} title="Export Data" onPress={() => {}} colors={colors} />
            <SettingRow icon={Trash2} title="Clear Cache" subtitle={storageInfo ? `${dataManager.formatBytes(storageInfo.cacheSize)} used` : '...'} onPress={handleClearCache} colors={colors} />
            <SettingRow icon={RefreshCw} title="Reset All Data" onPress={handleResetData} colors={colors} isDestructive />
          </SettingSection>
          
          <SettingSection title="About & Support" colors={colors}>
            <SettingRow icon={HelpCircle} title="Help Center" onPress={() => {}} colors={colors} />
            <SettingRow icon={MessageSquare} title="Send Feedback" onPress={() => {}} colors={colors} />
            <SettingRow icon={Mail} title="Contact Support" onPress={() => {}} colors={colors} />
            <SettingRow icon={Info} title="Version" value="1.0.0" colors={colors} />
            <SettingRow icon={ExternalLink} title="Privacy Policy" onPress={() => {}} colors={colors} />
            <SettingRow icon={ExternalLink} title="Terms of Service" onPress={() => {}} colors={colors} />
          </SettingSection>
        </ScrollView>

        // In settings.tsx - Line 233
 <ThemeSelectionModal 
  visible={modalVisible === 'theme'}  onClose={() => setModalVisible(null)}  onSelectTheme={(theme) => handleUpdateSettings({ theme: theme as "auto" | "dark" | "light" })}  currentTheme={settings.theme} 
      />
        <LargeTextModal visible={modalVisible === 'textSize'} onClose={() => setModalVisible(null)} onSelectSize={(size) => handleUpdateSettings({ textSize: size })} currentTextSize={settings.textSize} />
        <LayoutSettingsModal visible={modalVisible === 'layout'} onClose={() => setModalVisible(null)} />
        <SubscriptionModal visible={modalVisible === 'subscription'} onClose={() => setModalVisible(null)} />
        <SettingsPopup
            visible={modalVisible === 'ai'}
            onClose={() => setModalVisible(null)}
            initialConfidence={settings.identification.confidenceThreshold}
            initialQuality={settings.camera.imageQuality}
            onApply={({ confidence, quality }) => {
                handleUpdateSettings({
                    identification: { ...settings.identification, confidenceThreshold: confidence },
                    camera: { ...settings.camera, imageQuality: quality },
                });
            }}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  gradient: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', gap: 16 },
  loadingText: { fontSize: 16, fontFamily: 'Inter-Regular' },
  header: { paddingHorizontal: 20, paddingTop: 20, paddingBottom: 10 },
  title: { fontSize: 32, fontFamily: 'Inter-Bold' },
  scrollContent: { paddingHorizontal: 20, paddingBottom: 40 },
  subscriptionCard: { borderRadius: 20, overflow: 'hidden', shadowOffset: { width: 0, height: 8 }, shadowOpacity: 0.15, shadowRadius: 20, elevation: 12, marginBottom: 24 },
  subscriptionGradient: { padding: 24, position: 'relative' },
  subscriptionHeader: { flexDirection: 'row', alignItems: 'center', gap: 8, backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 20, alignSelf: 'flex-start' },
  subscriptionBadgeText: { fontSize: 12, fontFamily: 'Inter-Bold', color: '#FFFFFF' },
  subscriptionTitle: { fontSize: 22, fontFamily: 'Inter-Bold', color: '#FFFFFF', marginTop: 16 },
  subscriptionChevron: { position: 'absolute', right: 24, top: '50%' },
  section: { marginBottom: 24 },
  sectionTitle: { fontSize: 16, fontFamily: 'Inter-SemiBold', marginBottom: 12, paddingHorizontal: 4, textTransform: 'uppercase', letterSpacing: 0.5 },
  sectionContent: { borderRadius: 16, overflow: 'hidden', borderWidth: 1, borderColor: 'rgba(0,0,0,0.05)' },
  settingRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 14, borderBottomWidth: 1 },
  settingLeft: { flex: 1, flexDirection: 'row', alignItems: 'center', gap: 16 },
  settingIcon: { width: 36, height: 36, borderRadius: 10, alignItems: 'center', justifyContent: 'center' },
  settingText: { flex: 1 },
  settingTitle: { fontSize: 16, fontFamily: 'Inter-Medium' },
  settingSubtitle: { fontSize: 13, fontFamily: 'Inter-Regular', lineHeight: 18, paddingTop: 2 },
  settingRight: { flexDirection: 'row', alignItems: 'center', gap: 8 },
  settingValue: { fontSize: 15, fontFamily: 'Inter-Regular', textTransform: 'capitalize' },
});
