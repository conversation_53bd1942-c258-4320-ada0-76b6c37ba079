import Purchases, {
  CustomerInfo,
  LOG_LEVEL,
  PurchasesOffering,
  PurchasesPackage
} from 'react-native-purchases';
import {
  REVENUECAT_CONFIG,
  getRevenueCatInitConfig,
  validateRevenueCatConfig
} from './RevenueCatConfig';

export interface RevenueCatCustomerInfo {
  isActive: boolean;
  tier: 'monthly' | 'yearly' | null;
  expirationDate: Date | null;
  isTrialActive: boolean;
  trialEndDate: Date | null;
  features: string[];
  originalPurchaseDate: Date | null;
  latestPurchaseDate: Date | null;
}

export class RevenueCatService {
  private static instance: RevenueCatService;
  private isInitialized = false;
  private currentOffering: PurchasesOffering | null = null;

  static getInstance(): RevenueCatService {
    if (!RevenueCatService.instance) {
      RevenueCatService.instance = new RevenueCatService();
    }
    return RevenueCatService.instance;
  }

  /**
   * Initialize RevenueCat SDK
   */
  async initialize(): Promise<boolean> {
    try {
      // Validate configuration
      if (!validateRevenueCatConfig()) {
        console.warn('⚠️ RevenueCat not properly configured. Using mock mode.');
        return false;
      }

      // Set log level
      if (REVENUECAT_CONFIG.options.enableDebugLogging) {
        Purchases.setLogLevel(LOG_LEVEL.VERBOSE);
      }

      // Configure RevenueCat
      const config = getRevenueCatInitConfig();
      Purchases.configure(config);

      console.log('✅ RevenueCat initialized successfully');
      this.isInitialized = true;

      // Load current offering
      await this.loadOfferings();

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize RevenueCat:', error);
      return false;
    }
  }

  /**
   * Load available offerings from RevenueCat
   */
  async loadOfferings(): Promise<PurchasesOffering | null> {
    try {
      const offerings = await Purchases.getOfferings();
      this.currentOffering = offerings.current;
      
      if (!this.currentOffering) {
        console.warn('⚠️ No current offering found in RevenueCat');
      }
      
      return this.currentOffering;
    } catch (error) {
      console.error('❌ Failed to load offerings:', error);
      return null;
    }
  }

  /**
   * Get customer info and parse subscription status
   */
  async getCustomerInfo(): Promise<RevenueCatCustomerInfo> {
    try {
      const customerInfo = await Purchases.getCustomerInfo();
      return this.parseCustomerInfo(customerInfo);
    } catch (error) {
      console.error('❌ Failed to get customer info:', error);
      return this.getDefaultCustomerInfo();
    }
  }

  /**
   * Parse RevenueCat CustomerInfo into our format
   */
  private parseCustomerInfo(customerInfo: CustomerInfo): RevenueCatCustomerInfo {
    const premiumEntitlement = customerInfo.entitlements.active[REVENUECAT_CONFIG.entitlements.premium];
    
    if (!premiumEntitlement) {
      return this.getDefaultCustomerInfo();
    }

    const isActive = premiumEntitlement.isActive;
    const expirationDate = premiumEntitlement.expirationDate ? new Date(premiumEntitlement.expirationDate) : null;
    const originalPurchaseDate = premiumEntitlement.originalPurchaseDate ? new Date(premiumEntitlement.originalPurchaseDate) : null;
    const latestPurchaseDate = premiumEntitlement.latestPurchaseDate ? new Date(premiumEntitlement.latestPurchaseDate) : null;

    // Determine tier based on product identifier
    let tier: 'monthly' | 'yearly' | null = null;
    if (premiumEntitlement.productIdentifier === REVENUECAT_CONFIG.products.monthly) {
      tier = 'monthly';
    } else if (premiumEntitlement.productIdentifier === REVENUECAT_CONFIG.products.yearly) {
      tier = 'yearly';
    }

    // Check if in trial period
    const isTrialActive = premiumEntitlement.willRenew && premiumEntitlement.periodType === 'trial';
    const trialEndDate = isTrialActive ? expirationDate : null;

    // Get features based on tier
    const features = this.getFeaturesForTier(tier);

    return {
      isActive,
      tier,
      expirationDate,
      isTrialActive,
      trialEndDate,
      features,
      originalPurchaseDate,
      latestPurchaseDate,
    };
  }

  /**
   * Get default customer info for non-subscribers
   */
  private getDefaultCustomerInfo(): RevenueCatCustomerInfo {
    return {
      isActive: false,
      tier: null,
      expirationDate: null,
      isTrialActive: false,
      trialEndDate: null,
      features: [],
      originalPurchaseDate: null,
      latestPurchaseDate: null,
    };
  }

  /**
   * Get features for a subscription tier
   */
  private getFeaturesForTier(tier: 'monthly' | 'yearly' | null): string[] {
    if (!tier) return [];

    const monthlyFeatures = [
      'unlimited_scans',
      'advanced_ai',
      'full_history',
      'offline_mode',
      'priority_support',
      'no_ads',
      'export_data',
      'cloud_sync'
    ];

    const yearlyFeatures = [
      ...monthlyFeatures,
      'extended_offline',
      'advanced_analytics',
      'custom_collections',
      'early_features'
    ];

    return tier === 'yearly' ? yearlyFeatures : monthlyFeatures;
  }

  /**
   * Purchase a subscription package
   */
  async purchasePackage(packageToPurchase: PurchasesPackage): Promise<{ success: boolean; customerInfo?: RevenueCatCustomerInfo; error?: string }> {
    try {
      const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
      const parsedInfo = this.parseCustomerInfo(customerInfo);
      
      return {
        success: true,
        customerInfo: parsedInfo,
      };
    } catch (error: any) {
      console.error('❌ Purchase failed:', error);
      
      // Handle user cancellation
      if (error.userCancelled) {
        return {
          success: false,
          error: 'Purchase was cancelled',
        };
      }
      
      return {
        success: false,
        error: error.message || 'Purchase failed',
      };
    }
  }

  /**
   * Restore purchases
   */
  async restorePurchases(): Promise<{ success: boolean; customerInfo?: RevenueCatCustomerInfo; error?: string }> {
    try {
      const customerInfo = await Purchases.restorePurchases();
      const parsedInfo = this.parseCustomerInfo(customerInfo);
      
      return {
        success: true,
        customerInfo: parsedInfo,
      };
    } catch (error: any) {
      console.error('❌ Restore failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to restore purchases',
      };
    }
  }

  /**
   * Get available packages for purchase
   */
  async getAvailablePackages(): Promise<PurchasesPackage[]> {
    if (!this.currentOffering) {
      await this.loadOfferings();
    }
    
    return this.currentOffering?.availablePackages || [];
  }

  /**
   * Get specific package by identifier
   */
  async getPackageByIdentifier(identifier: string): Promise<PurchasesPackage | null> {
    const packages = await this.getAvailablePackages();
    return packages.find(pkg => pkg.identifier === identifier) || null;
  }

  /**
   * Check if RevenueCat is properly initialized
   */
  isRevenueCatInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Set user ID for RevenueCat (optional)
   */
  async setUserId(userId: string): Promise<void> {
    try {
      await Purchases.logIn(userId);
      console.log('✅ User logged in to RevenueCat:', userId);
    } catch (error) {
      console.error('❌ Failed to set user ID:', error);
    }
  }

  /**
   * Log out user from RevenueCat
   */
  async logOut(): Promise<void> {
    try {
      await Purchases.logOut();
      console.log('✅ User logged out from RevenueCat');
    } catch (error) {
      console.error('❌ Failed to log out user:', error);
    }
  }
}

export default RevenueCatService;
