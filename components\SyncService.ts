import firestore from '@react-native-firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthService from './AuthService';
import { WidgetData } from './WidgetDataManager';

export interface SyncData {
  userId: string;
  deviceId: string;
  lastSyncTime: Date;
  widgetData: WidgetData;
  notificationSettings: NotificationSettings;
  appPreferences: AppPreferences;
  scanHistory: ScanHistoryItem[];
  todoTasks: TodoTask[];
}

export interface NotificationSettings {
  dailyScanReminder: boolean;
  scanReminderTime: { hour: number; minute: number };
  goalReminders: boolean;
  achievementNotifications: boolean;
  pushToken?: string;
}

export interface AppPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
  autoSync: boolean;
  offlineMode: boolean;
}

export interface ScanHistoryItem {
  id: string;
  species: string;
  confidence: number;
  imageUri: string;
  timestamp: Date;
  location?: { latitude: number; longitude: number };
  category: string;
}

export interface TodoTask {
  id: string;
  text: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  reminder?: Date;
  category: string;
  createdAt: Date;
  completedAt?: Date;
}

export class SyncService {
  private static instance: SyncService;
  private authService: AuthService;
  private isOnline = true;
  private syncInProgress = false;
  private lastSyncTime: Date | null = null;

  static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  constructor() {
    this.authService = AuthService.getInstance();
    this.initializeSync();
  }

  /**
   * Initialize sync service
   */
  private async initializeSync(): Promise<void> {
    try {
      // Listen to auth state changes
      this.authService.addAuthStateListener((user) => {
        if (user && !user.isAnonymous) {
          // User signed in - sync data
          this.syncFromCloud();
        } else if (!user) {
          // User signed out - clear synced data
          this.clearSyncedData();
        }
      });

      // Load last sync time
      const lastSync = await AsyncStorage.getItem('last_sync_time');
      if (lastSync) {
        this.lastSyncTime = new Date(lastSync);
      }

      console.log('✅ SyncService initialized');
    } catch (error) {
      console.error('❌ Failed to initialize SyncService:', error);
    }
  }

  /**
   * Sync data to cloud
   */
  async syncToCloud(): Promise<{ success: boolean; error?: string }> {
    try {
      const user = this.authService.getCurrentUser();
      if (!user || user.isAnonymous) {
        return { success: false, error: 'User not signed in' };
      }

      if (this.syncInProgress) {
        return { success: false, error: 'Sync already in progress' };
      }

      this.syncInProgress = true;

      // Collect data to sync
      const syncData = await this.collectLocalData(user.uid);
      
      // Upload to Firestore
      await firestore()
        .collection('user_data')
        .doc(user.uid)
        .set(syncData, { merge: true });

      // Update last sync time
      this.lastSyncTime = new Date();
      await AsyncStorage.setItem('last_sync_time', this.lastSyncTime.toISOString());

      console.log('✅ Data synced to cloud successfully');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Failed to sync to cloud:', error);
      return { success: false, error: error.message };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync data from cloud
   */
  async syncFromCloud(): Promise<{ success: boolean; error?: string }> {
    try {
      const user = this.authService.getCurrentUser();
      if (!user || user.isAnonymous) {
        return { success: false, error: 'User not signed in' };
      }

      if (this.syncInProgress) {
        return { success: false, error: 'Sync already in progress' };
      }

      this.syncInProgress = true;

      // Get data from Firestore
      const doc = await firestore()
        .collection('user_data')
        .doc(user.uid)
        .get();

      if (!doc.exists) {
        // No cloud data - sync local data to cloud
        return await this.syncToCloud();
      }

      const cloudData = doc.data() as SyncData;
      
      // Merge cloud data with local data
      await this.mergeCloudData(cloudData);

      // Update last sync time
      this.lastSyncTime = new Date();
      await AsyncStorage.setItem('last_sync_time', this.lastSyncTime.toISOString());

      console.log('✅ Data synced from cloud successfully');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Failed to sync from cloud:', error);
      return { success: false, error: error.message };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Collect local data for syncing
   */
  private async collectLocalData(userId: string): Promise<SyncData> {
    const deviceId = await this.getDeviceId();
    
    // Get widget data
    const widgetDataStr = await AsyncStorage.getItem('widget_data');
    const widgetData = widgetDataStr ? JSON.parse(widgetDataStr) : {};

    // Get notification settings
    const notificationSettingsStr = await AsyncStorage.getItem('notification_settings');
    const notificationSettings = notificationSettingsStr ? JSON.parse(notificationSettingsStr) : {
      dailyScanReminder: true,
      scanReminderTime: { hour: 10, minute: 0 },
      goalReminders: true,
      achievementNotifications: true,
    };

    // Get app preferences
    const appPreferencesStr = await AsyncStorage.getItem('app_preferences');
    const appPreferences = appPreferencesStr ? JSON.parse(appPreferencesStr) : {
      theme: 'auto',
      language: 'en',
      units: 'metric',
      autoSync: true,
      offlineMode: false,
    };

    // Get scan history
    const scanHistoryStr = await AsyncStorage.getItem('scan_history');
    const scanHistory = scanHistoryStr ? JSON.parse(scanHistoryStr) : [];

    // Get todo tasks
    const todoTasksStr = await AsyncStorage.getItem('todo_tasks');
    const todoTasks = todoTasksStr ? JSON.parse(todoTasksStr) : [];

    return {
      userId,
      deviceId,
      lastSyncTime: new Date(),
      widgetData,
      notificationSettings,
      appPreferences,
      scanHistory,
      todoTasks,
    };
  }

  /**
   * Merge cloud data with local data
   */
  private async mergeCloudData(cloudData: SyncData): Promise<void> {
    try {
      // Merge widget data (keep most recent)
      if (cloudData.widgetData) {
        const localWidgetStr = await AsyncStorage.getItem('widget_data');
        const localWidget = localWidgetStr ? JSON.parse(localWidgetStr) : {};
        
        const cloudLastUpdated = new Date(cloudData.widgetData.lastUpdated);
        const localLastUpdated = localWidget.lastUpdated ? new Date(localWidget.lastUpdated) : new Date(0);
        
        if (cloudLastUpdated > localLastUpdated) {
          await AsyncStorage.setItem('widget_data', JSON.stringify(cloudData.widgetData));
        }
      }

      // Merge notification settings
      if (cloudData.notificationSettings) {
        await AsyncStorage.setItem('notification_settings', JSON.stringify(cloudData.notificationSettings));
      }

      // Merge app preferences
      if (cloudData.appPreferences) {
        await AsyncStorage.setItem('app_preferences', JSON.stringify(cloudData.appPreferences));
      }

      // Merge scan history (combine and deduplicate)
      if (cloudData.scanHistory) {
        const localHistoryStr = await AsyncStorage.getItem('scan_history');
        const localHistory = localHistoryStr ? JSON.parse(localHistoryStr) : [];
        
        const mergedHistory = this.mergeArrays(localHistory, cloudData.scanHistory, 'id');
        await AsyncStorage.setItem('scan_history', JSON.stringify(mergedHistory));
      }

      // Merge todo tasks (combine and deduplicate)
      if (cloudData.todoTasks) {
        const localTasksStr = await AsyncStorage.getItem('todo_tasks');
        const localTasks = localTasksStr ? JSON.parse(localTasksStr) : [];
        
        const mergedTasks = this.mergeArrays(localTasks, cloudData.todoTasks, 'id');
        await AsyncStorage.setItem('todo_tasks', JSON.stringify(mergedTasks));
      }
    } catch (error) {
      console.error('Error merging cloud data:', error);
    }
  }

  /**
   * Merge two arrays and deduplicate by key
   */
  private mergeArrays(local: any[], cloud: any[], key: string): any[] {
    const merged = [...local];
    
    cloud.forEach(cloudItem => {
      const existingIndex = merged.findIndex(localItem => localItem[key] === cloudItem[key]);
      
      if (existingIndex >= 0) {
        // Item exists - keep the most recent one
        const localItem = merged[existingIndex];
        const cloudUpdated = new Date(cloudItem.updatedAt || cloudItem.createdAt || 0);
        const localUpdated = new Date(localItem.updatedAt || localItem.createdAt || 0);
        
        if (cloudUpdated > localUpdated) {
          merged[existingIndex] = cloudItem;
        }
      } else {
        // New item from cloud
        merged.push(cloudItem);
      }
    });
    
    return merged;
  }

  /**
   * Get device ID
   */
  private async getDeviceId(): Promise<string> {
    let deviceId = await AsyncStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await AsyncStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }

  /**
   * Clear synced data (on sign out)
   */
  private async clearSyncedData(): Promise<void> {
    try {
      this.lastSyncTime = null;
      await AsyncStorage.removeItem('last_sync_time');
      console.log('✅ Synced data cleared');
    } catch (error) {
      console.error('Error clearing synced data:', error);
    }
  }

  /**
   * Check if sync is needed
   */
  shouldSync(): boolean {
    if (!this.authService.isSignedIn() || this.authService.isAnonymous()) {
      return false;
    }

    if (!this.lastSyncTime) {
      return true;
    }

    // Sync every 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return this.lastSyncTime < fiveMinutesAgo;
  }

  /**
   * Get last sync time
   */
  getLastSyncTime(): Date | null {
    return this.lastSyncTime;
  }

  /**
   * Force sync now
   */
  async forcSync(): Promise<{ success: boolean; error?: string }> {
    return await this.syncToCloud();
  }
}

export default SyncService;
