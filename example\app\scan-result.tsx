/* ScanResultScreen.tsx - Complete Implementation */
import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Image,
  Animated,
  Share,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useLocalSearchParams } from 'expo-router';
import {
  ArrowLeft,
  Share2,
  Heart,
  BookOpen,
  Ruler,
  Droplet,
  Calendar,
  Leaf,
  Zap,
  RotateCcw,
  Star,
  AlertCircle,
  Flame,
  Beef,
  Wheat,
  GlassWater,
  Mountain,
  CircleDollarSign,
  Tag,
  Cpu,
  Shirt,
  Book,
  Syringe,
  Hammer,
  Info,
  Wrench,
  ShieldCheck,
  Link,
  TrendingUp,
  LeafyGreen,
  ThumbsUp,
  ThumbsDown,
  ShoppingBag,
  Package,
  BatteryCharging,
  Award,
  CalendarDays,
  Palette,
  Scale,
  Recycle,
  Users,
  FlaskConical,
  Pill,
  Warehouse,
  GraduationCap,
  ChevronDown,
  ChevronUp,
  Copy,
  ExternalLink,
  Edit3,
  Plus,
  Minus,
  Check,
  X,
  Target,
  Activity,
  Shield,
  Clock,
  ChefHat,
  Utensils,
} from 'lucide-react-native';
import { useTheme } from '@/components/ThemeContext';
// Ensure all necessary types are imported from GeminiService
import { IdentificationResult, Category, NutritionalInfo, FoodAnalysis, Ingredient, PhysicalTraits } from '@/components/GeminiService';
import { OptimizedImage } from '@/components/LazyLoad';
import { useTextScaling } from '@/components/TextScalingContext';
import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';
// In scan-result.tsx, line 14, change the import:
import { addScanToHistory, ScanHistoryItemType } from './(tabs)/history';
const { width } = Dimensions.get('window');

// Helper to get icon for category
const getCategoryIcon = (category: Category) => {
  switch (category) {
    case 'Animal': return Beef;
    case 'Plant': return Leaf;
    case 'Food': return Utensils;
    case 'Insect': return Leaf; // Using Leaf as fallback
    case 'Rock / Mineral': return Mountain;
    case 'Coin / Currency': return CircleDollarSign;
    case 'Wine': return GlassWater;
    case 'Product': return Package;
    case 'Electronics': return Cpu;
    case 'Clothing': return Shirt;
    case 'Book': return Book;
    case 'Medicine': return Pill;
    case 'Tool': return Hammer;
    case 'Art': return Palette;
    default: return Info;
  }
};

// Helper to get health score color
const getHealthScoreColor = (score: number, colors: any) => {
  if (score >= 8) return colors.success;
  if (score >= 6) return '#F59E0B'; // Yellow
  if (score >= 4) return '#F97316'; // Orange
  return colors.error;
};

export default function ScanResultScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { getScaledFontSize } = useTextScaling();
  const params = useLocalSearchParams();
  const hapticService = HapticService.getInstance();
  const notificationService = NotificationService.getInstance();

  // Parse the result data from params
  const result: IdentificationResult | null = params.result
    ? JSON.parse(params.result as string)
    : null;
  const imageData: string | null = params.imageData as string || null;

  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [isIngredientsExpanded, setIsIngredientsExpanded] = useState(false);
  const [isHealthInsightsExpanded, setIsHealthInsightsExpanded] = useState(false);
  const [isAlternativesExpanded, setIsAlternativesExpanded] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [servings, setServings] = useState(1);
  const [customIngredients, setCustomIngredients] = useState<Ingredient[]>([]);

  // Fallback for when result is null
  if (!result) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Scan Result</Text>
          <View style={styles.headerSpacer} />
        </View>
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.text }]}>No scan result found.</Text>
          <Text style={[styles.errorSubText, { color: colors.textSecondary }]}>
            Please go back and try scanning again.
          </Text>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={() => router.replace('/camera')}
          >
            <Text style={[styles.primaryButtonText, { color: colors.buttonText }]}>Go to Camera</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Handle sharing the result
  const onShare = async () => {
    hapticService.triggerSelectionFeedback();
    try {
      let shareContent = `Check out what I scanned with Apex! 🌿\n\n${result.name}`;
      
      if (result.category === 'Food' && result.nutritionalInfo) {
        shareContent += `\n📊 ${result.nutritionalInfo.totalCalories} calories`;
        shareContent += `\n🥩 ${result.nutritionalInfo.protein}g protein`;
        shareContent += `\n🌾 ${result.nutritionalInfo.carbs}g carbs`;
        shareContent += `\n🥑 ${result.nutritionalInfo.fat}g fat`;
        if (result.nutritionalInfo.healthMetrics) {
          shareContent += `\n❤️ Health Score: ${result.nutritionalInfo.healthMetrics.healthScore}/10`;
        }
      }
      
      shareContent += `\n\n#ApexApp #FoodTracking #NutritionAnalysis`;
      
      await Share.share({
        message: shareContent,
        url: imageData || undefined,
      });
    } catch (error: any) {
      console.error('Error sharing:', error.message);
      notificationService.sendNotification({
        title: 'Share Failed',
        body: 'Could not share the scan result.',
      });
    }
  };

  // Function to render a collapsible section
  const renderCollapsibleSection = (
    title: string,
    content: React.ReactNode,
    isExpanded: boolean,
    toggleExpansion: () => void,
    icon: React.ElementType,
    iconColor: string
  ) => (
    <View style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <TouchableOpacity onPress={toggleExpansion} style={styles.collapsibleHeader}>
        <View style={styles.collapsibleTitleContainer}>
          {icon && (
            <View style={[styles.collapsibleIconBg, {backgroundColor: iconColor + '20'}]}>
              {React.createElement(icon, { size: 20, color: iconColor })}
            </View>
          )}
          <Text style={[styles.collapsibleTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>{title}</Text>
        </View>
        {isExpanded ? (
          <ChevronUp size={20} color={colors.textSecondary} />
        ) : (
          <ChevronDown size={20} color={colors.textSecondary} />
        )}
      </TouchableOpacity>
      {isExpanded && <View style={styles.collapsibleContent}>{content}</View>}
    </View>
  );

  const renderBulletList = (items: string[]) => (
    <View style={styles.bulletList}>
      {items.map((item, index) => (
        <View key={index} style={styles.bulletItem}>
          <Text style={[styles.bulletIcon, { color: colors.text }]}>•</Text>
          <Text style={[styles.bulletText, { color: colors.text, fontSize: getScaledFontSize(14) }]}>{item}</Text>
        </View>
      ))}
    </View>
  );

  // Enhanced Food Nutrition Display
  const renderFoodNutrition = (nutritionalInfo: NutritionalInfo) => {
    const adjustedCalories = Math.round(nutritionalInfo.totalCalories * servings);
    const adjustedProtein = Math.round(nutritionalInfo.protein * servings * 10) / 10;
    const adjustedCarbs = Math.round(nutritionalInfo.carbs * servings * 10) / 10;
    const adjustedFat = Math.round(nutritionalInfo.fat * servings * 10) / 10;

    return (
      <View style={[styles.nutritionCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        {/* Servings Control */}
        <View style={styles.servingsControl}>
          <Text style={[styles.servingsLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(14) }]}>
            Servings
          </Text>
          <View style={styles.servingsCounter}>
            <TouchableOpacity
              onPress={() => servings > 0.5 && setServings(servings - 0.5)}
              style={[styles.servingsButton, { borderColor: colors.border }]}
            >
              <Minus size={16} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.servingsValue, { color: colors.text, fontSize: getScaledFontSize(18) }]}>
              {servings}
            </Text>
            <TouchableOpacity
              onPress={() => setServings(servings + 0.5)}
              style={[styles.servingsButton, { borderColor: colors.border }]}
            >
              <Plus size={16} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Main Calorie Display */}
        <View style={styles.calorieDisplay}>
          <View style={styles.calorieMainContainer}>
            <View style={[styles.calorieIconBg, { backgroundColor: colors.accent + '20' }]}>
              <Flame size={24} color={colors.accent} />
            </View>
            <Text style={[styles.calorieNumber, { color: colors.text, fontSize: getScaledFontSize(36) }]}>
              {adjustedCalories}
            </Text>
            <Text style={[styles.calorieLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(16) }]}>
              Calories
            </Text>
          </View>
        </View>

        {/* Macronutrients */}
        <View style={styles.macroContainer}>
          <View style={styles.macroRow}>
            <View style={styles.macroItem}>
              <View style={[styles.macroIconBg, { backgroundColor: colors.primary + '20' }]}>
                <Beef size={20} color={colors.primary} />
              </View>
              <Text style={[styles.macroValue, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
                {adjustedProtein}g
              </Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Protein
              </Text>
            </View>
            <View style={styles.macroItem}>
              <View style={[styles.macroIconBg, { backgroundColor: '#F59E0B20' }]}>
                <Wheat size={20} color="#F59E0B" />
              </View>
              <Text style={[styles.macroValue, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
                {adjustedCarbs}g
              </Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Carbs
              </Text>
            </View>
            <View style={styles.macroItem}>
              <View style={[styles.macroIconBg, { backgroundColor: colors.error + '20' }]}>
                <Droplet size={20} color={colors.error} />
              </View>
              <Text style={[styles.macroValue, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
                {adjustedFat}g
              </Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Fats
              </Text>
            </View>
          </View>
        </View>

        {/* Health Score */}
        {nutritionalInfo.healthMetrics && (
          <View style={styles.healthScoreContainer}>
            <View style={styles.healthScoreRow}>
              <View style={styles.healthScoreLeft}>
                <Heart size={20} color={getHealthScoreColor(nutritionalInfo.healthMetrics.healthScore, colors)} />
                <Text style={[styles.healthScoreLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(14) }]}>
                  Health Score
                </Text>
              </View>
              <View style={styles.healthScoreRight}>
                <Text style={[styles.healthScoreValue, { 
                  color: getHealthScoreColor(nutritionalInfo.healthMetrics.healthScore, colors),
                  fontSize: getScaledFontSize(24) 
                }]}>
                  {nutritionalInfo.healthMetrics.healthScore}
                </Text>
                <Text style={[styles.healthScoreMax, { color: colors.textSecondary, fontSize: getScaledFontSize(16) }]}>
                  /10
                </Text>
              </View>
            </View>
            <View style={styles.healthScoreBar}>
              <View 
                style={[
                  styles.healthScoreProgress, 
                  { 
                    backgroundColor: getHealthScoreColor(nutritionalInfo.healthMetrics.healthScore, colors),
                    width: `${(nutritionalInfo.healthMetrics.healthScore / 10) * 100}%`
                  }
                ]} 
              />
            </View>
          </View>
        )}

        {/* Allergens Warning */}
        {nutritionalInfo.allergens && nutritionalInfo.allergens.length > 0 && (
          <View style={[styles.allergensContainer, { backgroundColor: colors.error + '10' }]}>
            <AlertCircle size={16} color={colors.error} />
            <Text style={[styles.allergensText, { color: colors.error, fontSize: getScaledFontSize(14) }]}>
              Allergens: {nutritionalInfo.allergens.join(', ')}
            </Text>
          </View>
        )}
      </View>
    );
  };

  // Enhanced Ingredients Display
  const renderIngredients = (ingredients: Ingredient[]) => (
    <View style={styles.ingredientsContainer}>
      <View style={styles.ingredientsHeader}>
        <Text style={[styles.ingredientsTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
          Ingredients Breakdown
        </Text>
        <TouchableOpacity onPress={() => setIsEditModalVisible(true)}>
          <Edit3 size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
      {ingredients.map((ingredient, index) => (
        <View key={index} style={[styles.ingredientRow, { borderBottomColor: colors.border }]}>
          <View style={styles.ingredientLeft}>
            <Text style={[styles.ingredientName, { color: colors.text, fontSize: getScaledFontSize(14) }]}>
              {ingredient.name}
            </Text>
            <Text style={[styles.ingredientAmount, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
              {ingredient.quantity} {ingredient.unit}
            </Text>
          </View>
          <View style={styles.ingredientRight}>
            <Text style={[styles.ingredientCalories, { color: colors.text, fontSize: getScaledFontSize(14) }]}>
              {Math.round(ingredient.calories * servings)} cal
            </Text>
            <View style={styles.ingredientMacros}>
              <Text style={[styles.ingredientMacro, { color: colors.primary, fontSize: getScaledFontSize(10) }]}>
                P: {Math.round(ingredient.protein * servings * 10) / 10}g
              </Text>
              <Text style={[styles.ingredientMacro, { color: '#F59E0B', fontSize: getScaledFontSize(10) }]}>
                C: {Math.round(ingredient.carbs * servings * 10) / 10}g
              </Text>
              <Text style={[styles.ingredientMacro, { color: colors.error, fontSize: getScaledFontSize(10) }]}>
                F: {Math.round(ingredient.fat * servings * 10) / 10}g
              </Text>
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  // Food Analysis Display
  const renderFoodAnalysis = (foodAnalysis: FoodAnalysis) => (
    <View style={styles.foodAnalysisContainer}>
      {/* Meal Info */}
      <View style={styles.mealInfoRow}>
        {foodAnalysis.cuisine && (
          <View style={[styles.mealTag, { backgroundColor: colors.primary + '20' }]}>
            <Text style={[styles.mealTagText, { color: colors.primary, fontSize: getScaledFontSize(12) }]}>
              {foodAnalysis.cuisine}
            </Text>
          </View>
        )}
        {foodAnalysis.mealType && (
          <View style={[styles.mealTag, { backgroundColor: colors.accent + '20' }]}>
            <Text style={[styles.mealTagText, { color: colors.accent, fontSize: getScaledFontSize(12) }]}>
              {foodAnalysis.mealType}
            </Text>
          </View>
        )}
        {foodAnalysis.difficulty && (
          <View style={[styles.mealTag, { backgroundColor: colors.secondary + '20' }]}>
            <ChefHat size={12} color={colors.secondary} />
            <Text style={[styles.mealTagText, { color: colors.secondary, fontSize: getScaledFontSize(12) }]}>
              {foodAnalysis.difficulty}
            </Text>
          </View>
        )}
      </View>

      {/* Cooking Info */}
      {(foodAnalysis.preparationMethod || foodAnalysis.estimatedCookingTime) && (
        <View style={styles.cookingInfoRow}>
          {foodAnalysis.preparationMethod && (
            <View style={styles.cookingInfoItem}>
              <Utensils size={16} color={colors.textSecondary} />
              <Text style={[styles.cookingInfoText, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
                {foodAnalysis.preparationMethod}
              </Text>
            </View>
          )}
          {foodAnalysis.estimatedCookingTime && (
            <View style={styles.cookingInfoItem}>
              <Clock size={16} color={colors.textSecondary} />
              <Text style={[styles.cookingInfoText, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
                {foodAnalysis.estimatedCookingTime}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Calorie Breakdown Chart */}
      {foodAnalysis.calorieBreakdown && (
        <View style={styles.calorieBreakdownContainer}>
          <Text style={[styles.calorieBreakdownTitle, { color: colors.text, fontSize: getScaledFontSize(14) }]}>
            Calorie Sources
          </Text>
          <View style={styles.calorieBreakdownChart}>
            <View style={[
              styles.calorieBreakdownBar,
              { 
                backgroundColor: colors.primary,
                flex: foodAnalysis.calorieBreakdown.fromProtein
              }
            ]} />
            <View style={[
              styles.calorieBreakdownBar,
              { 
                backgroundColor: '#F59E0B',
                flex: foodAnalysis.calorieBreakdown.fromCarbs
              }
            ]} />
            <View style={[
              styles.calorieBreakdownBar,
              { 
                backgroundColor: colors.error,
                flex: foodAnalysis.calorieBreakdown.fromFats
              }
            ]} />
          </View>
          <View style={styles.calorieBreakdownLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Protein {foodAnalysis.calorieBreakdown.fromProtein}%
              </Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: '#F59E0B' }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Carbs {foodAnalysis.calorieBreakdown.fromCarbs}%
              </Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: colors.error }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                Fats {foodAnalysis.calorieBreakdown.fromFats}%
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
          {result.category === 'Food' ? 'Nutrition' : 'Scan Result'}
        </Text>
        <TouchableOpacity onPress={onShare} style={styles.shareButton}>
          <Share2 size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Section */}
        {imageData && (
          <View style={styles.imageContainer}>
            <OptimizedImage
              source={{ uri: imageData }}
              style={styles.resultImage}
              resizeMode="cover"
              loadingIndicator={<ActivityIndicator size="large" color={colors.primary} />}
            />
            {/* Confidence Badge */}
            <View style={[styles.confidenceBadge, { backgroundColor: colors.primary }]}>
              <Text style={[styles.confidenceText, { color: colors.buttonText, fontSize: getScaledFontSize(14) }]}>
                {result.confidence}% Confidence
              </Text>
            </View>
          </View>
        )}

        {/* Main Identification Card */}
        <View style={[styles.mainInfoCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <View style={styles.mainInfoCategory}>
            <Text style={[styles.mainInfoCategoryText, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
              {result.category}
            </Text>
            <View style={[styles.categoryIconBg, {backgroundColor: colors.primary + '1A'}]}>
              {React.createElement(getCategoryIcon(result.category), { size: 16, color: colors.primary })}
            </View>
          </View>
          <Text style={[styles.mainInfoName, { color: colors.text, fontSize: getScaledFontSize(24) }]}>{result.name}</Text>
          {result.scientificName !== 'N/A' && (
            <Text style={[styles.mainInfoScientificName, { color: colors.textSecondary, fontSize: getScaledFontSize(16) }]}>
              {result.scientificName}
            </Text>
          )}
        </View>

        {/* Food-Specific Enhanced Display */}
        {result.category === 'Food' && result.nutritionalInfo && (
          <>
            {renderFoodNutrition(result.nutritionalInfo)}
            
            {/* Ingredients Section */}
            {result.nutritionalInfo.ingredients && result.nutritionalInfo.ingredients.length > 0 && 
              renderCollapsibleSection(
                'Ingredients',
                renderIngredients(result.nutritionalInfo.ingredients),
                isIngredientsExpanded,
                () => setIsIngredientsExpanded(!isIngredientsExpanded),
                Package, colors.primary
              )
            }

            {/* Food Analysis */}
            {result.foodAnalysis && 
              renderCollapsibleSection(
                'Food Analysis',
                renderFoodAnalysis(result.foodAnalysis),
                isHealthInsightsExpanded,
                () => setIsHealthInsightsExpanded(!isHealthInsightsExpanded),
                Activity, colors.accent
              )
            }

            {/* Health Benefits & Concerns */}
            {result.foodAnalysis && (result.foodAnalysis.healthBenefits?.length > 0 || result.foodAnalysis.potentialConcerns?.length > 0) &&
              renderCollapsibleSection(
                'Health Insights',
                <View> {/* Added a wrapper View here */}
                  {result.foodAnalysis.healthBenefits && result.foodAnalysis.healthBenefits.length > 0 && (
                    <View style={styles.healthSection}>
                      <View style={styles.healthSectionHeader}>
                        <ThumbsUp size={16} color={colors.success} />
                        <Text style={[styles.healthSectionTitle, { color: colors.success, fontSize: getScaledFontSize(14) }]}>
                          Health Benefits
                        </Text>
                      </View>
                      {renderBulletList(result.foodAnalysis.healthBenefits)}
                    </View>
                  )}
                  {result.foodAnalysis.potentialConcerns && result.foodAnalysis.potentialConcerns.length > 0 && (
                    <View style={styles.healthSection}>
                      <View style={styles.healthSectionHeader}>
                        <AlertCircle size={16} color={colors.error} />
                        <Text style={[styles.healthSectionTitle, { color: colors.error, fontSize: getScaledFontSize(14) }]}>
                          Potential Concerns
                        </Text>
                      </View>
                      {renderBulletList(result.foodAnalysis.potentialConcerns)}
                    </View>
                  )}
                </View>,
                isHealthInsightsExpanded,
                () => setIsHealthInsightsExpanded(!isHealthInsightsExpanded),
                Shield, colors.success
              )
            }

            {/* Healthier Alternatives */}
            {result.foodAnalysis?.healthierAlternatives && result.foodAnalysis.healthierAlternatives.length > 0 &&
              renderCollapsibleSection(
                'Healthier Alternatives',
                <View style={styles.alternativesContainer}>
                  {result.foodAnalysis.healthierAlternatives.map((alt, index) => (
                    <View key={index} style={[styles.alternativeItem, { borderColor: colors.border }]}>
                      <Text style={[styles.alternativeSuggestion, { color: colors.text, fontSize: getScaledFontSize(14) }]}>
                        {alt.suggestion}
                      </Text>
                      <Text style={[styles.alternativeBenefit, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
                        {alt.benefit}
                      </Text>
                      {alt.calorieReduction && (
                        <Text style={[styles.alternativeCalories, { color: colors.success, fontSize: getScaledFontSize(12) }]}>
                          -{alt.calorieReduction} calories
                        </Text>
                      )}
                    </View>
                  ))}
                </View>,
                isAlternativesExpanded,
                () => setIsAlternativesExpanded(!isAlternativesExpanded),
                Target, colors.success
              )
            }
          </>
        )}

        {/* Description Section for all items */}
        {renderCollapsibleSection(
          'Description',
          <Text style={[styles.descriptionText, { color: colors.text, fontSize: getScaledFontSize(14) }]}>{result.description}</Text>,
          isDescriptionExpanded,
          () => setIsDescriptionExpanded(!isDescriptionExpanded),
          BookOpen, colors.primary
        )}

        {/* Facts Section */}
        {result.facts && result.facts.length > 0 && renderCollapsibleSection(
          'Key Facts',
          renderBulletList(result.facts),
          false, // This section is not collapsible by default
          () => {}, // No toggle function needed
          Info, colors.accent
        )}

        {/* Action Section */}
        <View style={styles.actionSection}>
          {result.category === 'Food' && (
            <TouchableOpacity
              style={[styles.editButton, { borderColor: colors.primary }]}
              onPress={() => setIsEditModalVisible(true)}
            >
              <Edit3 size={20} color={colors.primary} />
              <Text style={[styles.editButtonText, { color: colors.primary, fontSize: getScaledFontSize(16) }]}>
                Fix Results
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              // Ensure all properties expected by ScanHistoryItemType are present and correctly typed
              const historyItem: ScanHistoryItemType = {
                id: new Date().toISOString(),
                date: new Date().toISOString(),
                location: 'Unknown', // You might want to get actual location data
                imageUri: imageData || '',
                name: result.name,
                scientificName: result.scientificName,
                confidence: result.confidence,
                category: result.category,
                description: result.description,
                habitat: result.habitat,
                conservationStatus: result.conservationStatus,
                facts: result.facts || [], // Ensure it's an array, even if empty
                physicalTraits: result.physicalTraits || {}, // Ensure it's an object, even if empty
                alternatives: result.alternatives || [], // Ensure it's an array, even if empty
                nutritionalInfo: result.nutritionalInfo,
                foodAnalysis: result.foodAnalysis,
                wineInfo: result.wineInfo,
                rockInfo: result.rockInfo,
                coinInfo: result.coinInfo,
                productInfo: result.productInfo,
                behavior: result.behavior,
                reproduction: result.reproduction,
                distinguishingFeatures: result.distinguishingFeatures || [], // Ensure it's an array
                ecosystemRole: result.ecosystemRole,
                threats: result.threats || [], // Ensure it's an array
                usage: result.usage || [],
                maintenance: result.maintenance || [],
                safety: result.safety || [],
                relatedItems: result.relatedItems || [],
                marketTrends: result.marketTrends,
                environmentalImpact: result.environmentalImpact,
                isFavorite: false, // Default to false when saving
                tags: [], // Default to empty array
                isPrivate: false, // Default to false
              };
              addScanToHistory(historyItem);
              hapticService.triggerNotificationFeedback('success');
              if (result.category === 'Food' && result.nutritionalInfo) {
                notificationService.sendNotification({
                  title: 'Food Tracked!',
                  body: `${result.name} - ${result.nutritionalInfo.totalCalories} calories logged`,
                });
              }
              router.replace('/history');
            }}
          >
            <Text style={[styles.primaryButtonText, { color: colors.buttonText, fontSize: getScaledFontSize(16) }]}>
              {result.category === 'Food' ? 'Done' : 'Save to History'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Edit Modal for Food Items */}
      <Modal
        visible={isEditModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <TouchableOpacity onPress={() => setIsEditModalVisible(false)}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text, fontSize: getScaledFontSize(18) }]}>
              Edit Food Details
            </Text>
            <TouchableOpacity onPress={() => setIsEditModalVisible(false)}>
              <Check size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            <Text style={[styles.modalSectionTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
              Manual Entry
            </Text>
            <Text style={[styles.modalDescription, { color: colors.textSecondary, fontSize: getScaledFontSize(14) }]}>
              If the app couldn't identify your food correctly, you can manually enter the nutritional information below.
            </Text>
            {/* Add manual entry form here */}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  shareButton: {
    padding: 8,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    height: 250,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  resultImage: {
    width: '100%',
    height: '100%',
  },
  confidenceBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  mainInfoCard: {
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
  },
  mainInfoCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  mainInfoCategoryText: {
    fontSize: 13,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  categoryIconBg: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainInfoName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  mainInfoScientificName: {
    fontSize: 16,
    fontStyle: 'italic',
  },
  nutritionCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
  },
  servingsControl: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  servingsLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  servingsCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  servingsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servingsValue: {
    fontSize: 18,
    fontWeight: '600',
    minWidth: 40,
    textAlign: 'center',
  },
  calorieDisplay: {
    alignItems: 'center',
    marginBottom: 24,
  },
  calorieMainContainer: {
    alignItems: 'center',
  },
  calorieIconBg: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  calorieNumber: {
    fontSize: 36,
    fontWeight: '700',
    marginBottom: 4,
  },
  calorieLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  macroContainer: {
    marginBottom: 20,
  },
  macroRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  macroItem: {
    alignItems: 'center',
    flex: 1,
  },
  macroIconBg: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  macroValue: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  macroLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  healthScoreContainer: {
    marginBottom: 16,
  },
  healthScoreRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  healthScoreLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  healthScoreLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  healthScoreRight: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  healthScoreValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  healthScoreMax: {
    fontSize: 16,
    fontWeight: '500',
  },
  healthScoreBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  healthScoreProgress: {
    height: '100%',
    borderRadius: 4,
  },
  allergensContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  allergensText: {
    fontSize: 14,
    fontWeight: '500',
  },
  card: {
    margin: 16,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
  },
  collapsibleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  collapsibleTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  collapsibleIconBg: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  collapsibleTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  collapsibleContent: {
    padding: 16,
    paddingTop: 0,
  },
  ingredientsContainer: {
    gap: 12,
  },
  ingredientsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  ingredientsTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  ingredientLeft: {
    flex: 1,
  },
  ingredientName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  ingredientAmount: {
    fontSize: 12,
  },
  ingredientRight: {
    alignItems: 'flex-end',
  },
  ingredientCalories: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  ingredientMacros: {
    flexDirection: 'row',
    gap: 8,
  },
  ingredientMacro: {
    fontSize: 10,
    fontWeight: '500',
  },
  foodAnalysisContainer: {
    gap: 16,
  },
  mealInfoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  mealTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  mealTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  cookingInfoRow: {
    gap: 12,
  },
  cookingInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cookingInfoText: {
    fontSize: 13,
    fontWeight: '500',
  },
  calorieBreakdownContainer: {
    gap: 12,
  },
  calorieBreakdownTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  calorieBreakdownChart: {
    flexDirection: 'row',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  calorieBreakdownBar: {
    height: '100%',
  },
  calorieBreakdownLegend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '500',
  },
  healthSection: {
    marginBottom: 16,
  },
  healthSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  healthSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  bulletList: {
    gap: 8,
  },
  bulletItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  bulletIcon: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 2,
  },
  bulletText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  alternativesContainer: {
    gap: 12,
  },
  alternativeItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  alternativeSuggestion: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  alternativeBenefit: {
    fontSize: 13,
    marginBottom: 4,
  },
  alternativeCalories: {
    fontSize: 12,
    fontWeight: '600',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  actionSection: {
    padding: 16,
    gap: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    gap: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  errorSubText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
