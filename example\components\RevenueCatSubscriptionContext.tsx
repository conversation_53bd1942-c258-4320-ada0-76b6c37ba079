import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import Purchases, { CustomerInfo, PurchasesOfferings, LogInResult } from 'react-native-purchases';

export type SubscriptionTier = 'free' | 'pro' | 'expert';

interface RevenueCatSubscriptionContextType {
  subscription: {
    tier: SubscriptionTier;
    isSubscribed: boolean;
    expirationDate: string | null;
    customerInfo: CustomerInfo | null;
  };
  loading: boolean;
  checkSubscription: () => Promise<void>;
  presentPaywall: () => Promise<PurchasesOfferings | undefined>;
  restorePurchases: () => Promise<void>;
  logIn: (userId: string) => Promise<LogInResult>;
  logOut: () => Promise<void>;
}

const RevenueCatSubscriptionContext = createContext<RevenueCatSubscriptionContextType | null>(null);

export const RevenueCatSubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [subscription, setSubscription] = useState<{
    tier: SubscriptionTier;
    isSubscribed: boolean;
    expirationDate: string | null;
    customerInfo: CustomerInfo | null;
  }>({
    tier: 'free' as SubscriptionTier,
    isSubscribed: false,
    expirationDate: null,
    customerInfo: null,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeRevenueCat();
  }, []);

  const initializeRevenueCat = async () => {
    try {
      const appleApiKey = process.env.EXPO_PUBLIC_REVENUECAT_APPLE_API_KEY;
      const googleApiKey = process.env.EXPO_PUBLIC_REVENUECAT_GOOGLE_API_KEY;
      
      if (!appleApiKey || !googleApiKey) {
        throw new Error('RevenueCat API keys are not defined');
      }

      Purchases.setLogLevel(Purchases.LOG_LEVEL.VERBOSE);
      
      if (Platform.OS === 'ios') {
        Purchases.configure({ apiKey: appleApiKey });
      } else if (Platform.OS === 'android') {
        Purchases.configure({ apiKey: googleApiKey });
      }

      console.log('RevenueCat SDK initialized successfully');
      await checkSubscription();
    } catch (error) {
      console.error('RevenueCat initialization error:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkSubscription = useCallback(async (): Promise<void> => {
    try {
      const customerInfo = await Purchases.getCustomerInfo();
      const isSubscribed = customerInfo.entitlements.active.premium !== undefined;
      const expirationDate: string | null = customerInfo.entitlements.active.premium?.expirationDate || null;
      
      setSubscription({
        tier: isSubscribed ? 'pro' : 'free',
        isSubscribed,
        expirationDate,
        customerInfo,
      });
    } catch (error) {
      console.error('Error checking subscription:', error);
      setSubscription({
        tier: 'free',
        isSubscribed: false,
        expirationDate: null,
        customerInfo: null,
      });
    }
  }, []);

  const presentPaywall = async (): Promise<PurchasesOfferings | undefined> => {
    try {
      const offerings = await Purchases.getOfferings();
      if (offerings.current) {
        // This would typically present the paywall UI
        // For now, we'll return the offerings
        return offerings;
      }
      return undefined;
    } catch (error) {
      console.error('Error presenting paywall:', error);
      Alert.alert('Error', 'Unable to load subscription options');
      return undefined;
    }
  };

  const restorePurchases = async (): Promise<void> => {
    try {
      await Purchases.restorePurchases();
      await checkSubscription();
      Alert.alert('Success', 'Purchases restored successfully');
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert('Error', 'Failed to restore purchases');
    }
  };

  const logIn = async (userId: string): Promise<LogInResult> => {
    try {
      const result = await Purchases.logIn(userId);
      await checkSubscription();
      return result;
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  };

  const logOut = async (): Promise<void> => {
    try {
      await Purchases.logOut();
      await checkSubscription();
    } catch (error) {
      console.error('Error logging out:', error);
      throw error;
    }
  };

  // Fixed useEffect - removed the problematic unsubscribe pattern
  useEffect(() => {
    // Listen for customer info updates
    const customerInfoUpdateListener = (customerInfo: CustomerInfo) => {
      const isSubscribed = customerInfo.entitlements.active.premium !== undefined;
      const expirationDate: string | null = customerInfo.entitlements.active.premium?.expirationDate || null;
      
      setSubscription({
        tier: isSubscribed ? 'pro' : 'free',
        isSubscribed,
        expirationDate,
        customerInfo,
      });
    };

    Purchases.addCustomerInfoUpdateListener(customerInfoUpdateListener);
    
    // No return cleanup needed since addCustomerInfoUpdateListener doesn't return an unsubscribe function
  }, []);

  return (
    <RevenueCatSubscriptionContext.Provider
      value={{
        subscription,
        loading,
        checkSubscription,
        presentPaywall,
        restorePurchases,
        logIn,
        logOut,
      }}
    >
      {children}
    </RevenueCatSubscriptionContext.Provider>
  );
};

export const useRevenueCatSubscription = () => {
  const context = useContext(RevenueCatSubscriptionContext);
  if (!context) {
    throw new Error('useRevenueCatSubscription must be used within a RevenueCatSubscriptionProvider');
  }
  return context;
};