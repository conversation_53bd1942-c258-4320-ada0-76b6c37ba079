import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  Alert,
  Share,
  Animated,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSubscription } from '@/components/SubscriptionContext';
import { Colors } from '@/constants/Colors';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IdentificationResult, Category } from '@/components/GeminiService';

const { width } = Dimensions.get('window');

interface CategoryConfig {
  icon: string;
  color: string;
  gradient: [string, string];
  bgColor: string;
  actionLabel: string;
  actionIcon: string;
  secondaryAction?: {
    label: string;
    icon: string;
  };
}

const categoryConfigs: Record<Category, CategoryConfig> = {
  'Food': {
    icon: 'fork.knife',
    color: '#FF6B6B',
    gradient: ['#FF6B6B', '#FF8E8E'],
    bgColor: '#FFF5F5',
    actionLabel: 'Find Recipes',
    actionIcon: 'book.fill',
    secondaryAction: {
      label: 'Nutrition Info',
      icon: 'heart.fill'
    }
  },
  'Plant': {
    icon: 'leaf.fill',
    color: '#4ECDC4',
    gradient: ['#4ECDC4', '#44A08D'],
    bgColor: '#F0FDFC',
    actionLabel: 'Plant Care Guide',
    actionIcon: 'book.fill',
    secondaryAction: {
      label: 'Buy Seeds',
      icon: 'cart.fill'
    }
  },
  'Animal': {
    icon: 'pawprint.fill',
    color: '#45B7D1',
    gradient: ['#45B7D1', '#96CEB4'],
    bgColor: '#F0F9FF',
    actionLabel: 'Learn More',
    actionIcon: 'safari.fill',
    secondaryAction: {
      label: 'Conservation Info',
      icon: 'shield.fill'
    }
  },
  'Electronics': {
    icon: 'laptopcomputer',
    color: '#6C5CE7',
    gradient: ['#6C5CE7', '#A29BFE'],
    bgColor: '#F8F7FF',
    actionLabel: 'Shop Now',
    actionIcon: 'cart.fill',
    secondaryAction: {
      label: 'Compare Prices',
      icon: 'chart.bar.fill'
    }
  },
  'Wine / Beverage': {
    icon: 'wineglass.fill',
    color: '#8E44AD',
    gradient: ['#8E44AD', '#C39BD3'],
    bgColor: '#FDF2F8',
    actionLabel: 'Find Store',
    actionIcon: 'location.fill',
    secondaryAction: {
      label: 'Wine Pairings',
      icon: 'fork.knife'
    }
  },
  'Coin / Currency': {
    icon: 'dollarsign.circle.fill',
    color: '#F39C12',
    gradient: ['#F39C12', '#F7DC6F'],
    bgColor: '#FFFBF0',
    actionLabel: 'Check Value',
    actionIcon: 'chart.line.uptrend.xyaxis',
    secondaryAction: {
      label: 'Find Dealers',
      icon: 'location.fill'
    }
  },
  'Rock / Mineral': {
    icon: 'diamond.fill',
    color: '#7F8C8D',
    gradient: ['#7F8C8D', '#BDC3C7'],
    bgColor: '#F8F9FA',
    actionLabel: 'Learn More',
    actionIcon: 'book.fill',
    secondaryAction: {
      label: 'Find Locations',
      icon: 'map.fill'
    }
  },
  'Insect': {
    icon: 'ant.fill',
    color: '#27AE60',
    gradient: ['#27AE60', '#58D68D'],
    bgColor: '#F0FFF4',
    actionLabel: 'Species Info',
    actionIcon: 'safari.fill',
    secondaryAction: {
      label: 'Report Sighting',
      icon: 'location.fill'
    }
  },
  'Product': {
    icon: 'cube.box.fill',
    color: '#E67E22',
    gradient: ['#E67E22', '#F4A460'],
    bgColor: '#FFF8F0',
    actionLabel: 'Shop Now',
    actionIcon: 'cart.fill',
    secondaryAction: {
      label: 'Read Reviews',
      icon: 'star.fill'
    }
  },
  'Unknown': {
    icon: 'questionmark.circle.fill',
    color: '#95A5A6',
    gradient: ['#95A5A6', '#BDC3C7'],
    bgColor: '#F5F5F5',
    actionLabel: 'Web Search',
    actionIcon: 'magnifyingglass',
    secondaryAction: {
      label: 'Ask Community',
      icon: 'person.3.fill'
    }
  }
};

export default function EnhancedScanResultScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  const { hasFeature, isPremiumUser } = useSubscription();

  const [activeTab, setActiveTab] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const fadeAnim = new Animated.Value(0);

  // Parse the identification result from params
  const result: IdentificationResult = params.result
    ? JSON.parse(params.result as string)
    : getDefaultResult();

  const imageUri = params.imageUri as string;
  const categoryConfig = categoryConfigs[result.category] || categoryConfigs['Unknown'];

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  function getDefaultResult(): IdentificationResult {
    return {
      name: 'MacBook Pro 16-inch',
      scientificName: 'N/A',
      confidence: 92,
      category: 'Electronics',
      description: 'Apple MacBook Pro with M2 Max chip, featuring advanced performance and professional capabilities.',
      scanQuality: 'good',
      physicalTraits: { color: 'Space Gray', texture: 'Aluminum' },
      alternatives: [
        { name: 'MacBook Pro 14-inch', scientificName: 'N/A', confidence: 78 },
        { name: 'MacBook Air M2', scientificName: 'N/A', confidence: 65 }
      ],
      distinguishingFeatures: ['Apple Logo', 'Aluminum Body', 'USB-C Ports'],
      recommendedActions: ['Check specifications', 'Compare prices', 'Find accessories'],
      electronicsInfo: {
        brand: 'Apple',
        model: 'MacBook Pro 16-inch M2 Max',
        category: 'Laptop Computer',
        estimatedPrice: '$2,499 - $4,299',
        specifications: {
          'Processor': 'Apple M2 Max',
          'RAM': '16GB - 96GB',
          'Storage': '512GB - 8TB SSD',
          'Display': '16.2-inch Liquid Retina XDR',
          'Operating System': 'macOS Ventura',
          'Release Year': '2023'
        },
        features: [
          'M2 Max chip with 12-core CPU',
          '38-core GPU option',
          'Up to 22 hours battery life',
          'ProRes acceleration',
          'Three Thunderbolt 4 ports',
          'MagSafe 3 charging'
        ]
      }
    };
  }

  const handlePrimaryAction = async () => {
    let searchQuery = '';
    
    switch (result.category) {
      case 'Electronics':
        if (result.electronicsInfo?.brand && result.electronicsInfo?.model) {
          searchQuery = `${result.electronicsInfo.brand} ${result.electronicsInfo.model} buy online`;
        } else {
          searchQuery = `${result.name} specifications buy`;
        }
        break;
      
      case 'Food':
        searchQuery = `${result.name} recipes cooking`;
        break;
        
      case 'Plant':
        searchQuery = `${result.name} plant care guide growing tips`;
        break;
        
      case 'Animal':
        searchQuery = `${result.name} animal facts behavior habitat`;
        break;
        
      case 'Wine / Beverage':
        if (result.wineInfo?.wineType && result.wineInfo?.region) {
          searchQuery = `${result.wineInfo.wineType} wine ${result.wineInfo.region} buy online`;
        } else {
          searchQuery = `${result.name} wine store near me`;
        }
        break;
        
      case 'Coin / Currency':
        if (result.coinInfo?.country && result.coinInfo?.year) {
          searchQuery = `${result.coinInfo.country} ${result.coinInfo.year} coin value price`;
        } else {
          searchQuery = `${result.name} coin value collector`;
        }
        break;
        
      default:
        searchQuery = `${result.name} information`;
    }

    const url = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
    
    try {
      await Linking.openURL(url);
    } catch (error) {
      Alert.alert('Error', 'Could not open browser');
    }
  };

  const handleSecondaryAction = async () => {
    let searchQuery = '';
    
    switch (result.category) {
      case 'Electronics':
        if (result.electronicsInfo?.brand && result.electronicsInfo?.model) {
          searchQuery = `${result.electronicsInfo.brand} ${result.electronicsInfo.model} price comparison`;
        } else {
          searchQuery = `${result.name} price comparison`;
        }
        break;
        
      case 'Food':
        searchQuery = `${result.name} nutrition facts health benefits`;
        break;
        
      case 'Plant':
        searchQuery = `${result.name} seeds buy online gardening`;
        break;
        
      case 'Wine / Beverage':
        searchQuery = `${result.name} food pairing wine recommendations`;
        break;
        
      case 'Coin / Currency':
        searchQuery = `coin dealers ${result.name} collectors near me`;
        break;
        
      default:
        searchQuery = `${result.name} community forum discussion`;
    }

    const url = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
    
    try {
      await Linking.openURL(url);
    } catch (error) {
      Alert.alert('Error', 'Could not open browser');
    }
  };

  const handleShare = async () => {
    try {
      const shareContent = generateShareContent(result);
      await Share.share({
        message: shareContent,
        title: `Check out this ${result.category.toLowerCase()} I identified with Apex AI!`
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const generateShareContent = (result: IdentificationResult): string => {
    let content = `🔍 Identified: ${result.name}\n`;
    content += `📊 Confidence: ${result.confidence}%\n`;

    if (result.category === 'Electronics' && result.electronicsInfo) {
      content += `💻 Brand: ${result.electronicsInfo.brand}\n`;
      content += `💰 Price Range: ${result.electronicsInfo.estimatedPrice}\n`;
    } else if (result.category === 'Food' && result.nutritionalInfo) {
      content += `🍽️ Calories: ${result.nutritionalInfo.totalCalories}\n`;
      content += `💪 Protein: ${result.nutritionalInfo.protein}g\n`;
    }

    content += `\nIdentified with Apex AI - AI-powered object recognition`;
    return content;
  };

  const handleSaveToHistory = async () => {
    if (!isPremiumUser() && !hasFeature('full_history')) {
      Alert.alert(
        'Premium Feature',
        'Full history access requires a premium subscription. Upgrade to save unlimited scans!',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'Upgrade Now', onPress: () => router.push('/subscription') }
        ]
      );
      return;
    }

    Alert.alert('Success', 'Scan saved to your history!');
  };

  const ConfidenceMeter = ({ confidence }: { confidence: number }) => {
    const getConfidenceColor = () => {
      if (confidence >= 80) return '#10B981';
      if (confidence >= 60) return '#F59E0B';
      return '#EF4444';
    };

    return (
      <View style={styles.confidenceMeter}>
        <View style={styles.confidenceTrack}>
          <Animated.View
            style={[
              styles.confidenceFill,
              {
                width: `${confidence}%`,
                backgroundColor: getConfidenceColor(),
                opacity: fadeAnim
              }
            ]}
          />
        </View>
        <Text style={[styles.confidenceText, { color: getConfidenceColor() }]}>
          {confidence}% Confident
        </Text>
      </View>
    );
  };

  const CategorySpecificCard = () => {
    switch (result.category) {
      case 'Electronics':
        return <ElectronicsCard result={result} categoryConfig={categoryConfig} />;
      case 'Food':
        return <FoodCard result={result} categoryConfig={categoryConfig} />;
      case 'Plant':
        return <PlantCard result={result} categoryConfig={categoryConfig} />;
      case 'Animal':
        return <AnimalCard result={result} categoryConfig={categoryConfig} />;
      case 'Wine / Beverage':
        return <WineCard result={result} categoryConfig={categoryConfig} />;
      case 'Coin / Currency':
        return <CoinCard result={result} categoryConfig={categoryConfig} />;
      default:
        return <DefaultCard result={result} categoryConfig={categoryConfig} />;
    }
  };

  const TabNavigation = () => {
    const tabs = ['Overview', 'Specifications', 'Actions'];

    return (
      <View style={styles.tabContainer}>
        {tabs.map((tab, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.tab,
              activeTab === index && [styles.activeTab, { backgroundColor: categoryConfig.color }]
            ]}
            onPress={() => setActiveTab(index)}
          >
            <Text style={[
              styles.tabText,
              activeTab === index && styles.activeTabText
            ]}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
          <IconSymbol size={24} name="arrow.left" color={Colors[colorScheme ?? 'light'].text} />
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={() => setIsBookmarked(!isBookmarked)}
            style={styles.headerButton}
          >
            <IconSymbol
              size={24}
              name={isBookmarked ? "bookmark.fill" : "bookmark"}
              color={isBookmarked ? categoryConfig.color : Colors[colorScheme ?? 'light'].text}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleShare} style={styles.headerButton}>
            <IconSymbol size={24} name="square.and.arrow.up" color={Colors[colorScheme ?? 'light'].text} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Section */}
        <Animated.View style={[styles.imageSection, { opacity: fadeAnim }]}>
          <Image source={{ uri: imageUri }} style={styles.resultImage} />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageOverlay}
          />

          {/* Category Badge */}
          <View style={[styles.categoryBadge, { backgroundColor: categoryConfig.color }]}>
            <IconSymbol size={16} name={categoryConfig.icon as any} color="white" />
            <Text style={styles.categoryText}>{result.category}</Text>
          </View>
        </Animated.View>

        {/* Title Section */}
        <View style={styles.titleSection}>
          <ThemedText style={styles.resultTitle}>{result.name}</ThemedText>
          {result.electronicsInfo?.brand && (
            <ThemedText style={styles.brandText}>by {result.electronicsInfo.brand}</ThemedText>
          )}
          {result.scientificName && result.scientificName !== 'N/A' && (
            <ThemedText style={styles.scientificName}>{result.scientificName}</ThemedText>
          )}
          <ConfidenceMeter confidence={result.confidence} />
        </View>

        {/* Category Specific Card */}
        <CategorySpecificCard />

        {/* Tab Navigation */}
        <TabNavigation />

        {/* Tab Content */}
        <Animated.View style={{ opacity: fadeAnim, paddingHorizontal: 20 }}>
          {activeTab === 0 && <OverviewContent result={result} />}
          {activeTab === 1 && <SpecificationsContent result={result} />}
          {activeTab === 2 && <ActionsContent result={result} />}
        </Animated.View>

        {/* Enhanced Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: categoryConfig.color }]}
            onPress={handleSecondaryAction}
          >
            <IconSymbol size={20} name={categoryConfig.secondaryAction?.icon as any || "star.fill"} color={categoryConfig.color} />
            <Text style={[styles.secondaryButtonText, { color: categoryConfig.color }]}>
              {categoryConfig.secondaryAction?.label || 'More Info'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: categoryConfig.color }]}
            onPress={handlePrimaryAction}
          >
            <IconSymbol size={20} name={categoryConfig.actionIcon as any} color="white" />
            <Text style={styles.primaryButtonText}>{categoryConfig.actionLabel}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Category-specific card components
const ElectronicsCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.electronicsInfo) return null;

  const specs = result.electronicsInfo.specifications || {};
  const keySpecs = ['Processor', 'RAM', 'Storage', 'Operating System', 'Release Year'];

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="laptopcomputer" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>Device Specifications</Text>
          <Text style={styles.cardSubtitle}>{result.electronicsInfo.estimatedPrice}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        {keySpecs.map((spec, index) => (
          specs[spec] && (
            <View key={index} style={styles.specRow}>
              <Text style={styles.specLabel}>{spec}</Text>
              <Text style={styles.specValue}>{specs[spec]}</Text>
            </View>
          )
        ))}
      </View>
    </ThemedView>
  );
};

const FoodCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.nutritionalInfo) return null;

  const nutrition = result.nutritionalInfo;

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="flame.fill" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>{nutrition.totalCalories} Calories</Text>
          <Text style={styles.cardSubtitle}>Per {nutrition.servingSize || 'serving'}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{nutrition.protein}g</Text>
            <Text style={styles.nutritionLabel}>Protein</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{nutrition.carbs}g</Text>
            <Text style={styles.nutritionLabel}>Carbs</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{nutrition.fat}g</Text>
            <Text style={styles.nutritionLabel}>Fat</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{nutrition.fiber}g</Text>
            <Text style={styles.nutritionLabel}>Fiber</Text>
          </View>
        </View>
      </View>
    </ThemedView>
  );
};

const PlantCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.plantInfo) return null;

  const plant = result.plantInfo;

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="leaf.fill" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>Plant Care</Text>
          <Text style={styles.cardSubtitle}>{plant.family || 'Plant Information'}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        {plant.sunRequirements && (
          <View style={styles.careItem}>
            <IconSymbol size={16} name="sun.max.fill" color="#F59E0B" />
            <Text style={styles.careText}>Light: {plant.sunRequirements}</Text>
          </View>
        )}
        {plant.waterNeeds && (
          <View style={styles.careItem}>
            <IconSymbol size={16} name="drop.fill" color="#3B82F6" />
            <Text style={styles.careText}>Water: {plant.waterNeeds}</Text>
          </View>
        )}
        {plant.toxicity && (
          <View style={[styles.careItem, { backgroundColor: '#FEF2F2', borderRadius: 8, padding: 8 }]}>
            <IconSymbol size={16} name="exclamationmark.triangle.fill" color="#EF4444" />
            <Text style={[styles.careText, { color: '#EF4444' }]}>{plant.toxicity}</Text>
          </View>
        )}
      </View>
    </ThemedView>
  );
};

const AnimalCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.animalInfo) return null;

  const animal = result.animalInfo;

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="pawprint.fill" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>Animal Facts</Text>
          <Text style={styles.cardSubtitle}>{animal.conservationStatus || 'Wildlife'}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        {animal.habitat && (
          <View style={styles.factItem}>
            <Text style={styles.factLabel}>Habitat</Text>
            <Text style={styles.factValue}>{animal.habitat}</Text>
          </View>
        )}
        {animal.diet && (
          <View style={styles.factItem}>
            <Text style={styles.factLabel}>Diet</Text>
            <Text style={styles.factValue}>{animal.diet}</Text>
          </View>
        )}
        {animal.lifespan && (
          <View style={styles.factItem}>
            <Text style={styles.factLabel}>Lifespan</Text>
            <Text style={styles.factValue}>{animal.lifespan}</Text>
          </View>
        )}
      </View>
    </ThemedView>
  );
};

const WineCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.wineInfo) return null;

  const wine = result.wineInfo;

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="wineglass.fill" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>{wine.wineType || 'Wine'}</Text>
          <Text style={styles.cardSubtitle}>{wine.region} • {wine.vintage}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        {wine.alcoholContent && (
          <View style={styles.wineDetail}>
            <Text style={styles.wineLabel}>Alcohol</Text>
            <Text style={styles.wineValue}>{wine.alcoholContent}%</Text>
          </View>
        )}
        {wine.servingTemperature && (
          <View style={styles.wineDetail}>
            <Text style={styles.wineLabel}>Serve At</Text>
            <Text style={styles.wineValue}>{wine.servingTemperature}</Text>
          </View>
        )}
      </View>
    </ThemedView>
  );
};

const CoinCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  if (!result.coinInfo) return null;

  const coin = result.coinInfo;

  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name="dollarsign.circle.fill" color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>{coin.denomination || 'Coin'}</Text>
          <Text style={styles.cardSubtitle}>{coin.country} • {coin.year}</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        {coin.estimatedValue && (
          <View style={[styles.valueHighlight, { backgroundColor: '#F0F9FF' }]}>
            <Text style={styles.valueLabel}>Estimated Value</Text>
            <Text style={styles.valueAmount}>{coin.estimatedValue}</Text>
          </View>
        )}
        <View style={styles.coinDetails}>
          {coin.composition && (
            <Text style={styles.coinDetail}>Material: {coin.composition}</Text>
          )}
          {coin.condition && (
            <Text style={styles.coinDetail}>Condition: {coin.condition}</Text>
          )}
          {coin.rarity && (
            <Text style={styles.coinDetail}>Rarity: {coin.rarity}</Text>
          )}
        </View>
      </View>
    </ThemedView>
  );
};

const DefaultCard = ({ result, categoryConfig }: { result: IdentificationResult, categoryConfig: CategoryConfig }) => {
  return (
    <ThemedView style={[styles.categoryCard, { backgroundColor: categoryConfig.bgColor }]}>
      <LinearGradient colors={categoryConfig.gradient} style={styles.cardHeader}>
        <IconSymbol size={24} name={categoryConfig.icon as any} color="white" />
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>Object Details</Text>
          <Text style={styles.cardSubtitle}>General Information</Text>
        </View>
      </LinearGradient>

      <View style={styles.cardContent}>
        <Text style={styles.description}>{result.description}</Text>
      </View>
    </ThemedView>
  );
};

// Tab content components
const OverviewContent = ({ result }: { result: IdentificationResult }) => (
  <View style={styles.tabContent}>
    <ThemedView style={styles.overviewCard}>
      <ThemedText style={styles.descriptionText}>{result.description}</ThemedText>
    </ThemedView>
    {result.alternatives && result.alternatives.length > 0 && (
      <ThemedView style={styles.alternativesCard}>
        <View style={styles.cardHeaderRow}>
          <IconSymbol size={20} name="arrow.triangle.branch" color="#6C5CE7" />
          <ThemedText style={styles.cardTitle}>Similar Items</ThemedText>
        </View>
        {result.alternatives.map((alt, index) => (
          <View key={index} style={styles.alternativeItem}>
            <ThemedText style={styles.alternativeName}>{alt.name}</ThemedText>
            <View style={styles.confidencePill}>
              <Text style={styles.confidencePillText}>{alt.confidence}%</Text>
            </View>
          </View>
        ))}
      </ThemedView>
    )}
  </View>
);

const SpecificationsContent = ({ result }: { result: IdentificationResult }) => {
  const renderSpecifications = () => {
    switch (result.category) {
      case 'Electronics':
        if (!result.electronicsInfo?.specifications) return null;
        return Object.entries(result.electronicsInfo.specifications).map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      case 'Food':
        if (!result.nutritionalInfo) return null;
        const nutrition = result.nutritionalInfo;
        return [
          ['Calories', `${nutrition.totalCalories} per ${nutrition.servingSize || 'serving'}`],
          ['Protein', `${nutrition.protein}g`],
          ['Carbohydrates', `${nutrition.carbs}g`],
          ['Fat', `${nutrition.fat}g`],
          ['Fiber', `${nutrition.fiber}g`],
          ['Sugar', `${nutrition.sugar}g`],
          ['Sodium', `${nutrition.sodium}mg`],
        ].map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      case 'Plant':
        if (!result.plantInfo) return null;
        const plant = result.plantInfo;
        const plantSpecs = [
          ['Family', plant.family],
          ['Native Region', plant.nativeRegion],
          ['Sun Requirements', plant.sunRequirements],
          ['Water Needs', plant.waterNeeds],
          ['Toxicity', plant.toxicity],
        ].filter(([, value]) => value);
        
        return plantSpecs.map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      case 'Animal':
        if (!result.animalInfo) return null;
        const animal = result.animalInfo;
        const animalSpecs = [
          ['Habitat', animal.habitat],
          ['Diet', animal.diet],
          ['Lifespan', animal.lifespan],
          ['Conservation Status', animal.conservationStatus],
        ].filter(([, value]) => value);
        
        return animalSpecs.map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      case 'Wine / Beverage':
        if (!result.wineInfo) return null;
        const wine = result.wineInfo;
        const wineSpecs = [
          ['Type', wine.wineType],
          ['Vintage', wine.vintage?.toString()],
          ['Region', wine.region],
          ['Alcohol Content', wine.alcoholContent ? `${wine.alcoholContent}%` : undefined],
          ['Serving Temperature', wine.servingTemperature],
        ].filter(([, value]) => value);
        
        return wineSpecs.map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      case 'Coin / Currency':
        if (!result.coinInfo) return null;
        const coin = result.coinInfo;
        const coinSpecs = [
          ['Country', coin.country],
          ['Year', coin.year?.toString()],
          ['Denomination', coin.denomination],
          ['Composition', coin.composition],
          ['Condition', coin.condition],
          ['Rarity', coin.rarity],
          ['Estimated Value', coin.estimatedValue],
        ].filter(([, value]) => value);
        
        return coinSpecs.map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
      
      default:
        if (!result.physicalTraits) return null;
        return Object.entries(result.physicalTraits).map(([key, value], index) => (
          <View key={index} style={styles.specificationRow}>
            <Text style={styles.specKey}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
            <Text style={styles.specValue}>{value}</Text>
          </View>
        ));
    }
  };

  return (
    <View style={styles.tabContent}>
      <ThemedView style={styles.specificationsCard}>
        <View style={styles.cardHeaderRow}>
          <IconSymbol size={20} name="info.circle.fill" color="#45B7D1" />
          <ThemedText style={styles.cardTitle}>Detailed Specifications</ThemedText>
        </View>
        <View style={styles.specificationsContent}>
          {renderSpecifications()}
        </View>
      </ThemedView>
    </View>
  );
};

const ActionsContent = ({ result }: { result: IdentificationResult }) => {
  const getActionItems = () => {
    switch (result.category) {
      case 'Electronics':
        return [
          { icon: 'cart.fill', label: 'Shop Online', color: '#10B981' },
          { icon: 'chart.bar.fill', label: 'Compare Prices', color: '#3B82F6' },
          { icon: 'star.fill', label: 'Read Reviews', color: '#F59E0B' },
          { icon: 'wrench.and.screwdriver.fill', label: 'Find Support', color: '#6B7280' },
        ];
      
      case 'Food':
        return [
          { icon: 'book.fill', label: 'Find Recipes', color: '#EF4444' },
          { icon: 'heart.fill', label: 'Nutrition Info', color: '#EC4899' },
          { icon: 'camera.fill', label: 'Log Meal', color: '#8B5CF6' },
          { icon: 'location.fill', label: 'Find Restaurant', color: '#10B981' },
        ];
      
      case 'Plant':
        return [
          { icon: 'book.fill', label: 'Care Guide', color: '#10B981' },
          { icon: 'cart.fill', label: 'Buy Seeds', color: '#059669' },
          { icon: 'calendar', label: 'Set Reminders', color: '#3B82F6' },
          { icon: 'exclamationmark.triangle.fill', label: 'Safety Info', color: '#EF4444' },
        ];
      
      case 'Animal':
        return [
          { icon: 'safari.fill', label: 'Learn More', color: '#3B82F6' },
          { icon: 'shield.fill', label: 'Conservation', color: '#10B981' },
          { icon: 'location.fill', label: 'Report Sighting', color: '#F59E0B' },
          { icon: 'camera.fill', label: 'Photo Tips', color: '#8B5CF6' },
        ];
      
      case 'Wine / Beverage':
        return [
          { icon: 'location.fill', label: 'Find Store', color: '#7C2D92' },
          { icon: 'fork.knife', label: 'Food Pairings', color: '#DC2626' },
          { icon: 'star.fill', label: 'Rate & Review', color: '#F59E0B' },
          { icon: 'gift.fill', label: 'Gift Ideas', color: '#EC4899' },
        ];
      
      case 'Coin / Currency':
        return [
          { icon: 'chart.line.uptrend.xyaxis', label: 'Check Value', color: '#F59E0B' },
          { icon: 'location.fill', label: 'Find Dealers', color: '#10B981' },
          { icon: 'book.fill', label: 'Learn History', color: '#3B82F6' },
          { icon: 'person.3.fill', label: 'Join Community', color: '#8B5CF6' },
        ];
      
      default:
        return [
          { icon: 'magnifyingglass', label: 'Web Search', color: '#6B7280' },
          { icon: 'person.3.fill', label: 'Ask Community', color: '#8B5CF6' },
          { icon: 'camera.fill', label: 'Take Better Photo', color: '#EF4444' },
          { icon: 'book.fill', label: 'Learn More', color: '#3B82F6' },
        ];
    }
  };

  return (
    <View style={styles.tabContent}>
      <ThemedView style={styles.actionsCard}>
        <View style={styles.cardHeaderRow}>
          <IconSymbol size={20} name="bolt.fill" color="#F59E0B" />
          <ThemedText style={styles.cardTitle}>Quick Actions</ThemedText>
        </View>
        <View style={styles.actionsGrid}>
          {getActionItems().map((action, index) => (
            <TouchableOpacity key={index} style={[styles.actionItem, { borderColor: action.color }]}>
              <IconSymbol size={24} name={action.icon as any} color={action.color} />
              <Text style={[styles.actionLabel, { color: action.color }]}>{action.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ThemedView>

      {result.recommendedActions && result.recommendedActions.length > 0 && (
        <ThemedView style={styles.recommendationsCard}>
          <View style={styles.cardHeaderRow}>
            <IconSymbol size={20} name="lightbulb.fill" color="#F59E0B" />
            <ThemedText style={styles.cardTitle}>Recommendations</ThemedText>
          </View>
          {result.recommendedActions.map((action, index) => (
            <View key={index} style={styles.recommendationItem}>
              <View style={styles.recommendationBullet} />
              <Text style={styles.recommendationText}>{action}</Text>
            </View>
          ))}
        </ThemedView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollView: {
    flex: 1,
  },
  imageSection: {
    height: width * 0.75,
    position: 'relative',
    marginBottom: 20,
  },
  resultImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  categoryBadge: {
    position: 'absolute',
    top: 20,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  categoryText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  titleSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  resultTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  brandText: {
    fontSize: 16,
    opacity: 0.8,
    marginBottom: 8,
    fontWeight: '500',
  },
  scientificName: {
    fontSize: 16,
    fontStyle: 'italic',
    opacity: 0.7,
    marginBottom: 16,
  },
  confidenceMeter: {
    marginTop: 8,
  },
  confidenceTrack: {
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 8,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 3,
  },
  confidenceText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  categoryCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    gap: 16,
  },
  cardHeaderText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  cardSubtitle: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    marginTop: 2,
  },
  cardContent: {
    padding: 20,
  },
  specRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  specLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    flex: 1,
  },
  specValue: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'right',
    flex: 1,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  nutritionItem: {
    alignItems: 'center',
    flex: 1,
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  nutritionLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  careText: {
    fontSize: 14,
    color: '#374151',
  },
  factItem: {
    marginBottom: 12,
  },
  factLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  factValue: {
    fontSize: 16,
    color: '#1F2937',
    marginTop: 2,
  },
  wineDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  wineLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  wineValue: {
    fontSize: 14,
    color: '#6B7280',
  },
  valueHighlight: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  valueAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 4,
  },
  coinDetails: {
    gap: 8,
  },
  coinDetail: {
    fontSize: 14,
    color: '#374151',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  activeTab: {
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  tabContent: {
    marginBottom: 20,
  },
  overviewCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  alternativesCard: {
    padding: 20,
    borderRadius: 16,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  cardHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  alternativeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  alternativeName: {
    fontSize: 16,
    flex: 1,
  },
  confidencePill: {
    backgroundColor: '#E5E7EB',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidencePillText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
  },
  specificationsCard: {
    padding: 20,
    borderRadius: 16,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  specificationsContent: {
    gap: 2,
  },
  specificationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  specKey: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    flex: 1,
  },
  actionsCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  recommendationsCard: {
    padding: 20,
    borderRadius: 16,
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    gap: 12,
  },
  recommendationBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#F59E0B',
    marginTop: 7,
  },
  recommendationText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 24,
    gap: 16,
  },
  primaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    gap: 8,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    borderWidth: 2,
    backgroundColor: 'transparent',
    gap: 8,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});