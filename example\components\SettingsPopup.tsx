import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Animated,
  PanResponder,
  Platform,
  Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { X, Target, Camera, Check } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface SettingsPopupProps {
  visible: boolean;
  onClose: () => void;
  initialConfidence: number;
  initialQuality: 'low' | 'medium' | 'high';
  onApply: (newSettings: { confidence: number; quality: 'low' | 'medium' | 'high' }) => void;
}

const imageQualityOptions = [
  { value: 'low' as const, label: 'Low', description: 'Faster processing, smaller files' },
  { value: 'medium' as const, label: 'Medium', description: 'Balanced quality and speed' },
  { value: 'high' as const, label: 'High', description: 'Best quality, larger files' },
];

export default function SettingsPopup({
  visible,
  onClose,
  initialConfidence,
  initialQuality,
  onApply,
}: SettingsPopupProps) {
  // Local state to manage changes within the modal before applying them.
  const [confidence, setConfidence] = useState(initialConfidence);
  const [quality, setQuality] = useState(initialQuality);

  const slideAnim = useRef(new Animated.Value(height)).current;
  const backdropAnim = useRef(new Animated.Value(0)).current;
  const sliderWidth = useRef(0);

  // Reset local state when the modal is opened with new initial props.
  useEffect(() => {
    if (visible) {
      setConfidence(initialConfidence);
      setQuality(initialQuality);
    }
  }, [visible, initialConfidence, initialQuality]);

  // Animation handling
  useEffect(() => {
    Animated.parallel([
      Animated.timing(backdropAnim, {
        toValue: visible ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: visible ? 0 : height,
        tension: 100,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();
  }, [visible]);

  const handleApply = () => {
    onApply({ confidence, quality });
    onClose();
  };

  const handleCancel = () => {
    // Reset state to initial values before closing
    setConfidence(initialConfidence);
    setQuality(initialQuality);
    onClose();
  };

  // More robust PanResponder for the slider
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (evt, gestureState) => {
        if (sliderWidth.current > 0) {
          // Use locationX for accuracy relative to the slider element
          const newPercentage = Math.round((Math.max(0, Math.min(gestureState.x, sliderWidth.current)) / sliderWidth.current) * 100);
          setConfidence(newPercentage);
        }
      },
    })
  ).current;

  const getConfidenceColor = (value: number) => {
    if (value >= 80) return '#22C55E'; // Green
    if (value >= 60) return '#F59E0B'; // Amber
    return '#EF4444'; // Red
  };
  
  const confidenceColor = getConfidenceColor(confidence);

  return (
    <Modal visible={visible} transparent animationType="none" onRequestClose={handleCancel}>
      <Pressable style={styles.backdrop} onPress={handleCancel}>
        <Animated.View style={{ flex: 1, opacity: backdropAnim }}>
            <Animated.View style={[styles.container, { transform: [{ translateY: slideAnim }] }]}>
              {/* By wrapping the card in a Pressable that does nothing, we prevent taps inside from closing the modal */}
              <Pressable> 
                <View style={styles.card}>
                  <View style={styles.header}>
                    <Text style={styles.headerTitle}>AI & Camera Settings</Text>
                    <TouchableOpacity style={styles.closeButton} onPress={handleCancel}>
                      <X size={20} color="#6B7280" />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                      <Text style={styles.sectionTitle}>Confidence Threshold</Text>
                      <View style={[styles.valueBadge, { backgroundColor: confidenceColor }]}>
                        <Text style={styles.valueText}>{confidence}%</Text>
                      </View>
                    </View>
                    <Text style={styles.sectionDescription}>Set the minimum confidence for a positive ID.</Text>
                    
                    <View style={styles.sliderContainer} onLayout={(e) => (sliderWidth.current = e.nativeEvent.layout.width)}>
                      <View style={styles.sliderTrack}>
                        <View style={[styles.sliderTrackActive, { width: `${confidence}%`, backgroundColor: confidenceColor }]} />
                      </View>
                      <Animated.View
                        style={[styles.sliderThumb, { left: `${confidence}%` }]}
                        {...panResponder.panHandlers}>
                        <View style={[styles.sliderThumbInner, { backgroundColor: confidenceColor }]} />
                      </Animated.View>
                    </View>
                  </View>

                  <View style={styles.divider} />

                  <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Image Quality</Text>
                    <Text style={styles.sectionDescription}>Balance capture speed and file size.</Text>
                    <View style={styles.radioGroup}>
                      {imageQualityOptions.map((option) => (
                        <TouchableOpacity key={option.value} style={[styles.radioOption, quality === option.value && styles.radioOptionSelected]} onPress={() => setQuality(option.value)}>
                          <View style={[styles.radioButton, quality === option.value && styles.radioButtonSelected]}>
                            {quality === option.value && <Check size={14} color="#FFFFFF" />}
                          </View>
                          <View>
                            <Text style={[styles.radioLabel, quality === option.value && styles.radioLabelSelected]}>{option.label}</Text>
                            <Text style={styles.radioDescription}>{option.description}</Text>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  <View style={styles.actions}>
                    <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                      <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.saveButton} onPress={handleApply}>
                      <Text style={styles.saveButtonText}>Apply Settings</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Pressable>
            </Animated.View>
        </Animated.View>
      </Pressable>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: { flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.6)', justifyContent: 'flex-end' },
  container: { maxHeight: height * 0.9, },
  card: { backgroundColor: '#FFFFFF', borderTopLeftRadius: 24, borderTopRightRadius: 24, paddingTop: 8, },
  header: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 24, paddingVertical: 20, borderBottomWidth: 1, borderBottomColor: '#F3F4F6' },
  headerTitle: { fontSize: 20, fontFamily: 'Inter-SemiBold', color: '#111827' },
  closeButton: { width: 36, height: 36, borderRadius: 18, backgroundColor: '#F3F4F6', alignItems: 'center', justifyContent: 'center' },
  section: { paddingHorizontal: 24, paddingVertical: 24 },
  sectionHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 },
  sectionTitle: { fontSize: 18, fontFamily: 'Inter-SemiBold', color: '#111827' },
  sectionDescription: { fontSize: 14, fontFamily: 'Inter-Regular', color: '#6B7280', marginBottom: 20, lineHeight: 20 },
  valueBadge: { paddingHorizontal: 12, paddingVertical: 6, borderRadius: 12 },
  valueText: { fontSize: 14, fontFamily: 'Inter-Bold', color: '#FFFFFF' },
  sliderContainer: { height: 24, justifyContent: 'center' },
  sliderTrack: { height: 8, backgroundColor: '#E5E7EB', borderRadius: 4, width: '100%' },
  sliderTrackActive: { position: 'absolute', height: 8, borderRadius: 4 },
  sliderThumb: { position: 'absolute', width: 24, height: 24, borderRadius: 12, borderWidth: 4, borderColor: '#FFFFFF', elevation: 4, shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.2, shadowRadius: 4, transform: [{ translateX: -12 }] },
  sliderThumbInner: { width: '100%', height: '100%', borderRadius: 12 },
  divider: { height: 1, backgroundColor: '#F3F4F6' },
  radioGroup: { gap: 12 },
  radioOption: { flexDirection: 'row', alignItems: 'center', gap: 12, borderWidth: 2, borderColor: '#E5E7EB', borderRadius: 16, padding: 16 },
  radioOptionSelected: { borderColor: '#3B82F6', backgroundColor: '#EFF6FF' },
  radioButton: { width: 20, height: 20, borderRadius: 10, borderWidth: 2, borderColor: '#D1D5DB', alignItems: 'center', justifyContent: 'center' },
  radioButtonSelected: { borderColor: '#3B82F6', backgroundColor: '#3B82F6' },
  radioLabel: { fontSize: 16, fontFamily: 'Inter-SemiBold', color: '#111827' },
  radioLabelSelected: { color: '#1E40AF' },
  radioDescription: { fontSize: 13, color: '#6B7280' },
  actions: { flexDirection: 'row', padding: 24, gap: 12, borderTopWidth: 1, borderTopColor: '#F3F4F6' },
  cancelButton: { flex: 1, paddingVertical: 16, borderRadius: 12, backgroundColor: '#F3F4F6', alignItems: 'center' },
  cancelButtonText: { fontSize: 16, fontFamily: 'Inter-SemiBold', color: '#4B5563' },
  saveButton: { flex: 1.5, paddingVertical: 16, borderRadius: 12, backgroundColor: '#2563EB', alignItems: 'center' },
  saveButtonText: { fontSize: 16, fontFamily: 'Inter-Bold', color: '#FFFFFF' },
});
