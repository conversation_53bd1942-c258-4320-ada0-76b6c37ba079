import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';
import { useSettings } from '@/components/SettingsContext';
import { useTheme } from '@/components/ThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  type: 'toggle' | 'navigation' | 'action';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
}

// Theme Selection Modal Component
interface ThemeSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTheme: (theme: 'light' | 'dark' | 'auto') => void;
  currentTheme: string;
}

const ThemeSelectionModal: React.FC<ThemeSelectionModalProps> = ({
  visible,
  onClose,
  onSelectTheme,
  currentTheme,
}) => {
  const { colors } = useTheme();

  const themes = [
    { id: 'light', name: 'Light', description: 'Always use light theme' },
    { id: 'dark', name: 'Dark', description: 'Always use dark theme' },
    { id: 'auto', name: 'Auto (System)', description: 'Follow device theme' },
  ];

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.modalBackground }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Theme</Text>
            <TouchableOpacity onPress={onClose} style={styles.modalCloseButton}>
              <IconSymbol size={24} name="xmark" color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.themeOptionsContainer}>
            {themes.map((theme) => (
              <TouchableOpacity
                key={theme.id}
                style={[
                  styles.themeOption,
                  { borderBottomColor: colors.border },
                  currentTheme === theme.id && { backgroundColor: colors.cardSecondary },
                ]}
                onPress={() => {
                  onSelectTheme(theme.id as 'light' | 'dark' | 'auto');
                  onClose();
                }}
              >
                <View style={styles.themeOptionContent}>
                  <Text style={[styles.themeOptionTitle, { color: colors.text }]}>
                    {theme.name}
                  </Text>
                  <Text style={[styles.themeOptionDescription, { color: colors.textSecondary }]}>
                    {theme.description}
                  </Text>
                </View>
                {currentTheme === theme.id && (
                  <IconSymbol size={20} name="checkmark" color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { settings, updateSettings, isLoading } = useSettings();
  const { colors } = useTheme();
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [notificationPermissionStatus, setNotificationPermissionStatus] = useState<{
    granted: boolean;
    denied: boolean;
  }>({ granted: false, denied: false });

  // Initialize notification service and haptic service
  useEffect(() => {
    const initializeServices = async () => {
      // Check notification permissions
      const notificationService = NotificationService.getInstance();
      const permissionStatus = await notificationService.checkPermissionStatus();
      setNotificationPermissionStatus(permissionStatus);

      // Initialize haptic service with current setting
      const hapticService = HapticService.getInstance();
      hapticService.setEnabled(settings.accessibility.hapticFeedback);
    };

    if (!isLoading) {
      initializeServices();
    }
  }, [isLoading, settings.accessibility.hapticFeedback]);

  // Handle settings updates with haptic feedback
  const handleUpdateSettings = async (newSettings: Partial<typeof settings>) => {
    try {
      const hapticService = HapticService.getInstance();
      await hapticService.triggerSelectionFeedback();
      await updateSettings(newSettings);
    } catch (error) {
      console.error('Failed to update settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  // Handle notification permission request
  const handleNotificationToggle = async (enabled: boolean) => {
    if (enabled && !notificationPermissionStatus.granted) {
      const notificationService = NotificationService.getInstance();
      const granted = await notificationService.requestPermission();

      if (!granted) {
        Alert.alert(
          'Permission Required',
          'Please enable notifications in your device settings to receive scan results and updates.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              // On iOS/Android, this would open app settings
              if (Platform.OS !== 'web') {
                // Linking.openSettings(); // Uncomment if you want to open settings
              }
            }},
          ]
        );
        return;
      }

      setNotificationPermissionStatus({ granted: true, denied: false });
    }

    await handleUpdateSettings({
      notifications: { ...settings.notifications, enabled }
    });
  };

  // Handle theme selection
  const handleThemeSelection = async (theme: 'light' | 'dark' | 'auto') => {
    await handleUpdateSettings({ theme });
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: () => {
          // TODO: Implement sign out logic
          console.log('Signing out...');
        }},
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => {
          // TODO: Implement account deletion
          console.log('Deleting account...');
        }},
      ]
    );
  };

  // Get current theme display name
  const getThemeDisplayName = () => {
    switch (settings.theme) {
      case 'light': return 'Light';
      case 'dark': return 'Dark';
      case 'auto': return 'Auto (System)';
      default: return 'Auto (System)';
    }
  };

  const settingSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'Profile',
          subtitle: 'Edit your profile information',
          icon: 'person.circle',
          type: 'navigation' as const,
          onPress: () => console.log('Navigate to profile'),
        },
        {
          id: 'subscription',
          title: 'Subscription',
          subtitle: 'Manage your subscription',
          icon: 'crown',
          type: 'navigation' as const,
          onPress: () => router.push('/subscription'),
        },
      ],
    },
    {
      title: 'Appearance',
      items: [
        {
          id: 'theme',
          title: 'Theme',
          subtitle: getThemeDisplayName(),
          icon: 'paintbrush',
          type: 'navigation' as const,
          onPress: () => setShowThemeModal(true),
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          id: 'notifications',
          title: 'Enable Notifications',
          subtitle: notificationPermissionStatus.granted
            ? 'Notifications are enabled'
            : 'Tap to enable notifications',
          icon: 'bell',
          type: 'toggle' as const,
          value: settings.notifications.enabled,
          onToggle: handleNotificationToggle,
        },
        {
          id: 'scanResults',
          title: 'Scan Results',
          subtitle: 'Get notified when scans complete',
          icon: 'magnifyingglass',
          type: 'toggle' as const,
          value: settings.notifications.scanResults,
          onToggle: (value: boolean) => handleUpdateSettings({
            notifications: { ...settings.notifications, scanResults: value }
          }),
        },
        {
          id: 'achievements',
          title: 'Achievements',
          subtitle: 'Celebrate your milestones',
          icon: 'trophy',
          type: 'toggle' as const,
          value: settings.notifications.achievements,
          onToggle: (value: boolean) => handleUpdateSettings({
            notifications: { ...settings.notifications, achievements: value }
          }),
        },
        {
          id: 'community',
          title: 'Community Updates',
          subtitle: 'News and community features',
          icon: 'person.2',
          type: 'toggle' as const,
          value: settings.notifications.community,
          onToggle: (value: boolean) => handleUpdateSettings({
            notifications: { ...settings.notifications, community: value }
          }),
        },
      ],
    },
    {
      title: 'Privacy & Data',
      items: [
        {
          id: 'location',
          title: 'Location Services',
          subtitle: 'Save location with scans',
          icon: 'location',
          type: 'toggle' as const,
          value: settings.privacy.shareLocation,
          onToggle: (value: boolean) => handleUpdateSettings({
            privacy: { ...settings.privacy, shareLocation: value }
          }),
        },
        {
          id: 'autosave',
          title: 'Auto-save Scans',
          subtitle: 'Automatically save successful scans',
          icon: 'square.and.arrow.down',
          type: 'toggle' as const,
          value: settings.identification.autoSave,
          onToggle: (value: boolean) => handleUpdateSettings({
            identification: { ...settings.identification, autoSave: value }
          }),
        },
      ],
    },
    {
      title: 'Accessibility',
      items: [
        {
          id: 'haptic',
          title: 'Haptic Feedback',
          subtitle: 'Vibration for interactions',
          icon: 'iphone.radiowaves.left.and.right',
          type: 'toggle' as const,
          value: settings.accessibility.hapticFeedback,
          onToggle: (value: boolean) => handleUpdateSettings({
            accessibility: { ...settings.accessibility, hapticFeedback: value }
          }),
        },
        {
          id: 'sound',
          title: 'Sound Effects',
          subtitle: 'Audio feedback for actions',
          icon: 'speaker.wave.2',
          type: 'toggle' as const,
          value: settings.accessibility.soundEnabled,
          onToggle: (value: boolean) => handleUpdateSettings({
            accessibility: { ...settings.accessibility, soundEnabled: value }
          }),
        },
      ],
    },
    {
      title: 'App',
      items: [
        {
          id: 'camera',
          title: 'Camera Settings',
          subtitle: 'Configure camera preferences',
          icon: 'camera',
          type: 'navigation' as const,
          onPress: () => console.log('Navigate to camera settings'),
        },
        {
          id: 'data',
          title: 'Data & Storage',
          subtitle: 'Manage app data and cache',
          icon: 'internaldrive',
          type: 'navigation' as const,
          onPress: () => console.log('Navigate to data settings'),
        },
        {
          id: 'api',
          title: 'API Configuration',
          subtitle: 'Manage Gemini API settings',
          icon: 'gear.badge',
          type: 'navigation' as const,
          onPress: () => router.push('/api-config'),
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & FAQ',
          subtitle: 'Get help and find answers',
          icon: 'questionmark.circle',
          type: 'navigation' as const,
          onPress: () => console.log('Navigate to help'),
        },
        {
          id: 'feedback',
          title: 'Send Feedback',
          subtitle: 'Help us improve the app',
          icon: 'envelope',
          type: 'navigation' as const,
          onPress: () => console.log('Send feedback'),
        },
        {
          id: 'about',
          title: 'About',
          subtitle: 'App version and information',
          icon: 'info.circle',
          type: 'navigation' as const,
          onPress: () => console.log('Navigate to about'),
        },
      ],
    },
    {
      title: 'Account Actions',
      items: [
        {
          id: 'signout',
          title: 'Sign Out',
          icon: 'rectangle.portrait.and.arrow.right',
          type: 'action' as const,
          onPress: handleSignOut,
        },
        {
          id: 'delete',
          title: 'Delete Account',
          subtitle: 'Permanently delete your account',
          icon: 'trash',
          type: 'action' as const,
          onPress: handleDeleteAccount,
        },
      ],
    },
  ];

  const renderSettingItem = (item: SettingItem) => {
    return (
      <TouchableOpacity
        key={item.id}
        style={[styles.settingItem, { backgroundColor: colors.card }]}
        onPress={item.onPress}
        disabled={item.type === 'toggle'}
      >
        <View style={styles.settingLeft}>
          <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
            <IconSymbol size={20} name={item.icon as any} color={colors.primary} />
          </View>
          <View style={styles.settingText}>
            <Text style={[styles.settingTitle, { color: colors.text }]}>{item.title}</Text>
            {item.subtitle && (
              <Text style={[styles.settingSubtitle, { color: colors.textSecondary }]}>{item.subtitle}</Text>
            )}
          </View>
        </View>

        <View style={styles.settingRight}>
          {item.type === 'toggle' && item.onToggle && (
            <Switch
              value={item.value}
              onValueChange={item.onToggle}
              trackColor={{
                false: colors.border,
                true: colors.primary + '80'
              }}
              thumbColor={item.value ? colors.primary : '#f4f3f4'}
            />
          )}
          {item.type === 'navigation' && (
            <IconSymbol size={16} name="chevron.right" color={colors.textSecondary} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Customize your experience
        </Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {settingSections.map((section) => (
          <View key={section.title} style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map(renderSettingItem)}
            </View>
          </View>
        ))}

        <View style={styles.footer}>
          <Text style={[styles.versionText, { color: colors.text }]}>
            Bioapex v1.0.0
          </Text>
          <Text style={[styles.copyrightText, { color: colors.textSecondary }]}>
            © 2024 Bioapex. All rights reserved.
          </Text>
        </View>
      </ScrollView>

      {/* Theme Selection Modal */}
      <ThemeSelectionModal
        visible={showThemeModal}
        onClose={() => setShowThemeModal(false)}
        onSelectTheme={handleThemeSelection}
        currentTheme={settings.theme}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 32,
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  sectionContent: {
    gap: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  settingRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  versionText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 12,
    opacity: 0.6,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 0,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  modalCloseButton: {
    padding: 4,
  },
  themeOptionsContainer: {
    padding: 0,
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
  },
  themeOptionContent: {
    flex: 1,
  },
  themeOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  themeOptionDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
});
