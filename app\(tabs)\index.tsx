import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  Animated,
  Dimensions,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  unlocked: boolean;
  progress?: number;
  maxProgress?: number;
}

interface WeeklyGoal {
  id: string;
  title: string;
  current: number;
  target: number;
  icon: string;
  color: string;
}

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const [todayScans, setTodayScans] = useState(0);
  const [totalSpecies, setTotalSpecies] = useState(0);
  const [currentStreak, setCurrentStreak] = useState(0);
  const [weeklyGoals, setWeeklyGoals] = useState<WeeklyGoal[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [fadeAnim] = useState(new Animated.Value(0));

  // Helper function to get card color
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const getGradientColors = () => {
    return colorScheme === 'dark' 
      ? ['#1C1C1E', '#2C2C2E'] 
      : ['#F2F2F7', '#E5E5EA'];
  };

  // Animation on mount
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  // Mock data for stats and features
  useEffect(() => {
    setTodayScans(3);
    setTotalSpecies(47);
    setCurrentStreak(7);

    // Weekly goals
    const mockGoals: WeeklyGoal[] = [
      { id: '1', title: 'Species Scanned', current: 12, target: 20, icon: 'leaf.fill', color: '#4CAF50' },
      { id: '2', title: 'Perfect IDs', current: 8, target: 10, icon: 'star.fill', color: '#FFD700' },
      { id: '3', title: 'New Categories', current: 2, target: 5, icon: 'square.grid.2x2.fill', color: '#FF6B6B' },
    ];
    setWeeklyGoals(mockGoals);

    // Achievements
    const mockAchievements: Achievement[] = [
      { id: '1', title: 'First Scan', description: 'Complete your first identification', icon: 'camera.fill', color: '#007AFF', unlocked: true },
      { id: '2', title: 'Plant Expert', description: 'Identify 25 different plants', icon: 'leaf.fill', color: '#4CAF50', unlocked: true, progress: 25, maxProgress: 25 },
      { id: '3', title: 'Week Warrior', description: 'Scan for 7 days straight', icon: 'flame.fill', color: '#FF9800', unlocked: true },
      { id: '4', title: 'Perfectionist', description: 'Get 10 perfect confidence scores', icon: 'star.fill', color: '#FFD700', unlocked: false, progress: 8, maxProgress: 10 },
      { id: '5', title: 'Explorer', description: 'Discover 5 rare species', icon: 'binoculars.fill', color: '#9C27B0', unlocked: false, progress: 2, maxProgress: 5 },
    ];
    setAchievements(mockAchievements);
  }, []);

  const handleScanPress = () => {
    router.push('/camera');
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'gallery':
        Alert.alert('Gallery', 'Gallery scan feature coming soon!');
        break;
      case 'history':
        router.push('/(tabs)/history');
        break;
      case 'todo':
        router.push('/(tabs)/todo');
        break;
      case 'challenges':
        Alert.alert('Challenges', 'Weekly challenges feature coming soon!');
        break;
      case 'favorites':
        Alert.alert('Favorites', 'Favorites feature coming soon!');
        break;
      case 'map':
        Alert.alert('Scan Map', 'Interactive scan map feature coming soon!');
        break;
      default:
        break;
    }
  };

  const getProgressColor = (progress: number, target: number) => {
    const percentage = progress / target;
    if (percentage >= 1) return '#4CAF50';
    if (percentage >= 0.7) return '#FFD700';
    return Colors[colorScheme ?? 'light'].tint;
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      <Animated.ScrollView 
        style={[styles.scrollView, { opacity: fadeAnim }]} 
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Header with Profile */}
        <ThemedView style={styles.header}>
          <View>
            <ThemedText type="title">Welcome to Apex</ThemedText>
            <ThemedText style={styles.subtitle}>
              Discover and identify the world around you
            </ThemedText>
          </View>
          <TouchableOpacity style={[styles.profileButton, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="person.fill" color={Colors[colorScheme ?? 'light'].tint} />
          </TouchableOpacity>
        </ThemedView>

        {/* Enhanced Main Scan Button with Animation */}
        <TouchableOpacity
          style={styles.scanButtonContainer}
          onPress={handleScanPress}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[Colors[colorScheme ?? 'light'].tint, Colors[colorScheme ?? 'light'].tint + 'AA']}
            style={styles.scanButton}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.scanIconContainer}>
              <IconSymbol size={48} name="camera.fill" color="white" />
              <View style={[styles.scanPulse, { backgroundColor: 'rgba(255,255,255,0.3)' }]} />
            </View>
            <Text style={styles.scanButtonText}>Scan with Camera</Text>
            <Text style={styles.scanButtonSubtext}>AI-powered instant identification</Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Enhanced Stats Cards */}
        <ThemedView style={styles.statsContainer}>
          <LinearGradient
            colors={['#4CAF50', '#4CAF5099']}
            style={styles.statCard}
          >
            <IconSymbol size={24} name="camera.fill" color="white" />
            <ThemedText style={[styles.statNumber, { color: 'white' }]}>{todayScans}</ThemedText>
            <ThemedText style={[styles.statLabel, { color: 'white' }]}>Today's Scans</ThemedText>
          </LinearGradient>
          
          <LinearGradient
            colors={['#FF9800', '#FF980099']}
            style={styles.statCard}
          >
            <IconSymbol size={24} name="leaf.fill" color="white" />
            <ThemedText style={[styles.statNumber, { color: 'white' }]}>{totalSpecies}</ThemedText>
            <ThemedText style={[styles.statLabel, { color: 'white' }]}>Total Species</ThemedText>
          </LinearGradient>
          
          <LinearGradient
            colors={['#FF6B6B', '#FF6B6B99']}
            style={styles.statCard}
          >
            <IconSymbol size={24} name="flame.fill" color="white" />
            <ThemedText style={[styles.statNumber, { color: 'white' }]}>{currentStreak}</ThemedText>
            <ThemedText style={[styles.statLabel, { color: 'white' }]}>Day Streak</ThemedText>
          </LinearGradient>
        </ThemedView>

        {/* Weekly Goals Section */}
        <ThemedView style={styles.goalsContainer}>
          <ThemedText style={styles.sectionTitle}>Weekly Goals</ThemedText>
          {weeklyGoals.map((goal) => (
            <ThemedView key={goal.id} style={[styles.goalItem, { backgroundColor: getCardColor() }]}>
              <View style={[styles.goalIcon, { backgroundColor: goal.color + '20' }]}>
                <IconSymbol size={20} name={goal.icon as any} color={goal.color} />
              </View>
              <View style={styles.goalContent}>
                <ThemedText style={styles.goalTitle}>{goal.title}</ThemedText>
                <View style={styles.goalProgress}>
                  <View style={[styles.goalProgressBar, { backgroundColor: Colors[colorScheme ?? 'light'].text + '20' }]}>
                    <View 
                      style={[
                        styles.goalProgressFill, 
                        { 
                          backgroundColor: getProgressColor(goal.current, goal.target),
                          width: `${Math.min((goal.current / goal.target) * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <ThemedText style={styles.goalText}>
                    {goal.current}/{goal.target}
                  </ThemedText>
                </View>
              </View>
            </ThemedView>
          ))}
        </ThemedView>

        {/* Enhanced Quick Actions */}
        <ThemedView style={styles.quickActionsContainer}>
          <ThemedText style={styles.sectionTitle}>Quick Actions</ThemedText>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('gallery')}
            >
              <IconSymbol size={28} name="photo.fill" color="#007AFF" />
              <ThemedText style={styles.quickActionText}>Gallery</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('history')}
            >
              <IconSymbol size={28} name="clock.fill" color="#FF9800" />
              <ThemedText style={styles.quickActionText}>History</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('favorites')}
            >
              <IconSymbol size={28} name="heart.fill" color="#FF6B6B" />
              <ThemedText style={styles.quickActionText}>Favorites</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('map')}
            >
              <IconSymbol size={28} name="map.fill" color="#4CAF50" />
              <ThemedText style={styles.quickActionText}>Scan Map</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('challenges')}
            >
              <IconSymbol size={28} name="trophy.fill" color="#FFD700" />
              <ThemedText style={styles.quickActionText}>Challenges</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickActionButton, { backgroundColor: getCardColor() }]}
              onPress={() => handleQuickAction('todo')}
            >
              <IconSymbol size={28} name="checklist" color="#9C27B0" />
              <ThemedText style={styles.quickActionText}>Todo</ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedView>

        {/* Achievements Section */}
        <ThemedView style={styles.achievementsContainer}>
          <View style={styles.sectionHeader}>
            <ThemedText style={styles.sectionTitle}>Achievements</ThemedText>
            <TouchableOpacity>
              <ThemedText style={[styles.viewAllText, { color: Colors[colorScheme ?? 'light'].tint }]}>
                View All
              </ThemedText>
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {achievements.slice(0, 5).map((achievement) => (
              <TouchableOpacity
                key={achievement.id}
                style={[
                  styles.achievementCard,
                  { backgroundColor: getCardColor() },
                  !achievement.unlocked && { opacity: 0.6 }
                ]}
              >
                <View style={[styles.achievementIcon, { backgroundColor: achievement.color + '20' }]}>
                  <IconSymbol 
                    size={24} 
                    name={achievement.icon as any} 
                    color={achievement.unlocked ? achievement.color : Colors[colorScheme ?? 'light'].text + '60'} 
                  />
                </View>
                <ThemedText style={styles.achievementTitle} numberOfLines={1}>
                  {achievement.title}
                </ThemedText>
                <ThemedText style={styles.achievementDesc} numberOfLines={2}>
                  {achievement.description}
                </ThemedText>
                {achievement.progress && achievement.maxProgress && (
                  <View style={styles.achievementProgress}>
                    <View style={[styles.achievementProgressBar, { backgroundColor: Colors[colorScheme ?? 'light'].text + '20' }]}>
                      <View 
                        style={[
                          styles.achievementProgressFill, 
                          { 
                            backgroundColor: achievement.color,
                            width: `${(achievement.progress / achievement.maxProgress) * 100}%`
                          }
                        ]} 
                      />
                    </View>
                    <ThemedText style={styles.achievementProgressText}>
                      {achievement.progress}/{achievement.maxProgress}
                    </ThemedText>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </ThemedView>

        {/* Enhanced Recent Activity */}
        <ThemedView style={styles.recentContainer}>
          <View style={styles.sectionHeader}>
            <ThemedText style={styles.sectionTitle}>Recent Discoveries</ThemedText>
            <TouchableOpacity onPress={() => handleQuickAction('history')}>
              <ThemedText style={[styles.viewAllText, { color: Colors[colorScheme ?? 'light'].tint }]}>
                View All
              </ThemedText>
            </TouchableOpacity>
          </View>
          <ThemedView style={[styles.recentItem, { backgroundColor: getCardColor() }]}>
            <LinearGradient
              colors={['#4CAF50', '#4CAF5099']}
              style={styles.recentIcon}
            >
              <IconSymbol size={20} name="leaf.fill" color="white" />
            </LinearGradient>
            <View style={styles.recentContent}>
              <ThemedText style={styles.recentName}>Red Oak</ThemedText>
              <ThemedText style={styles.recentDetails}>Quercus rubra • 95% confidence</ThemedText>
              <View style={styles.recentMeta}>
                <IconSymbol size={12} name="location.fill" color={Colors[colorScheme ?? 'light'].text + '60'} />
                <ThemedText style={styles.recentLocation}>Central Park, NY</ThemedText>
              </View>
            </View>
            <View style={styles.recentTime}>
              <ThemedText style={styles.recentTimeText}>2h ago</ThemedText>
              <View style={[styles.confidenceBadge, { backgroundColor: '#4CAF50' + '20' }]}>
                <ThemedText style={[styles.confidenceText, { color: '#4CAF50' }]}>95%</ThemedText>
              </View>
            </View>
          </ThemedView>
          <ThemedView style={[styles.recentItem, { backgroundColor: getCardColor() }]}>
            <LinearGradient
              colors={['#FF9800', '#FF980099']}
              style={styles.recentIcon}
            >
              <IconSymbol size={20} name="pawprint.fill" color="white" />
            </LinearGradient>
            <View style={styles.recentContent}>
              <ThemedText style={styles.recentName}>American Robin</ThemedText>
              <ThemedText style={styles.recentDetails}>Turdus migratorius • 88% confidence</ThemedText>
              <View style={styles.recentMeta}>
                <IconSymbol size={12} name="location.fill" color={Colors[colorScheme ?? 'light'].text + '60'} />
                <ThemedText style={styles.recentLocation}>Backyard</ThemedText>
              </View>
            </View>
            <View style={styles.recentTime}>
              <ThemedText style={styles.recentTimeText}>5h ago</ThemedText>
              <View style={[styles.confidenceBadge, { backgroundColor: '#FF9800' + '20' }]}>
                <ThemedText style={[styles.confidenceText, { color: '#FF9800' }]}>88%</ThemedText>
              </View>
            </View>
          </ThemedView>
        </ThemedView>
      </Animated.ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  subtitle: {
    marginTop: 8,
    opacity: 0.7,
  },
  profileButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  scanButtonContainer: {
    marginBottom: 30,
  },
  scanButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 24,
    borderRadius: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  scanIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanPulse: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.3,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 22,
    fontWeight: '700',
    marginTop: 16,
  },
  scanButtonSubtext: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
    marginTop: 6,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    marginHorizontal: 4,
    borderRadius: 16,
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: '800',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  goalsContainer: {
    marginBottom: 30,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  goalIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  goalContent: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  goalProgress: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalProgressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    marginRight: 12,
  },
  goalProgressFill: {
    height: '100%',
    borderRadius: 3,
  },
  goalText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 40,
  },
  quickActionsContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: (width - 60) / 3,
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  achievementsContainer: {
    marginBottom: 30,
  },
  achievementCard: {
    width: 140,
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 4,
  },
  achievementDesc: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
  },
  achievementProgress: {
    marginTop: 8,
  },
  achievementProgressBar: {
    height: 4,
    borderRadius: 2,
    marginBottom: 4,
  },
  achievementProgressFill: {
    height: '100%',
    borderRadius: 2,
  },
  achievementProgressText: {
    fontSize: 10,
    opacity: 0.7,
  },
  recentContainer: {
    marginBottom: 30,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  recentIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recentContent: {
    flex: 1,
  },
  recentName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2,
  },
  recentDetails: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  recentMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentLocation: {
    fontSize: 12,
    opacity: 0.6,
    marginLeft: 4,
  },
  recentTime: {
    alignItems: 'flex-end',
  },
  recentTimeText: {
    fontSize: 12,
    opacity: 0.6,
    marginBottom: 4,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  confidenceText: {
    fontSize: 11,
    fontWeight: '700',
  },
});