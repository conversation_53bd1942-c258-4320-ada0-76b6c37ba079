# Firebase & Google Sign-In Setup for Biosapex

This guide will help you set up Firebase Authentication with Google Sign-In and cross-device synchronization for your Biosapex app.

## 🚀 Quick Overview

Your app now includes:
- ✅ Firebase Authentication service
- ✅ Google Sign-In integration
- ✅ Cross-device sync service
- ✅ Integration with RevenueCat user identification
- ✅ Notification and widget data synchronization

## 📋 Prerequisites

You'll need to install these Firebase packages:

```bash
npm install @react-native-firebase/app @react-native-firebase/auth @react-native-firebase/firestore
npm install @react-native-google-signin/google-signin
```

## 🔧 Firebase Project Setup

### Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `biosapex-app` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Create project

### Step 2: Add iOS App

1. In Firebase console, click "Add app" → iOS
2. **iOS bundle ID**: `com.yourcompany.biosapex` (match your Xcode project)
3. **App nickname**: `Biosapex iOS`
4. Download `GoogleService-Info.plist`
5. Add the plist file to your iOS project in Xcode
6. **Important**: Make sure it's added to the target

### Step 3: Add Android App

1. In Firebase console, click "Add app" → Android
2. **Android package name**: `com.yourcompany.biosapex` (match your build.gradle)
3. **App nickname**: `Biosapex Android`
4. **SHA-1 certificate**: Get from your keystore (see below)
5. Download `google-services.json`
6. Place in `android/app/` directory

#### Get SHA-1 Certificate:
```bash
# For debug keystore
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# For release keystore (when you have one)
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

### Step 4: Enable Authentication

1. In Firebase console, go to **Authentication** → **Sign-in method**
2. Enable **Google** sign-in provider
3. Set **Project support email**
4. **Web SDK configuration**: Copy the Web client ID (you'll need this)

### Step 5: Set up Firestore Database

1. Go to **Firestore Database** → **Create database**
2. Choose **Start in test mode** (for development)
3. Select your preferred location
4. Create database

## 📱 App Configuration

### Step 1: Update AuthService Configuration

Edit `components/AuthService.ts` and update the Google Sign-In configuration:

```typescript
GoogleSignin.configure({
  webClientId: 'YOUR_WEB_CLIENT_ID_FROM_FIREBASE', // From Firebase console
  offlineAccess: true,
  hostedDomain: '',
  forceCodeForRefreshToken: true,
});
```

### Step 2: iOS Configuration

Add to `ios/Biosapex/Info.plist`:

```xml
<!-- Add before closing </dict> -->
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>REVERSED_CLIENT_ID</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>YOUR_REVERSED_CLIENT_ID</string>
    </array>
  </dict>
</array>
```

**Get REVERSED_CLIENT_ID from your `GoogleService-Info.plist`**

### Step 3: Android Configuration

Add to `android/app/build.gradle` (bottom of file):

```gradle
apply plugin: 'com.google.gms.google-services'
```

Add to `android/build.gradle` (in dependencies):

```gradle
classpath 'com.google.gms:google-services:4.3.15'
```

## 🔐 Firestore Security Rules

Update your Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /user_data/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow users to read/write their own scan history
    match /scan_history/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow users to read/write their own settings
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🧪 Testing the Integration

### Test Authentication Flow

1. **Anonymous Sign-In**: App starts with anonymous user
2. **Google Sign-In**: User can upgrade to Google account
3. **Data Sync**: Data syncs across devices when signed in
4. **RevenueCat Integration**: Subscription follows the user

### Test Cross-Device Sync

1. Sign in on Device A
2. Make some scans, change settings
3. Sign in with same account on Device B
4. Verify data appears on Device B

## 🔄 How Cross-Device Sync Works

### Data Synchronized:
- **Widget Data**: Daily goals, streaks, achievements
- **Notification Settings**: Reminder times, preferences
- **App Preferences**: Theme, language, units
- **Scan History**: All species identifications
- **Todo Tasks**: User-created tasks and reminders

### Sync Triggers:
- **On Sign-In**: Immediate sync from cloud
- **On Data Change**: Auto-sync to cloud (if signed in)
- **Periodic**: Every 5 minutes (if changes detected)
- **Manual**: User can force sync

### Conflict Resolution:
- **Widget Data**: Most recent timestamp wins
- **Settings**: Cloud data overwrites local
- **History/Tasks**: Merge and deduplicate by ID
- **Timestamps**: Used to determine most recent

## 🔗 RevenueCat Integration

### User Identification:
- **Anonymous Users**: RevenueCat uses anonymous ID
- **Signed-In Users**: RevenueCat uses Firebase UID
- **Account Linking**: Preserves purchases when upgrading from anonymous

### Subscription Sync:
- Subscriptions follow the Firebase user account
- Cross-device subscription status synchronization
- Automatic RevenueCat user identification on sign-in

## 🚨 Important Security Notes

### Production Checklist:
- [ ] Update Firestore rules for production
- [ ] Use release SHA-1 certificates
- [ ] Enable App Check for additional security
- [ ] Set up proper error monitoring
- [ ] Test offline/online sync scenarios

### Privacy Considerations:
- User data is only accessible by the authenticated user
- Anonymous users' data stays local until they sign in
- Clear data handling policies in your privacy policy

## 🛠️ Troubleshooting

### Common Issues:

1. **Google Sign-In fails**:
   - Check SHA-1 certificate is correct
   - Verify bundle ID/package name matches
   - Ensure GoogleService files are properly added

2. **Firestore permission denied**:
   - Check security rules
   - Verify user is authenticated
   - Check user ID matches document path

3. **Sync not working**:
   - Check internet connection
   - Verify Firestore rules allow access
   - Check console for error messages

4. **RevenueCat user not identified**:
   - Ensure Firebase auth is working
   - Check RevenueCat initialization
   - Verify user ID is being passed correctly

## 🎉 You're Ready!

Once configured, your app will have:
- ✅ Secure user authentication
- ✅ Cross-device data synchronization
- ✅ Integrated subscription management
- ✅ Offline-first architecture with cloud backup
- ✅ Privacy-focused data handling

Your users can now:
- Sign in with Google for seamless experience
- Access their data across all devices
- Maintain subscription status across devices
- Work offline with automatic sync when online

Happy syncing! 🚀
