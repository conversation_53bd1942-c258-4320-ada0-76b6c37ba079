import WidgetKit
import SwiftUI

struct StatsProvider: TimelineProvider {
    func placeholder(in context: Context) -> StatsEntry {
        StatsEntry(
            date: Date(),
            todayScans: 5,
            streak: 7,
            totalSpecies: 42,
            weeklyGoal: 25,
            weeklyProgress: 18,
            favoriteCategory: "Plants"
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (StatsEntry) -> ()) {
        let entry = StatsEntry(
            date: Date(),
            todayScans: 3,
            streak: 12,
            totalSpecies: 67,
            weeklyGoal: 25,
            weeklyProgress: 22,
            favoriteCategory: "Birds"
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [StatsEntry] = []

        let currentDate = Date()
        for hourOffset in 0 ..< 6 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = StatsEntry(
                date: entryDate,
                todayScans: getTodayScans(),
                streak: getStreak(),
                totalSpecies: getTotalSpecies(),
                weeklyGoal: getWeeklyGoal(),
                weeklyProgress: getWeeklyProgress(),
                favoriteCategory: getFavoriteCategory()
            )
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func getTodayScans() -> Int {
        return Int.random(in: 0...10)
    }
    
    private func getStreak() -> Int {
        return Int.random(in: 1...30)
    }
    
    private func getTotalSpecies() -> Int {
        return Int.random(in: 20...100)
    }
    
    private func getWeeklyGoal() -> Int {
        return 25
    }
    
    private func getWeeklyProgress() -> Int {
        return Int.random(in: 10...30)
    }
    
    private func getFavoriteCategory() -> String {
        let categories = ["Plants", "Birds", "Insects", "Mammals", "Fungi"]
        return categories.randomElement() ?? "Plants"
    }
}

struct StatsEntry: TimelineEntry {
    let date: Date
    let todayScans: Int
    let streak: Int
    let totalSpecies: Int
    let weeklyGoal: Int
    let weeklyProgress: Int
    let favoriteCategory: String
}

struct StatsWidgetEntryView: View {
    var entry: StatsProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        if family == .systemSmall {
            SmallStatsView(entry: entry)
        } else {
            MediumStatsView(entry: entry)
        }
    }
}

struct SmallStatsView: View {
    let entry: StatsEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Stats")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Main stat
            VStack(alignment: .leading, spacing: 4) {
                Text("\(entry.streak)")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.orange)
                
                Text("day streak")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Secondary stats
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("\(entry.todayScans)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text("today")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(entry.totalSpecies)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text("species")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct MediumStatsView: View {
    let entry: StatsEntry
    
    var weeklyProgressPercentage: Double {
        return min(Double(entry.weeklyProgress) / Double(entry.weeklyGoal), 1.0)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Statistics")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text(entry.date, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Stats grid
            HStack(spacing: 16) {
                // Streak
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                        
                        Text("Streak")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("\(entry.streak)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
                
                Spacer()
                
                // Today's scans
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "viewfinder")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("Today")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("\(entry.todayScans)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                // Total species
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "leaf.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("Species")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("\(entry.totalSpecies)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
            }
            
            // Weekly progress
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text("Weekly Goal")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(entry.weeklyProgress)/\(entry.weeklyGoal)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 4)
                            .cornerRadius(2)
                        
                        Rectangle()
                            .fill(Color.blue)
                            .frame(width: geometry.size.width * weeklyProgressPercentage, height: 4)
                            .cornerRadius(2)
                    }
                }
                .frame(height: 4)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct StatsWidget: Widget {
    let kind: String = "StatsWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: StatsProvider()) { entry in
            StatsWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Scan Statistics")
        .description("Track your scanning progress, streaks, and achievements.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct StatsWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            StatsWidgetEntryView(entry: StatsEntry(
                date: Date(),
                todayScans: 5,
                streak: 7,
                totalSpecies: 42,
                weeklyGoal: 25,
                weeklyProgress: 18,
                favoriteCategory: "Plants"
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            
            StatsWidgetEntryView(entry: StatsEntry(
                date: Date(),
                todayScans: 5,
                streak: 7,
                totalSpecies: 42,
                weeklyGoal: 25,
                weeklyProgress: 18,
                favoriteCategory: "Plants"
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
        }
    }
}
