import React, { createContext, useContext, ReactNode, useMemo } from 'react';
import { useSettings } from './SettingsContext';
import { useBreakpoints } from '../hooks/useBreakpoints';

interface TextScalingContextType {
  getScaledFontSize: (baseSize: number) => number;
  textSize: 'small' | 'normal' | 'large' | 'extra-large';
}

const TextScalingContext = createContext<TextScalingContextType | undefined>(undefined);

export const useTextScaling = () => {
  const context = useContext(TextScalingContext);
  if (!context) {
    throw new Error('useTextScaling must be used within a TextScalingProvider');
  }
  return context;
};

interface TextScalingProviderProps {
  children: ReactNode;
}

export const TextScalingProvider: React.FC<TextScalingProviderProps> = ({ children }) => {
  const { settings } = useSettings();
  const { isTablet, isDesktop } = useBreakpoints();

  const getScaledFontSize = (baseSize: number): number => {
    // Base scaling factors
    let scaleFactor = 1;

    // Apply user preference
    switch (settings.textSize) {
      case 'small':
        scaleFactor = 0.9;
        break;
      case 'large':
        scaleFactor = 1.3;
        break;
      case 'extra-large':
        scaleFactor = 1.6;
        break;
      case 'normal':
      default:
        scaleFactor = 1;
        break;
    }

    // Additional scaling for larger screens
    if (isDesktop) {
      scaleFactor *= 1.2;
    } else if (isTablet) {
      scaleFactor *= 1.1;
    }

    return Math.round(baseSize * scaleFactor);
  };

  const value = useMemo(() => ({
    getScaledFontSize,
    textSize: settings.textSize,
  }), [settings.textSize, isTablet, isDesktop]);

  return (
    <TextScalingContext.Provider value={value}>
      {children}
    </TextScalingContext.Provider>
  );
};