// components/AdaptiveNavigation.tsx
import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { Tabs } from 'expo-router';
import { BlurView } from 'expo-blur';
import { useTheme } from './ThemeContext';
import { useBreakpoints } from '../hooks/useBreakpoints';
import { useSettings } from '@/components/SettingsContext'; // Import useSettings

import {
  Camera,
  Compass,
  Settings,
  ClipboardList,
  Sparkles,
  Menu,
  X
} from 'lucide-react-native';

interface AdaptiveNavigationProps {
  children: React.ReactNode;
}

export const AdaptiveNavigation: React.FC<AdaptiveNavigationProps> = ({ children }) => {
  const { colors } = useTheme();
  const { isTablet, isLargeScreen } = useBreakpoints();
  const { settings } = useSettings(); // Use settings context
  const [sideNavVisible, setSideNavVisible] = React.useState(false);

  // Determine navigation style based on settings and screen size
     const useSideNavigation = false;

  if (useSideNavigation) {
    return (
      <View style={styles.sideNavContainer}>
        {/* Side Navigation Drawer */}
        {sideNavVisible && (
          <View style={[styles.sideNavDrawer, { backgroundColor: colors.background }]}>
            <View style={styles.sideNavHeader}>
              <Text style={[styles.sideNavTitle, { color: colors.text }]}>Apex</Text>
              <TouchableOpacity onPress={() => setSideNavVisible(false)}>
                <X size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            {/* Navigation Items for Side Nav */}
            <TouchableOpacity style={styles.sideNavItem} onPress={() => { /* navigate to index */ setSideNavVisible(false); }}>
              <View style={[styles.sideNavIcon, { backgroundColor: `${colors.primary}1A` }]}>
                <Sparkles size={20} color={colors.primary} />
              </View>
              <Text style={[styles.sideNavText, { color: colors.text }]}>Scan</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.sideNavItem} onPress={() => { /* navigate to history */ setSideNavVisible(false); }}>
              <View style={[styles.sideNavIcon, { backgroundColor: `${colors.primary}1A` }]}>
                <Compass size={20} color={colors.primary} />
              </View>
              <Text style={[styles.sideNavText, { color: colors.text }]}>History</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.sideNavItem} onPress={() => { /* navigate to todo */ setSideNavVisible(false); }}>
              <View style={[styles.sideNavIcon, { backgroundColor: `${colors.primary}1A` }]}>
                <ClipboardList size={20} color={colors.primary} />
              </View>
              <Text style={[styles.sideNavText, { color: colors.text }]}>To-Do</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.sideNavItem} onPress={() => { /* navigate to settings */ setSideNavVisible(false); }}>
              <View style={[styles.sideNavIcon, { backgroundColor: `${colors.primary}1A` }]}>
                <Settings size={20} color={colors.primary} />
              </View>
              <Text style={[styles.sideNavText, { color: colors.text }]}>Settings</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Main content with hamburger menu for tablets */}
        <View style={styles.mainContent}>
          {/* Hamburger menu button */}
          <TouchableOpacity
            style={[styles.hamburgerButton, { backgroundColor: colors.card }]}
            onPress={() => setSideNavVisible(!sideNavVisible)}
          >
            <Menu size={24} color={colors.text} />
          </TouchableOpacity>

          {children}
        </View>
      </View>
    );
  }

  // Default bottom tab navigation for phones or when settings explicitly set to 'bottom'
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: Platform.OS === 'ios' ? 90 : 70,
          paddingBottom: Platform.OS === 'ios' ? 25 : 10,
          paddingTop: 10,
          borderTopWidth: 0,
          backgroundColor: Platform.OS === 'web' ? colors.blur : 'transparent',
          backdropFilter: Platform.OS === 'web' ? 'blur(20px)' : undefined,
        },
        tabBarBackground: Platform.OS !== 'web' ? () => (
          <BlurView
            intensity={100}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
            }}
          />
        ) : undefined,
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarLabelStyle: {
          fontFamily: 'Inter-Medium',
          fontSize: 12,
          marginTop: 4,
        },
        tabBarItemStyle: {
          paddingHorizontal: 16,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Scan',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
              }}
            >
              {focused ? (
                <Sparkles size={size} color={color} />
              ) : (
                <Camera size={size} color={color} />
              )}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="history"
        options={{
          title: 'History',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
              }}
            >
              <Compass size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="todo"
        options={{
          title: 'To-Do',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
              }}
            >
              <ClipboardList size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ size, color, focused }) => (
            <View
              style={{
                padding: 8,
                borderRadius: 16,
                backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
              }}
            >
              <Settings size={size} color={color} />
            </View>
          ),
        }}
      />
    </Tabs>
  );
};

const styles = StyleSheet.create({
  sideNavContainer: {
    flex: 1,
    flexDirection: 'row', // Added to allow side nav and main content to sit side-by-side
  },
  sideNavDrawer: {
    width: 280, // Fixed width for the side navigation
    padding: 20,
    zIndex: 100,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  sideNavHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  sideNavTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
  },
  sideNavItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
  },
  sideNavIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  sideNavText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  mainContent: {
    flex: 1,
  },
  hamburgerButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});
