import WidgetKit
import Swift<PERSON>

struct TodoProvider: TimelineProvider {
    func placeholder(in context: Context) -> TodoEntry {
        TodoEntry(
            date: Date(),
            pendingTasks: 3,
            nextTask: "Scan 5 different plant species",
            nextTaskDue: Date().addingTimeInterval(3600),
            completedToday: 2,
            totalTasks: 5
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (TodoEntry) -> ()) {
        let entry = TodoEntry(
            date: Date(),
            pendingTasks: 2,
            nextTask: "Complete daily water intake goal",
            nextTaskDue: Date().addingTimeInterval(7200),
            completedToday: 3,
            totalTasks: 5
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [TodoEntry] = []

        let currentDate = Date()
        for minuteOffset in stride(from: 0, to: 60, by: 15) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            let entry = TodoEntry(
                date: entryDate,
                pendingTasks: getPendingTasks(),
                nextTask: getNextTask(),
                nextTaskDue: getNextTaskDue(),
                completedToday: getCompletedToday(),
                totalTasks: getTotalTasks()
            )
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func getPendingTasks() -> Int {
        // Fetch from shared storage
        return Int.random(in: 0...5)
    }
    
    private func getNextTask() -> String {
        let tasks = [
            "Scan 5 different plant species",
            "Complete daily water intake goal",
            "Review scan history from this week",
            "Take nature photos",
            "Study botanical terms"
        ]
        return tasks.randomElement() ?? "No tasks"
    }
    
    private func getNextTaskDue() -> Date {
        return Date().addingTimeInterval(Double.random(in: 1800...7200))
    }
    
    private func getCompletedToday() -> Int {
        return Int.random(in: 0...3)
    }
    
    private func getTotalTasks() -> Int {
        return Int.random(in: 3...8)
    }
}

struct TodoEntry: TimelineEntry {
    let date: Date
    let pendingTasks: Int
    let nextTask: String
    let nextTaskDue: Date
    let completedToday: Int
    let totalTasks: Int
}

struct TodoWidgetEntryView: View {
    var entry: TodoProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        if family == .systemSmall {
            SmallTodoView(entry: entry)
        } else {
            MediumTodoView(entry: entry)
        }
    }
}

struct SmallTodoView: View {
    let entry: TodoEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Tasks")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Pending tasks count
            VStack(alignment: .leading, spacing: 4) {
                Text("\(entry.pendingTasks)")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.orange)
                
                Text("pending")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Progress
            HStack {
                Text("\(entry.completedToday)/\(entry.totalTasks)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("today")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct MediumTodoView: View {
    let entry: TodoEntry
    
    var timeUntilDue: String {
        let interval = entry.nextTaskDue.timeIntervalSinceNow
        if interval < 0 {
            return "Overdue"
        } else if interval < 3600 {
            return "\(Int(interval / 60))m"
        } else {
            return "\(Int(interval / 3600))h"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Tasks")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(entry.pendingTasks)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    
                    Text("pending")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Next task
            VStack(alignment: .leading, spacing: 4) {
                Text("Next Task")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(entry.nextTask)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(2)
            }
            
            // Due time and progress
            HStack {
                HStack(spacing: 4) {
                    Image(systemName: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(timeUntilDue)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text("\(entry.completedToday)/\(entry.totalTasks) completed today")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct TodoWidget: Widget {
    let kind: String = "TodoWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: TodoProvider()) { entry in
            TodoWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Tasks & Reminders")
        .description("Keep track of your daily tasks and upcoming reminders.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct TodoWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            TodoWidgetEntryView(entry: TodoEntry(
                date: Date(),
                pendingTasks: 3,
                nextTask: "Scan 5 different plant species",
                nextTaskDue: Date().addingTimeInterval(3600),
                completedToday: 2,
                totalTasks: 5
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            
            TodoWidgetEntryView(entry: TodoEntry(
                date: Date(),
                pendingTasks: 3,
                nextTask: "Complete daily water intake goal",
                nextTaskDue: Date().addingTimeInterval(3600),
                completedToday: 2,
                totalTasks: 5
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
        }
    }
}
