// components/LayoutSettings.tsx
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from './ThemeContext';
import { useTextScaling } from './TextScalingContext';
import {
  LayoutGrid,
  Navigation,
  Rows,
  Columns,
  Maximize,
  Minimize,
  AlignJustify,
  AlignLeft,
  AlignRight,
  LucideIcon,
} from 'lucide-react-native';
import { useSettings } from '@/components/SettingsContext';

type LayoutOptionValue = 'auto' | 'bottom' | 'side' | 'single' | 'multi' | 'compact' | 'normal' | 'spacious';

interface LayoutOption {
  value: LayoutOptionValue;
  label: string;
  icon: LucideIcon;
}

interface SettingGroupProps {
  label: string;
  options: LayoutOption[];
  selectedValue: LayoutOptionValue;
  onSelect: (value: LayoutOptionValue) => void;
}

const LayoutSettings = () => {
  const { settings, updateSettings } = useSettings();
  const { colors } = useTheme();
  const { getScaledFontSize } = useTextScaling();

  const handleLayoutChange = React.useCallback((newLayout: Partial<typeof settings.layout>) => {
    updateSettings({ layout: { ...settings.layout, ...newLayout } });
  }, [settings.layout, updateSettings]);

  const layoutOptions: { [key: string]: LayoutOption[] } = {
    navigationStyle: [
      { value: 'auto', label: 'Auto', icon: AlignJustify },
      { value: 'bottom', label: 'Bottom', icon: AlignLeft },
      { value: 'side', label: 'Side', icon: AlignRight },
    ],
    columnLayout: [
      { value: 'auto', label: 'Auto', icon: LayoutGrid },
      { value: 'single', label: 'Single', icon: Rows },
      { value: 'multi', label: 'Multi', icon: Columns },
    ],
    spacing: [
      { value: 'compact', label: 'Compact', icon: Minimize },
      { value: 'normal', label: 'Normal', icon: Maximize },
      { value: 'spacious', label: 'Spacious', icon: Navigation },
    ],
  };

  return (
    <View style={styles.container}>
      <SettingGroup
        label="Navigation Style"
        options={layoutOptions.navigationStyle}
        selectedValue={settings.layout.navigationStyle}
        onSelect={(value) => handleLayoutChange({ navigationStyle: value as 'auto' | 'bottom' | 'side' })}
      />
      <SettingGroup
        label="Column Layout"
        options={layoutOptions.columnLayout}
        selectedValue={settings.layout.columnLayout}
        onSelect={(value) => handleLayoutChange({ columnLayout: value as 'auto' | 'single' | 'multi' })}
      />
      <SettingGroup
        label="Spacing"
        options={layoutOptions.spacing}
        selectedValue={settings.layout.spacing}
        onSelect={(value) => handleLayoutChange({ spacing: value as 'compact' | 'normal' | 'spacious' })}
      />
    </View>
  );
};

const SettingGroup: React.FC<SettingGroupProps> = ({ label, options, selectedValue, onSelect }) => {
    const { colors } = useTheme();
    const { getScaledFontSize } = useTextScaling();

    return (
        <View style={styles.settingItem}>
            <Text style={[styles.settingLabel, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
                {label}
            </Text>
            <View style={styles.optionGroup}>
                {options.map(({ value, label: optionLabel, icon: Icon }) => {
                    const isActive = selectedValue === value;
                    const activeColor = colors.primary;
                    const inactiveColor = colors.textSecondary;
                    return (
                        <TouchableOpacity
                            key={value}
                            style={[
                                styles.optionButton,
                                { borderColor: isActive ? activeColor : colors.border },
                                isActive && styles.optionActive
                            ]}
                            onPress={() => onSelect(value)}
                        >
                            <Icon size={20} color={isActive ? activeColor : inactiveColor} />
                            <Text style={[styles.optionText, { color: isActive ? activeColor : inactiveColor }]}>
                                {optionLabel}
                            </Text>
                        </TouchableOpacity>
                    );
                })}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
  container: {
    // No specific container styles needed as it's embedded
  },
  settingItem: {
    marginBottom: 24,
  },
  settingLabel: {
    fontFamily: 'Inter-Medium',
    marginBottom: 12,
  },
  optionGroup: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
  },
  optionButton: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    borderWidth: 1.5,
    minWidth: 80,
    gap: 4,
  },
  optionActive: {
    borderWidth: 2,
  },
  optionText: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
  },
});

export default LayoutSettings;