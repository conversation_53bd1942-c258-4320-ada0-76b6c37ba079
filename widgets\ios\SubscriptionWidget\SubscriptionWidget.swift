import WidgetKit
import SwiftUI

struct SubscriptionProvider: TimelineProvider {
    func placeholder(in context: Context) -> SubscriptionEntry {
        SubscriptionEntry(
            date: Date(),
            tier: "Yearly",
            isActive: true,
            daysRemaining: 287,
            isTrialActive: false,
            trialDaysRemaining: 0,
            remainingScans: -1,
            usedScans: 0
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SubscriptionEntry) -> ()) {
        let entry = SubscriptionEntry(
            date: Date(),
            tier: "Monthly",
            isActive: true,
            daysRemaining: 23,
            isTrialActive: false,
            trialDaysRemaining: 0,
            remainingScans: -1,
            usedScans: 15
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [SubscriptionEntry] = []

        let currentDate = Date()
        for hourOffset in 0 ..< 6 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SubscriptionEntry(
                date: entryDate,
                tier: getTier(),
                isActive: getIsActive(),
                daysRemaining: getDaysRemaining(),
                isTrialActive: getIsTrialActive(),
                trialDaysRemaining: getTrialDaysRemaining(),
                remainingScans: getRemainingScans(),
                usedScans: getUsedScans()
            )
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func getTier() -> String? {
        let tiers = ["Free", "Monthly", "Yearly"]
        return tiers.randomElement()
    }
    
    private func getIsActive() -> Bool {
        return Bool.random()
    }
    
    private func getDaysRemaining() -> Int {
        return Int.random(in: 1...365)
    }
    
    private func getIsTrialActive() -> Bool {
        return Bool.random()
    }
    
    private func getTrialDaysRemaining() -> Int {
        return Int.random(in: 0...3)
    }
    
    private func getRemainingScans() -> Int {
        return Bool.random() ? -1 : Int.random(in: 0...5)
    }
    
    private func getUsedScans() -> Int {
        return Int.random(in: 0...20)
    }
}

struct SubscriptionEntry: TimelineEntry {
    let date: Date
    let tier: String?
    let isActive: Bool
    let daysRemaining: Int
    let isTrialActive: Bool
    let trialDaysRemaining: Int
    let remainingScans: Int // -1 for unlimited
    let usedScans: Int
}

struct SubscriptionWidgetEntryView: View {
    var entry: SubscriptionProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        if family == .systemSmall {
            SmallSubscriptionView(entry: entry)
        } else {
            MediumSubscriptionView(entry: entry)
        }
    }
}

struct SmallSubscriptionView: View {
    let entry: SubscriptionEntry
    
    var statusColor: Color {
        if entry.isTrialActive {
            return .orange
        } else if entry.isActive {
            return .green
        } else {
            return .gray
        }
    }
    
    var statusText: String {
        if entry.isTrialActive {
            return "Trial"
        } else if entry.isActive {
            return entry.tier ?? "Active"
        } else {
            return "Free"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(statusColor)
                    .font(.title2)
                
                Text("Plan")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Status
            VStack(alignment: .leading, spacing: 4) {
                Text(statusText)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(statusColor)
                
                if entry.isTrialActive {
                    Text("\(entry.trialDaysRemaining) days left")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else if entry.isActive {
                    Text("\(entry.daysRemaining) days left")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Text("Upgrade available")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // Scans info
            if entry.remainingScans == -1 {
                Text("∞ Unlimited")
                    .font(.caption)
                    .foregroundColor(.blue)
            } else if !entry.isActive && !entry.isTrialActive {
                Text("Subscribe to scan")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct MediumSubscriptionView: View {
    let entry: SubscriptionEntry
    
    var statusColor: Color {
        if entry.isTrialActive {
            return .orange
        } else if entry.isActive {
            return .green
        } else {
            return .gray
        }
    }
    
    var statusText: String {
        if entry.isTrialActive {
            return "Trial Active"
        } else if entry.isActive {
            return "\(entry.tier ?? "Premium") Plan"
        } else {
            return "No Subscription"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(statusColor)
                    .font(.title2)
                
                Text("Subscription")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Status badge
                Text(entry.isActive ? "Active" : "Free")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(statusColor)
                    .cornerRadius(8)
            }
            
            // Plan details
            VStack(alignment: .leading, spacing: 6) {
                Text(statusText)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                if entry.isTrialActive {
                    Text("Trial ends in \(entry.trialDaysRemaining) days")
                        .font(.caption)
                        .foregroundColor(.orange)
                } else if entry.isActive {
                    Text("Renews in \(entry.daysRemaining) days")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Usage info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Scans")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if entry.remainingScans == -1 {
                        Text("Unlimited")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    } else {
                        Text("\(entry.remainingScans) left")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Used Today")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(entry.usedScans)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
            
            // Upgrade prompt for free users
            if !entry.isActive && !entry.isTrialActive {
                HStack {
                    Text("Upgrade for unlimited scans")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "arrow.right")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct SubscriptionWidget: Widget {
    let kind: String = "SubscriptionWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SubscriptionProvider()) { entry in
            SubscriptionWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Subscription Status")
        .description("Monitor your subscription plan and remaining benefits.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct SubscriptionWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SubscriptionWidgetEntryView(entry: SubscriptionEntry(
                date: Date(),
                tier: "Yearly",
                isActive: true,
                daysRemaining: 287,
                isTrialActive: false,
                trialDaysRemaining: 0,
                remainingScans: -1,
                usedScans: 15
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            
            SubscriptionWidgetEntryView(entry: SubscriptionEntry(
                date: Date(),
                tier: "Free",
                isActive: false,
                daysRemaining: 0,
                isTrialActive: false,
                trialDaysRemaining: 0,
                remainingScans: 3,
                usedScans: 2
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
        }
    }
}
