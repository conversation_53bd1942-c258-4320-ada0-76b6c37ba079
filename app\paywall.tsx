import React from 'react';
import { SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ThemedText } from '@/components/ThemedText';
import RevenueCatPaywall from '@/components/RevenueCatPaywall';
import { useSubscription } from '@/components/SubscriptionContext';

export default function PaywallScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { subscription } = useSubscription();

  const handleDismiss = () => {
    router.back();
  };

  const handlePurchaseCompleted = async () => {
    // Refresh subscription state after purchase
    // The RevenueCat paywall will handle the purchase flow
    console.log('Purchase completed, navigating back');
    router.back();
  };

  const handleRestoreCompleted = async () => {
    // Refresh subscription state after restore
    console.log('Restore completed, navigating back');
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      {/* Header with close button */}
      <View style={[styles.header, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleDismiss}
        >
          <IconSymbol size={24} name="xmark" color={Colors[colorScheme ?? 'light'].text} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>Subscription</ThemedText>
        <View style={styles.headerSpacer} />
      </View>

      {/* RevenueCat Paywall */}
      <View style={styles.paywallContainer}>
        <RevenueCatPaywall
          onDismiss={handleDismiss}
          onPurchaseCompleted={handlePurchaseCompleted}
          onRestoreCompleted={handleRestoreCompleted}
          offering="default" // You can customize this based on your RevenueCat dashboard
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 40,
  },
  paywallContainer: {
    flex: 1,
  },
});
