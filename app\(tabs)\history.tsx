import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Share,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

interface ScanHistoryItem {
  id: string;
  name: string;
  scientificName: string;
  category: string;
  confidence: number;
  date: Date;
  imageUri?: string;
  location?: string;
  notes?: string;
  tags?: string[];
  isFavorite?: boolean;
  weather?: string;
  temperature?: string;
  habitat?: string;
  rarity?: 'Common' | 'Uncommon' | 'Rare' | 'Very Rare';
}

type SortOption = 'date' | 'name' | 'confidence' | 'category';
type ViewMode = 'list' | 'grid' | 'timeline';

export default function HistoryScreen() {
  const colorScheme = useColorScheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [scanHistory, setScanHistory] = useState<ScanHistoryItem[]>([]);
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedItem, setSelectedItem] = useState<ScanHistoryItem | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Helper function to get card color
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const getBorderColor = () => {
    return colorScheme === 'dark' ? '#3C3C3E' : '#E5E5EA';
  };

  const getAccentColor = () => {
    return colorScheme === 'dark' ? '#1C1C1E' : '#FFFFFF';
  };

  // Enhanced mock data
  useEffect(() => {
    const mockHistory: ScanHistoryItem[] = [
      {
        id: '1',
        name: 'Red Oak',
        scientificName: 'Quercus rubra',
        category: 'Plant',
        confidence: 95,
        date: new Date(2024, 0, 15),
        location: 'Central Park, NY',
        notes: 'Beautiful mature specimen with distinctive leaf pattern',
        tags: ['deciduous', 'native', 'large'],
        isFavorite: true,
        weather: 'Sunny',
        temperature: '72°F',
        habitat: 'Urban park',
        rarity: 'Common',
        imageUri: 'https://example.com/red-oak.jpg',
      },
      {
        id: '2',
        name: 'American Robin',
        scientificName: 'Turdus migratorius',
        category: 'Animal',
        confidence: 88,
        date: new Date(2024, 0, 14),
        location: 'Backyard',
        notes: 'Early morning sighting, likely searching for worms',
        tags: ['songbird', 'migratory'],
        isFavorite: false,
        weather: 'Overcast',
        temperature: '65°F',
        habitat: 'Suburban yard',
        rarity: 'Common',
      },
      {
        id: '3',
        name: 'Monarch Butterfly',
        scientificName: 'Danaus plexippus',
        category: 'Insect',
        confidence: 92,
        date: new Date(2024, 0, 13),
        location: 'Garden',
        notes: 'Feeding on milkweed flowers, possible migration pattern',
        tags: ['pollinator', 'endangered', 'migratory'],
        isFavorite: true,
        weather: 'Sunny',
        temperature: '78°F',
        habitat: 'Flower garden',
        rarity: 'Uncommon',
      },
      {
        id: '4',
        name: 'Eastern Gray Squirrel',
        scientificName: 'Sciurus carolinensis',
        category: 'Animal',
        confidence: 97,
        date: new Date(2024, 0, 12),
        location: 'Park',
        notes: 'Collecting acorns for winter storage',
        tags: ['mammal', 'urban-adapted'],
        isFavorite: false,
        weather: 'Partly cloudy',
        temperature: '68°F',
        habitat: 'Mixed woodland',
        rarity: 'Common',
      },
      {
        id: '5',
        name: 'Purple Trillium',
        scientificName: 'Trillium erectum',
        category: 'Plant',
        confidence: 89,
        date: new Date(2024, 0, 11),
        location: 'Forest Trail',
        notes: 'Rare spring ephemeral, found in rich deciduous forest',
        tags: ['wildflower', 'spring', 'ephemeral'],
        isFavorite: true,
        weather: 'Cool',
        temperature: '58°F',
        habitat: 'Deciduous forest',
        rarity: 'Rare',
      },
    ];
    setScanHistory(mockHistory);
  }, []);

  const categories = ['All', 'Plant', 'Animal', 'Insect', 'Mineral', 'Fungi'];
  const sortOptions: { label: string; value: SortOption }[] = [
    { label: 'Date', value: 'date' },
    { label: 'Name', value: 'name' },
    { label: 'Confidence', value: 'confidence' },
    { label: 'Category', value: 'category' },
  ];

  const toggleFavorite = (id: string) => {
    setScanHistory(prev => 
      prev.map(item => 
        item.id === id ? { ...item, isFavorite: !item.isFavorite } : item
      )
    );
  };

  const deleteItem = (id: string) => {
    Alert.alert(
      'Delete Scan',
      'Are you sure you want to delete this scan from your history?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => {
          setScanHistory(prev => prev.filter(item => item.id !== id));
        }},
      ]
    );
  };

  const shareItem = async (item: ScanHistoryItem) => {
    try {
      await Share.share({
        message: `I identified a ${item.name} (${item.scientificName}) with ${item.confidence}% confidence using my nature scanning app! Location: ${item.location}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const filterByDate = (item: ScanHistoryItem) => {
    const now = new Date();
    const itemDate = new Date(item.date);
    
    switch (dateRange) {
      case 'today':
        return itemDate.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return itemDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return itemDate >= monthAgo;
      default:
        return true;
    }
  };

  const filteredAndSortedHistory = scanHistory
    .filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.scientificName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesFilter = selectedFilter === 'All' || item.category === selectedFilter;
      const matchesDateRange = filterByDate(item);
      const matchesFavorites = !showFavoritesOnly || item.isFavorite;
      
      return matchesSearch && matchesFilter && matchesDateRange && matchesFavorites;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'confidence':
          return b.confidence - a.confidence;
        case 'category':
          return a.category.localeCompare(b.category);
        case 'date':
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
    });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Plant': return 'leaf.fill';
      case 'Animal': return 'pawprint.fill';
      case 'Insect': return 'ant.fill';
      case 'Mineral': return 'diamond.fill';
      case 'Fungi': return 'brain';
      default: return 'questionmark.circle';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Plant': return '#4CAF50';
      case 'Animal': return '#FF9800';
      case 'Insect': return '#795548';
      case 'Mineral': return '#9C27B0';
      case 'Fungi': return '#607D8B';
      default: return Colors[colorScheme ?? 'light'].tint;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return '#4CAF50';
      case 'Uncommon': return '#FF9800';
      case 'Rare': return '#F44336';
      case 'Very Rare': return '#9C27B0';
      default: return Colors[colorScheme ?? 'light'].text;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStats = () => {
    const totalScans = scanHistory.length;
    const uniqueCategories = new Set(scanHistory.map(item => item.category)).size;
    const avgConfidence = scanHistory.reduce((sum, item) => sum + item.confidence, 0) / totalScans;
    const favorites = scanHistory.filter(item => item.isFavorite).length;
    const thisWeek = scanHistory.filter(item => {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return new Date(item.date) >= weekAgo;
    }).length;
    
    return { 
      totalScans, 
      uniqueCategories, 
      avgConfidence: Math.round(avgConfidence || 0), 
      favorites,
      thisWeek 
    };
  };

  const stats = getStats();

  const renderListItem = (item: ScanHistoryItem) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.historyItem, { backgroundColor: getCardColor() }]}
      onPress={() => setSelectedItem(item)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: getCategoryColor(item.category) + '20' }]}>
        <IconSymbol 
          size={24} 
          name={getCategoryIcon(item.category) as any} 
          color={getCategoryColor(item.category)} 
        />
      </View>
      
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <ThemedText style={styles.itemName} numberOfLines={1}>
            {item.name}
          </ThemedText>
          <TouchableOpacity onPress={() => toggleFavorite(item.id)}>
            <IconSymbol 
              size={18} 
              name={item.isFavorite ? "heart.fill" : "heart"} 
              color={item.isFavorite ? '#FF6B6B' : Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
        </View>
        
        <ThemedText style={styles.itemScientific} numberOfLines={1}>
          {item.scientificName}
        </ThemedText>
        
        <View style={styles.itemDetails}>
          <ThemedText style={styles.itemLocation}>
            {item.location || 'Unknown location'}
          </ThemedText>
          
          <View style={styles.badges}>
            <View style={[styles.rarityBadge, { backgroundColor: getRarityColor(item.rarity || 'Common') + '20' }]}>
              <ThemedText style={[styles.rarityText, { color: getRarityColor(item.rarity || 'Common') }]}>
                {item.rarity}
              </ThemedText>
            </View>
            
            <View style={[styles.confidenceBadge, { backgroundColor: Colors[colorScheme ?? 'light'].tint + '20' }]}>
              <ThemedText style={[styles.confidenceText, { color: Colors[colorScheme ?? 'light'].tint }]}>
                {item.confidence}%
              </ThemedText>
            </View>
          </View>
        </View>
        
        {item.tags && item.tags.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
            {item.tags.map((tag, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: getBorderColor() }]}>
                <ThemedText style={styles.tagText}>#{tag}</ThemedText>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
      
      <View style={styles.itemMeta}>
        <ThemedText style={styles.itemDate}>
          {formatDate(item.date)}
        </ThemedText>
        <IconSymbol size={16} name="chevron.right" color={Colors[colorScheme ?? 'light'].text} />
      </View>
    </TouchableOpacity>
  );

  const renderGridItem = (item: ScanHistoryItem) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.gridItem, { backgroundColor: getCardColor() }]}
      onPress={() => setSelectedItem(item)}
    >
      <View style={styles.gridItemHeader}>
        <View style={[styles.smallCategoryIcon, { backgroundColor: getCategoryColor(item.category) }]}>
          <IconSymbol 
            size={16} 
            name={getCategoryIcon(item.category) as any} 
            color="white" 
          />
        </View>
        <TouchableOpacity onPress={() => toggleFavorite(item.id)}>
          <IconSymbol 
            size={16} 
            name={item.isFavorite ? "heart.fill" : "heart"} 
            color={item.isFavorite ? '#FF6B6B' : Colors[colorScheme ?? 'light'].text} 
          />
        </TouchableOpacity>
      </View>
      
      <ThemedText style={styles.gridItemName} numberOfLines={2}>
        {item.name}
      </ThemedText>
      
      <ThemedText style={styles.gridItemScientific} numberOfLines={1}>
        {item.scientificName}
      </ThemedText>
      
      <View style={styles.gridItemFooter}>
        <View style={[styles.confidenceBadge, { backgroundColor: Colors[colorScheme ?? 'light'].tint + '20' }]}>
          <ThemedText style={[styles.confidenceText, { color: Colors[colorScheme ?? 'light'].tint }]}>
            {item.confidence}%
          </ThemedText>
        </View>
        <ThemedText style={styles.gridItemDate}>
          {formatDate(item.date)}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Scan History</ThemedText>
        <ThemedText style={styles.subtitle}>
          Your identification journey
        </ThemedText>
      </ThemedView>

      {/* Enhanced Stats Cards */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statsScrollContainer}>
        <View style={styles.statsContainer}>
          <ThemedView style={[styles.statCard, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="camera.fill" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.statNumber}>{stats.totalScans}</ThemedText>
            <ThemedText style={styles.statLabel}>Total Scans</ThemedText>
          </ThemedView>
          
          <ThemedView style={[styles.statCard, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="square.grid.2x2" color="#4CAF50" />
            <ThemedText style={styles.statNumber}>{stats.uniqueCategories}</ThemedText>
            <ThemedText style={styles.statLabel}>Categories</ThemedText>
          </ThemedView>
          
          <ThemedView style={[styles.statCard, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="chart.bar.fill" color="#FF9800" />
            <ThemedText style={styles.statNumber}>{stats.avgConfidence}%</ThemedText>
            <ThemedText style={styles.statLabel}>Avg Confidence</ThemedText>
          </ThemedView>
          
          <ThemedView style={[styles.statCard, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="heart.fill" color="#FF6B6B" />
            <ThemedText style={styles.statNumber}>{stats.favorites}</ThemedText>
            <ThemedText style={styles.statLabel}>Favorites</ThemedText>
          </ThemedView>
          
          <ThemedView style={[styles.statCard, { backgroundColor: getCardColor() }]}>
            <IconSymbol size={24} name="calendar" color="#9C27B0" />
            <ThemedText style={styles.statNumber}>{stats.thisWeek}</ThemedText>
            <ThemedText style={styles.statLabel}>This Week</ThemedText>
          </ThemedView>
        </View>
      </ScrollView>

      {/* Search and Controls */}
      <ThemedView style={styles.controlsContainer}>
        <ThemedView style={[styles.searchContainer, { backgroundColor: getCardColor() }]}>
          <IconSymbol size={20} name="magnifyingglass" color={Colors[colorScheme ?? 'light'].text} />
          <TextInput
            style={[styles.searchInput, { color: Colors[colorScheme ?? 'light'].text }]}
            placeholder="Search scans, locations, tags..."
            placeholderTextColor={Colors[colorScheme ?? 'light'].text + '80'}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <IconSymbol size={16} name="xmark.circle.fill" color={Colors[colorScheme ?? 'light'].text} />
            </TouchableOpacity>
          )}
        </ThemedView>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: getCardColor() }]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <IconSymbol size={20} name="line.horizontal.3.decrease.circle" color={Colors[colorScheme ?? 'light'].text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: getCardColor() }]}
            onPress={() => {
              const modes: ViewMode[] = ['list', 'grid'];
              const currentIndex = modes.indexOf(viewMode);
              const nextIndex = (currentIndex + 1) % modes.length;
              setViewMode(modes[nextIndex]);
            }}
          >
            <IconSymbol 
              size={20} 
              name={viewMode === 'list' ? "square.grid.2x2" : "list.bullet"} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: showFavoritesOnly ? Colors[colorScheme ?? 'light'].tint : getCardColor() }]}
            onPress={() => setShowFavoritesOnly(!showFavoritesOnly)}
          >
            <IconSymbol 
              size={20} 
              name="heart.fill" 
              color={showFavoritesOnly ? 'white' : '#FF6B6B'} 
            />
          </TouchableOpacity>
        </View>
      </ThemedView>

      {/* Filters Panel */}
      {showFilters && (
        <ThemedView style={[styles.filtersPanel, { backgroundColor: getCardColor() }]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
            <ThemedText style={styles.filterLabel}>Category:</ThemedText>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.filterChip,
                  {
                    backgroundColor: selectedFilter === category 
                      ? Colors[colorScheme ?? 'light'].tint 
                      : getBorderColor()
                  }
                ]}
                onPress={() => setSelectedFilter(category)}
              >
                <ThemedText
                  style={[
                    styles.filterText,
                    { color: selectedFilter === category ? 'white' : Colors[colorScheme ?? 'light'].text }
                  ]}
                >
                  {category}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
            <ThemedText style={styles.filterLabel}>Time:</ThemedText>
            {[
              { label: 'All Time', value: 'all' },
              { label: 'Today', value: 'today' },
              { label: 'This Week', value: 'week' },
              { label: 'This Month', value: 'month' },
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.filterChip,
                  {
                    backgroundColor: dateRange === option.value 
                      ? Colors[colorScheme ?? 'light'].tint 
                      : getBorderColor()
                  }
                ]}
                onPress={() => setDateRange(option.value as any)}
              >
                <ThemedText
                  style={[
                    styles.filterText,
                    { color: dateRange === option.value ? 'white' : Colors[colorScheme ?? 'light'].text }
                  ]}
                >
                  {option.label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
            <ThemedText style={styles.filterLabel}>Sort by:</ThemedText>
            {sortOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.filterChip,
                  {
                    backgroundColor: sortBy === option.value 
                      ? Colors[colorScheme ?? 'light'].tint 
                      : getBorderColor()
                  }
                ]}
                onPress={() => setSortBy(option.value)}
              >
                <ThemedText
                  style={[
                    styles.filterText,
                    { color: sortBy === option.value ? 'white' : Colors[colorScheme ?? 'light'].text }
                  ]}
                >
                  {option.label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </ThemedView>
      )}

      {/* History List */}
      <ScrollView style={styles.historyList} showsVerticalScrollIndicator={false}>
        {viewMode === 'list' && filteredAndSortedHistory.map(renderListItem)}
        
        {viewMode === 'grid' && (
          <View style={styles.gridContainer}>
            {filteredAndSortedHistory.map(renderGridItem)}
          </View>
        )}
        
        {filteredAndSortedHistory.length === 0 && (
          <ThemedView style={styles.emptyState}>
            <IconSymbol size={64} name="clock" color={Colors[colorScheme ?? 'light'].text} />
            <ThemedText style={styles.emptyText}>
              {searchQuery || selectedFilter !== 'All' || showFavoritesOnly || dateRange !== 'all'
                ? 'No matching scans found' 
                : 'No scan history yet'}
            </ThemedText>
            <ThemedText style={styles.emptySubtext}>
              {searchQuery || selectedFilter !== 'All' || showFavoritesOnly || dateRange !== 'all'
                ? 'Try adjusting your search or filters' 
                : 'Start scanning to build your history'
              }
            </ThemedText>
          </ThemedView>
        )}
      </ScrollView>

      {/* Detail Modal */}
      <Modal
        visible={selectedItem !== null}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        {selectedItem && (
          <SafeAreaView style={[styles.modalContainer, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setSelectedItem(null)}>
                <IconSymbol size={24} name="xmark" color={Colors[colorScheme ?? 'light'].text} />
              </TouchableOpacity>
              
              <View style={styles.modalActions}>
                <TouchableOpacity 
                  onPress={() => shareItem(selectedItem)}
                  style={[styles.modalActionButton, { backgroundColor: getCardColor() }]}
                >
                  <IconSymbol size={20} name="square.and.arrow.up" color={Colors[colorScheme ?? 'light'].text} />
                </TouchableOpacity>
                
                <TouchableOpacity 
                  onPress={() => toggleFavorite(selectedItem.id)}
                  style={[styles.modalActionButton, { backgroundColor: getCardColor() }]}
                >
                  <IconSymbol 
                    size={20} 
                    name={selectedItem.isFavorite ? "heart.fill" : "heart"} 
                    color={selectedItem.isFavorite ? '#FF6B6B' : Colors[colorScheme ?? 'light'].text} 
                  />
                </TouchableOpacity>
                
                <TouchableOpacity 
                  onPress={() => {
                    deleteItem(selectedItem.id);
                    setSelectedItem(null);
                  }}
                  style={[styles.modalActionButton, { backgroundColor: getCardColor() }]}
                >
                  <IconSymbol size={20} name="trash" color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={[styles.modalImageContainer, { backgroundColor: getCategoryColor(selectedItem.category) + '20' }]}>
                <IconSymbol 
                  size={64} 
                  name={getCategoryIcon(selectedItem.category) as any} 
                  color={getCategoryColor(selectedItem.category)} 
                />
              </View>

              <ThemedText type="title" style={styles.modalTitle}>
                {selectedItem.name}
              </ThemedText>
              
              <ThemedText style={styles.modalScientific}>
                {selectedItem.scientificName}
              </ThemedText>

              <View style={styles.modalBadges}>
                <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(selectedItem.category) }]}>
                  <ThemedText style={styles.categoryBadgeText}>{selectedItem.category}</ThemedText>
                </View>
                
                <View style={[styles.rarityBadge, { backgroundColor: getRarityColor(selectedItem.rarity || 'Common') }]}>
                  <ThemedText style={styles.rarityBadgeText}>{selectedItem.rarity}</ThemedText>
                </View>
                
                <View style={[styles.confidenceBadge, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}>
                  <ThemedText style={styles.confidenceBadgeText}>{selectedItem.confidence}% Confidence</ThemedText>
                </View>
              </View>

              <View style={styles.modalSection}>
                <ThemedText style={styles.modalSectionTitle}>Details</ThemedText>
                <View style={[styles.detailCard, { backgroundColor: getCardColor() }]}>
                  <View style={styles.detailRow}>
                    <IconSymbol size={16} name="location" color={Colors[colorScheme ?? 'light'].text} />
                    <ThemedText style={styles.detailText}>{selectedItem.location}</ThemedText>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <IconSymbol size={16} name="calendar" color={Colors[colorScheme ?? 'light'].text} />
                    <ThemedText style={styles.detailText}>{formatDate(selectedItem.date)}</ThemedText>
                  </View>
                  
                  {selectedItem.weather && (
                    <View style={styles.detailRow}>
                      <IconSymbol size={16} name="cloud.sun" color={Colors[colorScheme ?? 'light'].text} />
                      <ThemedText style={styles.detailText}>{selectedItem.weather}</ThemedText>
                    </View>
                  )}
                  
                  {selectedItem.temperature && (
                    <View style={styles.detailRow}>
                      <IconSymbol size={16} name="thermometer" color={Colors[colorScheme ?? 'light'].text} />
                      <ThemedText style={styles.detailText}>{selectedItem.temperature}</ThemedText>
                    </View>
                  )}
                  
                  {selectedItem.habitat && (
                    <View style={styles.detailRow}>
                      <IconSymbol size={16} name="tree" color={Colors[colorScheme ?? 'light'].text} />
                      <ThemedText style={styles.detailText}>{selectedItem.habitat}</ThemedText>
                    </View>
                  )}
                </View>
              </View>

              {selectedItem.notes && (
                <View style={styles.modalSection}>
                  <ThemedText style={styles.modalSectionTitle}>Notes</ThemedText>
                  <View style={[styles.notesCard, { backgroundColor: getCardColor() }]}>
                    <ThemedText style={styles.notesText}>{selectedItem.notes}</ThemedText>
                  </View>
                </View>
              )}

              {selectedItem.tags && selectedItem.tags.length > 0 && (
                <View style={styles.modalSection}>
                  <ThemedText style={styles.modalSectionTitle}>Tags</ThemedText>
                  <View style={styles.modalTagsContainer}>
                    {selectedItem.tags.map((tag, index) => (
                      <View key={index} style={[styles.modalTag, { backgroundColor: getBorderColor() }]}>
                        <ThemedText style={styles.modalTagText}>#{tag}</ThemedText>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  statsScrollContainer: {
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  statCard: {
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    width: 100,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    opacity: 0.7,
    textAlign: 'center',
  },
  controlsContainer: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    marginHorizontal: 8,
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  actionButton: {
    width: (width - 60) / 4,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
  },
  filtersPanel: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  filtersRow: {
    marginBottom: 12,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 12,
    alignSelf: 'center',
    minWidth: 70,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  filterText: {
    fontSize: 13,
    fontWeight: '500',
  },
  historyList: {
    flex: 1,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  itemScientific: {
    fontSize: 14,
    fontStyle: 'italic',
    opacity: 0.7,
    marginBottom: 8,
  },
  itemDetails: {
    marginBottom: 8,
  },
  itemLocation: {
    fontSize: 12,
    opacity: 0.6,
    marginBottom: 6,
  },
  badges: {
    flexDirection: 'row',
    gap: 6,
  },
  rarityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  rarityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  confidenceText: {
    fontSize: 11,
    fontWeight: '600',
  },
  tagsContainer: {
    marginTop: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 6,
    marginRight: 6,
  },
  tagText: {
    fontSize: 10,
    opacity: 0.7,
  },
  itemMeta: {
    alignItems: 'flex-end',
    marginLeft: 8,
  },
  itemDate: {
    fontSize: 12,
    opacity: 0.6,
    marginBottom: 4,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridItem: {
    width: (width - 60) / 2,
    padding: 12,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  gridItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  smallCategoryIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridItemName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    height: 36,
  },
  gridItemScientific: {
    fontSize: 12,
    fontStyle: 'italic',
    opacity: 0.7,
    marginBottom: 8,
  },
  gridItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  gridItemDate: {
    fontSize: 10,
    opacity: 0.6,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 8,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    flex: 1,
  },
  modalImageContainer: {
    height: 200,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  modalScientific: {
    fontSize: 18,
    fontStyle: 'italic',
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  modalBadges: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginBottom: 30,
    flexWrap: 'wrap',
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  categoryBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  rarityBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  confidenceBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailCard: {
    padding: 16,
    borderRadius: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailText: {
    marginLeft: 12,
    fontSize: 14,
  },
  notesCard: {
    padding: 16,
    borderRadius: 12,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  modalTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  modalTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  modalTagText: {
    fontSize: 12,
    opacity: 0.7,
  },
});