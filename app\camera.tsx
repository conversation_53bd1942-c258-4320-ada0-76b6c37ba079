import GeminiService from '@/components/GeminiService';
import PaywallService from '@/components/PaywallService';
import { useSubscription } from '@/components/SubscriptionContext';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import ValidationService from '@/components/ValidationService';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import { readAsStringAsync } from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  Modal,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Vibration,
  View,
} from 'react-native';

const { width, height } = Dimensions.get('window');

interface CameraSettings {
  flash: 'on' | 'off' | 'auto';
  focusMode: 'auto' | 'manual';
  zoom: number;
  gridLines: boolean;
  timer: 0 | 3 | 10;
}

export default function CameraScreen() {
  const colorScheme = useColorScheme();
  const { subscription, isPremiumUser } = useSubscription();
  const paywallService = PaywallService.getInstance();
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [todayScans, setTodayScans] = useState(0);
  const [settings, setSettings] = useState<CameraSettings>({
    flash: 'off',
    focusMode: 'auto',
    zoom: 0,
    gridLines: false,
    timer: 0,
  });
  const [showSettings, setShowSettings] = useState(false);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [timerCount, setTimerCount] = useState(0);
  const [focusPoint, setFocusPoint] = useState<{ x: number; y: number } | null>(null);
  const [recentScans, setRecentScans] = useState<string[]>([]);
  
  const cameraRef = useRef<CameraView | null>(null);
  const router = useRouter();
  const geminiService = new GeminiService();
  
  const [pulseAnim] = useState(new Animated.Value(1));
  const [fadeAnim] = useState(new Animated.Value(0));
  const [zoomAnim] = useState(new Animated.Value(0));

  // Helper function to get card color
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const getOverlayColor = () => {
    return colorScheme === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.5)';
  };

  // Animations
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Pulse animation for scan button
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  // Timer effect
  useEffect(() => {
    if (isTimerActive && timerCount > 0) {
      const timer = setTimeout(() => {
        setTimerCount(prev => prev - 1);
        Vibration.vibrate(100);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (isTimerActive && timerCount === 0) {
      setIsTimerActive(false);
      takePicture();
    }
  }, [isTimerActive, timerCount]);

  // Focus point fade effect
  useEffect(() => {
    if (focusPoint) {
      const timer = setTimeout(() => {
        setFocusPoint(null);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [focusPoint]);

  if (!permission) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <View style={styles.permissionContainer}>
          <IconSymbol size={80} name="camera.fill" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.permissionTitle}>Camera Access Required</ThemedText>
          <ThemedText style={styles.permissionText}>
            We need camera permission to identify objects and help you discover the world around you.
          </ThemedText>
          <TouchableOpacity
            style={[styles.permissionButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
            onPress={requestPermission}
          >
            <IconSymbol size={20} name="camera.fill" color="white" />
            <Text style={styles.permissionButtonText}>Grant Camera Access</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const handleCameraTouch = (event: any) => {
    const { locationX, locationY } = event.nativeEvent;
    setFocusPoint({ x: locationX, y: locationY });
    
    // Animate focus indicator
    Animated.sequence([
      Animated.timing(zoomAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(zoomAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const startTimer = () => {
    if (settings.timer > 0) {
      setIsTimerActive(true);
      setTimerCount(settings.timer);
    } else {
      takePicture();
    }
  };

  const takePicture = async () => {
    if (!cameraRef.current || isProcessing) return;

    // Check subscription limits before scanning
    const canScan = await paywallService.enforceScanLimit(subscription, todayScans);
    if (!canScan) {
      return; // Paywall was shown, user needs to upgrade
    }

    try {
      setIsProcessing(true);
      Vibration.vibrate(50);

      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: true,
        exif: false,
      });

      if (!photo) {
        throw new Error('Failed to take picture');
      }

      // Validate captured image data
      const imageValidation = ValidationService.validateImageBase64(photo.base64);
      if (!imageValidation.isValid) {
        throw new Error(`Invalid image captured: ${imageValidation.errors.join(', ')}`);
      }

      // Add to recent scans
      setRecentScans(prev => [photo.uri, ...prev.slice(0, 2)]);

      // Process the image with Gemini AI
      const result = await geminiService.identifySpecimen(photo.base64!);

      // Increment today's scan count
      setTodayScans(prev => prev + 1);

      router.push({
        pathname: '/scan-result',
        params: {
          imageUri: photo.uri,
          species: result.name,
          confidence: result.confidence.toString(),
          details: JSON.stringify({
            scientificName: result.scientificName,
            category: result.category,
            description: result.description,
            habitat: result.animalInfo?.habitat || result.plantInfo?.nativeRegion || '',
            facts: result.animalInfo?.behavior || result.plantInfo?.careInstructions || [],
            physicalTraits: result.physicalTraits || {},
            distinguishingFeatures: result.distinguishingFeatures || [],
            usage: result.usage || [],
            safety: result.safety || [],
            nutritionalInfo: result.nutritionalInfo,
            foodAnalysis: result.foodAnalysis,
          }),
        },
      });

    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to process image. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const pickImageFromGallery = async () => {
    try {
      setIsProcessing(true);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        if (!asset.base64) {
          const base64 = await readAsStringAsync(asset.uri, {
            encoding: 'base64',
          });
          asset.base64 = base64;
        }

        // Validate selected image data
        if (!asset.base64) {
          throw new Error('Failed to read image data');
        }

        const imageValidation = ValidationService.validateImageBase64(asset.base64);
        if (!imageValidation.isValid) {
          throw new Error(`Invalid image selected: ${imageValidation.errors.join(', ')}`);
        }

        // Process the image with Gemini AI
        const aiResult = await geminiService.identifySpecimen(asset.base64!);

        router.push({
          pathname: '/scan-result',
          params: {
            imageUri: asset.uri,
            species: aiResult.name,
            confidence: aiResult.confidence.toString(),
            details: JSON.stringify({
              scientificName: aiResult.scientificName,
              category: aiResult.category,
              description: aiResult.description,
              habitat: aiResult.animalInfo?.habitat || aiResult.plantInfo?.nativeRegion || '',
              facts: aiResult.animalInfo?.behavior || aiResult.plantInfo?.careInstructions || [],
              physicalTraits: aiResult.physicalTraits || {},
              distinguishingFeatures: aiResult.distinguishingFeatures || [],
              usage: aiResult.usage || [],
              safety: aiResult.safety || [],
              nutritionalInfo: aiResult.nutritionalInfo,
              foodAnalysis: aiResult.foodAnalysis,
            }),
          },
        });
      }
    } catch (error) {
      console.error('Error processing gallery image:', error);
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to process image. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleFlash = () => {
    setSettings(prev => ({
      ...prev,
      flash: prev.flash === 'off' ? 'on' : prev.flash === 'on' ? 'auto' : 'off'
    }));
  };

  const toggleGrid = () => {
    setSettings(prev => ({ ...prev, gridLines: !prev.gridLines }));
  };

  const changeTimer = () => {
    setSettings(prev => ({
      ...prev,
      timer: prev.timer === 0 ? 3 : prev.timer === 3 ? 10 : 0 as 0 | 3 | 10
    }));
  };

  const getFlashIcon = () => {
    switch (settings.flash) {
      case 'on': return 'bolt.fill';
      case 'auto': return 'bolt.badge.a.fill';
      default: return 'bolt.slash.fill';
    }
  };

  const getTimerIcon = () => {
    return settings.timer === 0 ? 'timer' : 'timer.fill';
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        {/* Enhanced Header */}
        <View style={[styles.header, { backgroundColor: getOverlayColor() }]}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <IconSymbol size={24} name="arrow.left" color="white" />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Apex Scanner</Text>
            <Text style={styles.headerSubtitle}>Point & Identify</Text>
          </View>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowSettings(true)}
          >
            <IconSymbol size={24} name="gearshape.fill" color="white" />
          </TouchableOpacity>
        </View>

        {/* Enhanced Camera Container */}
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            facing={facing}
            ref={cameraRef}
            onTouchEnd={handleCameraTouch}
          >
            {/* Grid Lines */}
            {settings.gridLines && (
              <View style={styles.gridOverlay}>
                <View style={styles.gridLine} />
                <View style={[styles.gridLine, { top: '66.66%' }]} />
                <View style={[styles.gridLine, styles.verticalGrid, { left: '33.33%' }]} />
                <View style={[styles.gridLine, styles.verticalGrid, { left: '66.66%' }]} />
              </View>
            )}

            {/* Enhanced Scanning Frame */}
            <View style={styles.overlay}>
              <View style={styles.scanFrame}>
                <View style={[styles.corner, styles.topLeft, { borderColor: Colors[colorScheme ?? 'light'].tint }]} />
                <View style={[styles.corner, styles.topRight, { borderColor: Colors[colorScheme ?? 'light'].tint }]} />
                <View style={[styles.corner, styles.bottomLeft, { borderColor: Colors[colorScheme ?? 'light'].tint }]} />
                <View style={[styles.corner, styles.bottomRight, { borderColor: Colors[colorScheme ?? 'light'].tint }]} />
                
                {/* Animated scanning line */}
                <Animated.View 
                  style={[
                    styles.scanLine, 
                    { 
                      backgroundColor: Colors[colorScheme ?? 'light'].tint,
                      opacity: pulseAnim.interpolate({
                        inputRange: [1, 1.1],
                        outputRange: [0.3, 0.7],
                      }),
                    }
                  ]} 
                />
              </View>
              
              {/* Focus Point Indicator */}
              {focusPoint && (
                <Animated.View
                  style={[
                    styles.focusIndicator,
                    {
                      left: focusPoint.x - 25,
                      top: focusPoint.y - 25,
                      transform: [{ scale: zoomAnim }],
                      borderColor: Colors[colorScheme ?? 'light'].tint,
                    },
                  ]}
                />
              )}

              {/* Timer Countdown */}
              {isTimerActive && timerCount > 0 && (
                <View style={styles.timerOverlay}>
                  <Animated.Text
                    style={[
                      styles.timerText,
                      {
                        transform: [{ scale: pulseAnim }],
                        color: Colors[colorScheme ?? 'light'].tint,
                      },
                    ]}
                  >
                    {timerCount}
                  </Animated.Text>
                </View>
              )}
              
              {/* Enhanced Instructions */}
              <View style={styles.instructionsContainer}>
                <Text style={styles.instructionsText}>
                  {isProcessing ? 'Processing with AI...' : 
                   isTimerActive ? `Taking photo in ${timerCount}...` :
                   'Point camera at object to identify'}
                </Text>
                {isProcessing && (
                  <ActivityIndicator 
                    size="small" 
                    color="white" 
                    style={{ marginTop: 8 }} 
                  />
                )}
              </View>
            </View>
          </CameraView>

          {/* Top Controls Overlay */}
          <View style={styles.topControls}>
            <TouchableOpacity
              style={[styles.topControlButton, { backgroundColor: getOverlayColor() }]}
              onPress={toggleFlash}
            >
              <IconSymbol size={20} name={getFlashIcon() as any} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.topControlButton, { backgroundColor: getOverlayColor() }]}
              onPress={toggleGrid}
            >
              <IconSymbol 
                size={20} 
                name="grid" 
                color={settings.gridLines ? Colors[colorScheme ?? 'light'].tint : "white"} 
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.topControlButton, { backgroundColor: getOverlayColor() }]}
              onPress={changeTimer}
            >
              <IconSymbol 
                size={20} 
                name={getTimerIcon() as any}
                color={settings.timer > 0 ? Colors[colorScheme ?? 'light'].tint : "white"} 
              />
              {settings.timer > 0 && (
                <Text style={styles.timerBadge}>{settings.timer}</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Recent Scans Preview */}
          {recentScans.length > 0 && (
            <View style={[styles.recentScansContainer, { backgroundColor: getOverlayColor() }]}>
              <Text style={styles.recentScansTitle}>Recent</Text>
              <View style={styles.recentScansGrid}>
                {recentScans.map((uri, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.recentScanItem,
                      { borderColor: Colors[colorScheme ?? 'light'].tint }
                    ]}
                  >
                    <View style={styles.recentScanPlaceholder}>
                      <IconSymbol size={16} name="photo" color="white" />
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </View>

        {/* Enhanced Controls */}
        <View style={[styles.controls, { backgroundColor: getOverlayColor() }]}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: getCardColor() + '20' }]}
            onPress={pickImageFromGallery}
            disabled={isProcessing || isTimerActive}
          >
            <IconSymbol size={28} name="photo.fill" color="white" />
            <Text style={styles.controlButtonLabel}>Gallery</Text>
          </TouchableOpacity>

          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <TouchableOpacity
              style={[
                styles.captureButton, 
                { 
                  backgroundColor: isProcessing || isTimerActive ? 
                    Colors[colorScheme ?? 'light'].text + '40' : 
                    Colors[colorScheme ?? 'light'].tint,
                  borderColor: 'white',
                  borderWidth: 4,
                }
              ]}
              onPress={startTimer}
              disabled={isProcessing || isTimerActive}
            >
              {isProcessing ? (
                <ActivityIndicator size="large" color="white" />
              ) : isTimerActive ? (
                <IconSymbol size={32} name="pause.fill" color="white" />
              ) : (
                <IconSymbol size={40} name="camera.fill" color="white" />
              )}
            </TouchableOpacity>
          </Animated.View>

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: getCardColor() + '20' }]}
            onPress={toggleCameraFacing}
            disabled={isProcessing || isTimerActive}
          >
            <IconSymbol size={28} name="arrow.triangle.2.circlepath" color="white" />
            <Text style={styles.controlButtonLabel}>Flip</Text>
          </TouchableOpacity>
        </View>

        {/* Settings Modal */}
        <Modal
          visible={showSettings}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowSettings(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.settingsModal, { backgroundColor: getCardColor() }]}>
              <View style={styles.modalHeader}>
                <ThemedText type="subtitle">Camera Settings</ThemedText>
                <TouchableOpacity onPress={() => setShowSettings(false)}>
                  <IconSymbol size={24} name="xmark" color={Colors[colorScheme ?? 'light'].text} />
                </TouchableOpacity>
              </View>

              <View style={styles.settingSection}>
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <IconSymbol size={20} name="bolt.fill" color={Colors[colorScheme ?? 'light'].text} />
                    <ThemedText style={styles.settingLabel}>Flash</ThemedText>
                  </View>
                  <View style={styles.settingButtons}>
                    {['off', 'on', 'auto'].map((mode) => (
                      <TouchableOpacity
                        key={mode}
                        style={[
                          styles.settingButton,
                          {
                            backgroundColor: settings.flash === mode ? 
                              Colors[colorScheme ?? 'light'].tint : 'transparent',
                            borderColor: Colors[colorScheme ?? 'light'].tint,
                          }
                        ]}
                        onPress={() => setSettings(prev => ({ ...prev, flash: mode as any }))}
                      >
                        <ThemedText style={[
                          styles.settingButtonText,
                          { color: settings.flash === mode ? 'white' : Colors[colorScheme ?? 'light'].text }
                        ]}>
                          {mode.charAt(0).toUpperCase() + mode.slice(1)}
                        </ThemedText>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <IconSymbol size={20} name="timer" color={Colors[colorScheme ?? 'light'].text} />
                    <ThemedText style={styles.settingLabel}>Timer</ThemedText>
                  </View>
                  <View style={styles.settingButtons}>
                    {[0, 3, 10].map((seconds) => (
                      <TouchableOpacity
                        key={seconds}
                        style={[
                          styles.settingButton,
                          {
                            backgroundColor: settings.timer === seconds ? 
                              Colors[colorScheme ?? 'light'].tint : 'transparent',
                            borderColor: Colors[colorScheme ?? 'light'].tint,
                          }
                        ]}
                        onPress={() => setSettings(prev => ({ ...prev, timer: seconds as any }))}
                      >
                        <ThemedText style={[
                          styles.settingButtonText,
                          { color: settings.timer === seconds ? 'white' : Colors[colorScheme ?? 'light'].text }
                        ]}>
                          {seconds === 0 ? 'Off' : `${seconds}s`}
                        </ThemedText>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <IconSymbol size={20} name="grid" color={Colors[colorScheme ?? 'light'].text} />
                    <ThemedText style={styles.settingLabel}>Grid Lines</ThemedText>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.toggleButton,
                      { backgroundColor: settings.gridLines ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].text + '20' }
                    ]}
                    onPress={toggleGrid}
                  >
                    <View style={[
                      styles.toggleIndicator,
                      {
                        backgroundColor: 'white',
                        transform: [{ translateX: settings.gridLines ? 20 : 0 }],
                      }
                    ]} />
                  </TouchableOpacity>
                </View>

                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <IconSymbol size={20} name="info.circle" color={Colors[colorScheme ?? 'light'].text} />
                    <View>
                      <ThemedText style={styles.settingLabel}>AI Tips</ThemedText>
                      <ThemedText style={styles.settingDescription}>
                        Get better results with good lighting and clear focus
                      </ThemedText>
                    </View>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
                onPress={() => setShowSettings(false)}
              >
                <ThemedText style={styles.closeButtonText}>Done</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionContainer: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 24,
    marginBottom: 40,
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
  },
  headerSubtitle: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  topControls: {
    position: 'absolute',
    top: 100,
    right: 20,
    gap: 12,
    zIndex: 5,
  },
  topControlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  timerBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#FF6B6B',
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
    width: 16,
    height: 16,
    borderRadius: 8,
    textAlign: 'center',
    lineHeight: 16,
  },
  recentScansContainer: {
    position: 'absolute',
    bottom: 140,
    left: 20,
    borderRadius: 12,
    padding: 12,
    zIndex: 5,
  },
  recentScansTitle: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
  },
  recentScansGrid: {
    flexDirection: 'row',
    gap: 8,
  },
  recentScanItem: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  recentScanPlaceholder: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  gridOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    height: 1,
    width: '100%',
    top: '33.33%',
  },
  verticalGrid: {
    width: 1,
    height: '100%',
    top: 0,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  scanFrame: {
    width: width * 0.75,
    height: width * 0.75,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scanLine: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 2,
    borderRadius: 1,
  },
  focusIndicator: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderWidth: 2,
    borderRadius: 25,
    backgroundColor: 'transparent',
  },
  timerOverlay: {
    position: 'absolute',
    top: '20%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timerText: {
    fontSize: 72,
    fontWeight: '900',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  instructionsContainer: {
    position: 'absolute',
    bottom: 60,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  instructionsText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    overflow: 'hidden',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 40,
    paddingVertical: 30,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  controlButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    gap: 4,
  },
  controlButtonLabel: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  captureButton: {
    width: 90,
    height: 90,
    borderRadius: 45,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'flex-end',
  },
  settingsModal: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    maxHeight: height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  settingSection: {
    gap: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  settingDescription: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  settingButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  settingButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
  },
  settingButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  toggleButton: {
    width: 44,
    height: 24,
    borderRadius: 12,
    padding: 2,
    justifyContent: 'center',
  },
  toggleIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  closeButton: {
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});