What is RevenueCat?
RevenueCat provides a backend and a wrapper around StoreKit and Google Play Billing to make implementing in-app purchases and subscriptions easy. With our SDK, you can build and manage your app business on any platform without having to maintain IAP infrastructure. You can read more about how RevenueCat fits into your app or you can sign up free to start building.

Introduction
Expo is a framework for building React Native apps. It's a popular choice for rapidly iterating on your app, while letting Expo take care of all the platform-specific code.

To use and test RevenueCat with Expo, you'll need to create an Expo development build. Follow the instructions below and learn more about Expo development builds here.

This guide is specific to Expo, but you may also find our React Native guide useful.

Create an Expo development build
Install Expo CLI and development dependencies
Start by installing Expo CLI:

npm install expo -g

and then install Expo DevClient:

npx expo install expo-dev-client

Get started with an Expo project
Either create a new Expo project with the following command:

npx create-expo-app@latest

or use an existing Expo project. Ensure you're in the project directory for the next steps:

cd <expo-project-directory>

Install RevenueCat's SDKs
Install RevenueCat's react-native-purchases for core functionality and react-native-purchases-ui for UI components like Paywalls, Customer Center, and more.

Either run:

npx expo install react-native-purchases react-native-purchases-ui

or update your package.json with the latest package versions:

{
  "dependencies": {
    "react-native-purchases": "latest_version",
    "react-native-purchases-ui": "latest_version"
  }
}

After installing RevenueCat's SDKs, you must run the full build process as described below in the Testing your app section to ensure all dependencies are installed. Hot reloading without building will result in errors, such as:

Invariant Violation: `new NativeEventEmitter()` requires a non-null argument.

RevenueCat Dashboard Configuration
Configure a new project
RevenueCat projects are top-level containers for your apps, products, entitlements, paywalls, and more. If you don't already have a RevenueCat project for your app, create one here.

Connect to a Store (Apple, Google, Web, etc.)
Depending on which platform you're building for, you'll need to connect your RevenueCat project to one, or multiple, stores. Set up your project's supported stores here.

Add Products
For each store you're supporting, you'll need to add the products you plan on offering to your customers. Set up your products for each store here.

Create an Entitlement
An entitlement represents a level of access, features, or content that a customer is "entitled" to. When customers purchase a product, they're granted an entitlement. Create an entitlement here. Then, attach your products to your new entitlement.

Create an Offering
An offering is a collection of products that are "offered" to your customers on your paywall. Create an offering for your products here.

Configure a Paywall
A paywall is where your customers can purchase your products. RevenueCat's Paywalls allow you to remotely build and configure your paywall without any code changes or app updates. Create a paywall here.

RevenueCat SDK Configuration
Initialize the SDK
Once you've installed the RevenueCat SDK, you'll need to configure it. Add the following code to the entry point of your app and be sure to replace <revenuecat_project_platform_api_key> with your project's API keys.

More information about configuring the SDK can be found here.

Expo
import { Platform, useEffect } from 'react-native';
import { useEffect } from 'react';
import Purchases, { LOG_LEVEL } from 'react-native-purchases';

//...

export default function App() {

  useEffect(() => {
    Purchases.setLogLevel(LOG_LEVEL.VERBOSE);

    if (Platform.OS === 'ios') {
       Purchases.configure({apiKey: <revenuecat_project_apple_api_key>});
    } else if (Platform.OS === 'android') {
       Purchases.configure({apiKey: <revenuecat_project_google_api_key>});

      // OR: if building for Amazon, be sure to follow the installation instructions then:
       Purchases.configure({ apiKey: <revenuecat_project_amazon_api_key>, useAmazon: true });
    }

  }, []);
}


Identify a user and check subscription status
RevenueCat is the single source of truth for your customer's subscription status across all platforms. Learn more about the different ways to identify your customers to RevenueCat here.

Then, check the customer's subscription status by fetching the CustomerInfo object:

Expo
try {
  const customerInfo = await Purchases.getCustomerInfo();
  // access latest customerInfo
} catch (e) {
 // Error fetching customer info
}


and inspecting the entitlements object to see if the customer is subscribed to your entitlement:

Expo
if(typeof customerInfo.entitlements.active[<my_entitlement_identifier>] !== "undefined") {
  // Grant user "pro" access
}


Present a paywall
If the customer is not subscribed to your entitlement, you can present a paywall to them where they can purchase your products.

There are several ways to present a paywall in Expo, each with different use cases, so please review the React Native Paywalls documentation.

Testing your app
To test, we'll use EAS to build the app for the simulator. You'll need to sign up at expo.dev and use the account below.

For more information about EAS, see the EAS docs.

You can also follow these instructions on Expo's docs: https://docs.expo.dev/tutorial/eas/configure-development-build/#initialize-a-development-build

Get started by installing the EAS-CLI:

npm install -g eas-cli

Then login to EAS:

eas login

After logging in, initialize the EAS configuration:

eas init

Then run the following command, which will prompt you to select the platforms you'd like to configure for EAS Build.

eas build:configure

Testing on iOS simulator
Next, you'll need to update eas.json with the simulator build profile as described here.

Your eas.json file might look like this:

{
  "cli": {
    "version": ">= 7.3.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal"
    },
    "production": {},
    "ios-simulator": {
      "extends": "development",
      "ios": {
        "simulator": true
      }
    }
  },
  "submit": {
    "production": {}
  }
}

Next, build the app for the simulator:

eas build --platform ios --profile ios-simulator

Building creates a container app with your installed dependencies. Once the build completes and you run it on the device (or simulator, in this case), the app will hot reload with your local changes during development.

Enter your app's bundle ID, matching your RevenueCat config and App Store Connect. Once the build completes, Expo will ask if you want to open the app in the simulator. Choose yes, and it'll launch the simulator with your app.

After your app is running, you'll need to start the Expo server:

npx expo start

Finally, choose the local development server in the iOS simulator.

Testing on Android device or emulator
To test on an Android device, you'll need to build the app for a physical device or Android emulator as described here.

Please ensure that developmentClient in your eas.json file is set to true under the build.development profile. Then, build the app:

eas build --platform android --profile development

Enter your app's application ID matches your RevenueCat config and Google Play Console. Choose "Yes" when asked if you want to create a new Android Keystore.

Once the build completes, you can run the application on the device or emulator. To run the app on an Android device, install Expo Orbit, connect your device to your computer, and select your device from the Orbit menu. Alternatively, use the provided QR code method.

To run the app on an Android emulator, choose "Yes" in the terminal after the build completes.

After the app is running, you'll need to start the Expo server:

npx expo start

Expo Go
Expo Go is a sandbox that allows you to rapidly prototype your app. While it doesn’t support running custom native code—such as the native modules required for in-app purchases—react-native-purchases includes a built-in Preview API Mode specifically for Expo Go.

When your app runs inside Expo Go, react-native-purchases automatically detects the environment and replaces native calls with JavaScript-level mock APIs. This allows your app to load and execute all subscription-related logic without errors, even though real purchases will not function in this mode.

This means you can still preview subscription UIs, test integration flows, and continue development without needing to build a custom development client immediately.

However, to fully test in-app purchases and access real RevenueCat functionality, you must use a development build.
RevenueCat provides a backend and a wrapper around StoreKit and Google Play Billing to make implementing in-app purchases and subscriptions easy. With our SDK, you can build and manage your app business on any platform without having to maintain IAP infrastructure. You can read more about how RevenueCat fits into your app or you can sign up free to start building.

Installation
Release

Make sure that the deployment target for iOS is at least 13.4 and Android is at least 6.0 (API 23) as defined here.

React Native package
Purchases for React Native can be installed either via npm or yarn.

We recommend using the latest version of React Native, or making sure that the version is at least greater than 0.64.

Option 1.1: Using auto-linking
Recent versions of React Native will automatically link the SDK, so all that's needed is to install the library.

npm
yarn
npm install --save react-native-purchases


Option 1.2: Manual linking
npm
yarn
npm install --save react-native-purchases


After that, you should link the library to the native projects by doing:

Shell
react-native link react-native-purchases


Using Expo
Use Expo to rapidly iterate on your app by using JavaScript/TypeScript exclusively, while letting Expo take care of everything else.

See Using RevenueCat with Expo to get started.

Set the correct launchMode for Android
Depending on your user's payment method, they may be asked by Google Play to verify their purchase in their (banking) app. This means they will have to background your app and go to another app to verify the purchase. If your Activity's launchMode is set to anything other than standard or singleTop, backgrounding your app can cause the purchase to get cancelled. To avoid this, set the launchMode of your Activity to standard or singleTop in your Android app's AndroidManifest.xml file, like so:

AndroidManifest.xml
<activity 
    android:name="com.your.Activity"
    android:launchMode="standard" />  <!-- or singleTop -->


You can find Android's documentation on the various launchMode options here.

Import Purchases
You should now be able to import Purchases.

React Native
import Purchases from 'react-native-purchases';


📘Include BILLING permission for Android projects
Don't forget to include the BILLING permission in your AndroidManifest.xml file

AndroidManifest.xml
<uses-permission android:name="com.android.vending.BILLING" />


📘Enable In-App Purchase capability for your iOS project
Don't forget to enable the In-App Purchase capability for your project under Project Target -> Capabilities -> In-App Purchase

Android Build Issues
R8 Dependencies Conflict
If you encounter build failures related to R8 (Android's code shrinker) when using react-native-purchases-ui, you may see errors like:

Execution failed for task ':app:mergeExtDexDevDebug'.
> Could not resolve all files for configuration ':app:devDebugRuntimeClasspath'.

This issue occurs due to a bug in earlier versions of Android Gradle Plugin (AGP) that affects R8 dependency resolution. To fix this, add the following to your project-level build.gradle file (not app/build.gradle):

build.gradle (project level)
buildscript {
    repositories {
        mavenCentral()
        maven {
            url = uri("https://storage.googleapis.com/r8-releases/raw")
        }
    }
    dependencies {
        classpath("com.android.tools:r8:8.1.44")
    }
} 


This solution forces the use of a specific R8 version that resolves the dependency conflicts. For more details, see the Google Issue Tracker.