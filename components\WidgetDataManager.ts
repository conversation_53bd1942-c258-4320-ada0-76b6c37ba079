import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthService from './AuthService';
import NotificationManager from './NotificationManager';
import SyncService from './SyncService';

export interface WidgetData {
  // Quick Scan Widget Data
  todayScans: number;
  remainingScans: number;
  isUnlimited: boolean;
  
  // Todo Widget Data
  pendingTasks: number;
  nextTask: string;
  nextTaskDue: Date;
  completedToday: number;
  totalTasks: number;
  
  // Stats Widget Data
  streak: number;
  totalSpecies: number;
  weeklyGoal: number;
  weeklyProgress: number;
  favoriteCategory: string;
  
  // Subscription Widget Data
  subscriptionTier: string | null;
  isSubscriptionActive: boolean;
  daysRemaining: number;
  isTrialActive: boolean;
  trialDaysRemaining: number;
  
  // Daily Goals Data
  scanGoal: number;
  scanProgress: number;
  waterGoal: number;
  waterProgress: number;
  streakGoal: number;
  
  lastUpdated: Date;
}

export interface TodoTask {
  id: string;
  text: string;
  completed: boolean;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high';
  reminder?: Date;
  createdAt: Date;
}

/**
 * Widget Data Manager
 * Manages data sharing between the main app and widgets
 * Handles notifications for tasks and reminders
 */
export class WidgetDataManager {
  private static instance: WidgetDataManager;
  private notificationManager: NotificationManager;
  private syncService: SyncService;
  private authService: AuthService;

  static getInstance(): WidgetDataManager {
    if (!WidgetDataManager.instance) {
      WidgetDataManager.instance = new WidgetDataManager();
    }
    return WidgetDataManager.instance;
  }

  constructor() {
    this.notificationManager = NotificationManager.getInstance();
    this.syncService = SyncService.getInstance();
    this.authService = AuthService.getInstance();
  }

  /**
   * Update widget data and refresh all widgets
   */
  async updateWidgetData(data: Partial<WidgetData>): Promise<void> {
    try {
      const currentData = await this.getWidgetData();
      const updatedData: WidgetData = {
        ...currentData,
        ...data,
        lastUpdated: new Date(),
      };

      // Store data for widgets
      await AsyncStorage.setItem('widget_data', JSON.stringify(updatedData));

      // Update shared storage for iOS widgets (if available)
      if (typeof window !== 'undefined' && (window as any).webkit?.messageHandlers?.widgetData) {
        (window as any).webkit.messageHandlers.widgetData.postMessage(updatedData);
      }

      // Trigger widget refresh (platform specific)
      this.refreshWidgets();

      // Sync to cloud if user is signed in
      if (this.authService.isSignedIn() && !this.authService.isAnonymous()) {
        this.syncService.syncToCloud().catch(error => {
          console.warn('Failed to sync widget data to cloud:', error);
        });
      }

    } catch (error) {
      console.error('Failed to update widget data:', error);
    }
  }

  /**
   * Get current widget data
   */
  async getWidgetData(): Promise<WidgetData> {
    try {
      const stored = await AsyncStorage.getItem('widget_data');
      if (stored) {
        const data = JSON.parse(stored);
        // Convert date strings back to Date objects
        data.lastUpdated = new Date(data.lastUpdated);
        if (data.nextTaskDue) data.nextTaskDue = new Date(data.nextTaskDue);
        return data;
      }
    } catch (error) {
      console.error('Failed to get widget data:', error);
    }

    // Return default data
    return {
      todayScans: 0,
      remainingScans: 0, // No free tier - require subscription
      isUnlimited: false,
      pendingTasks: 0,
      nextTask: '',
      nextTaskDue: new Date(),
      completedToday: 0,
      totalTasks: 0,
      streak: 0,
      totalSpecies: 0,
      weeklyGoal: 25,
      weeklyProgress: 0,
      favoriteCategory: 'Plants',
      subscriptionTier: null,
      isSubscriptionActive: false,
      daysRemaining: 0,
      isTrialActive: false,
      trialDaysRemaining: 0,
      scanGoal: 5,
      scanProgress: 0,
      waterGoal: 8,
      waterProgress: 0,
      streakGoal: 7,
      lastUpdated: new Date(),
    };
  }

  /**
   * Update scan statistics
   */
  async updateScanStats(scansToday: number, totalSpecies: number, streak: number): Promise<void> {
    const subscription = await this.getSubscriptionStatus();

    await this.updateWidgetData({
      todayScans: scansToday,
      remainingScans: (subscription.isActive || subscription.isTrialActive) ? -1 : 0,
      isUnlimited: subscription.isActive || subscription.isTrialActive,
      totalSpecies,
      streak,
      scanProgress: scansToday,
    });
  }

  /**
   * Update subscription status
   */
  async updateSubscriptionStatus(subscription: any): Promise<void> {
    await this.updateWidgetData({
      subscriptionTier: subscription.tier,
      isSubscriptionActive: subscription.isActive,
      daysRemaining: subscription.daysRemaining || 0,
      isTrialActive: subscription.isTrialActive || false,
      trialDaysRemaining: subscription.trialDaysRemaining || 0,
      isUnlimited: subscription.isActive,
      remainingScans: subscription.isActive ? -1 : await this.getRemainingScans(),
    });
  }

  /**
   * Add or update a todo task with notification scheduling
   */
  async addTodoTask(task: TodoTask): Promise<void> {
    try {
      const tasks = await this.getTodoTasks();
      const updatedTasks = [...tasks.filter(t => t.id !== task.id), task];
      
      await AsyncStorage.setItem('todo_tasks', JSON.stringify(updatedTasks));
      
      // Schedule notification if reminder is set
      if (task.reminder && task.reminder > new Date()) {
        await this.notificationManager.scheduleTaskReminder({
          id: `task_${task.id}`,
          title: '📋 Task Reminder',
          body: task.text,
          scheduledTime: task.reminder,
          taskId: task.id,
          type: 'task',
          data: { priority: task.priority },
        });
      }

      // Update widget data
      await this.updateTodoWidgetData();
      
    } catch (error) {
      console.error('Failed to add todo task:', error);
    }
  }

  /**
   * Complete a todo task
   */
  async completeTodoTask(taskId: string): Promise<void> {
    try {
      const tasks = await this.getTodoTasks();
      const updatedTasks = tasks.map(task => 
        task.id === taskId ? { ...task, completed: true } : task
      );
      
      await AsyncStorage.setItem('todo_tasks', JSON.stringify(updatedTasks));
      
      // Cancel any pending notifications for this task
      await this.notificationManager.cancelTaskNotifications(taskId);
      
      // Update widget data
      await this.updateTodoWidgetData();
      
    } catch (error) {
      console.error('Failed to complete todo task:', error);
    }
  }

  /**
   * Get all todo tasks
   */
  async getTodoTasks(): Promise<TodoTask[]> {
    try {
      const stored = await AsyncStorage.getItem('todo_tasks');
      if (stored) {
        const tasks = JSON.parse(stored);
        return tasks.map((task: any) => ({
          ...task,
          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
          reminder: task.reminder ? new Date(task.reminder) : undefined,
          createdAt: new Date(task.createdAt),
        }));
      }
    } catch (error) {
      console.error('Failed to get todo tasks:', error);
    }
    return [];
  }

  /**
   * Update todo widget data based on current tasks
   */
  private async updateTodoWidgetData(): Promise<void> {
    const tasks = await this.getTodoTasks();
    const pendingTasks = tasks.filter(t => !t.completed);
    const completedToday = tasks.filter(t => 
      t.completed && 
      new Date(t.createdAt).toDateString() === new Date().toDateString()
    );

    // Find next task (earliest due date or highest priority)
    const nextTask = pendingTasks
      .sort((a, b) => {
        if (a.dueDate && b.dueDate) {
          return a.dueDate.getTime() - b.dueDate.getTime();
        }
        if (a.dueDate) return -1;
        if (b.dueDate) return 1;
        
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      })[0];

    await this.updateWidgetData({
      pendingTasks: pendingTasks.length,
      nextTask: nextTask?.text || 'No pending tasks',
      nextTaskDue: nextTask?.dueDate || new Date(),
      completedToday: completedToday.length,
      totalTasks: tasks.length,
    });
  }

  /**
   * Schedule daily reminders
   */
  async scheduleDailyReminders(): Promise<void> {
    // Schedule daily scan reminder
    await this.notificationManager.scheduleDailyScanReminder(10, 0); // 10:00 AM
    
    // Schedule daily goal reminders
    await this.notificationManager.scheduleDailyGoalReminder('scans', 
      new Date(Date.now() + 6 * 60 * 60 * 1000) // 6 hours from now
    );
  }

  /**
   * Get subscription status
   */
  private async getSubscriptionStatus(): Promise<{ isActive: boolean; isTrialActive?: boolean; tier?: string }> {
    try {
      const stored = await AsyncStorage.getItem('subscription_status');
      return stored ? JSON.parse(stored) : { isActive: false, isTrialActive: false };
    } catch {
      return { isActive: false, isTrialActive: false };
    }
  }

  /**
   * Get remaining scans (no free tier - require subscription)
   */
  private async getRemainingScans(): Promise<number> {
    const subscription = await this.getSubscriptionStatus();
    return (subscription.isActive || subscription.isTrialActive) ? -1 : 0;
  }

  /**
   * Refresh widgets (platform specific)
   */
  private refreshWidgets(): void {
    // For iOS, this would trigger WidgetKit refresh
    // For Android, this would update app widgets
    console.log('Refreshing widgets with updated data');
  }
}

export default WidgetDataManager;
