import { useSubscription } from '@/components/SubscriptionContext';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

interface SubscriptionTier {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  period: string;
  features: string[];
  popular?: boolean;
  color: string;
  savings?: string;
  trial?: string;
  badge?: string;
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    id: 'monthly',
    name: 'Monthly',
    price: '$19.99',
    period: 'per month',
    color: '#3B82F6',
    features: [
      'Unlimited scans',
      'Advanced AI identification',
      'Full history access',
      'Offline mode',
      'Priority support',
      'No ads',
      'Export data',
      'Cloud sync'
    ]
  },
  {
    id: 'yearly',
    name: 'Yearly',
    price: '$29.99',
    originalPrice: '$35.88',
    period: 'per year',
    popular: true,
    color: '#10B981',
    savings: 'Save 17%',
    trial: '3-day free trial',
    badge: 'Best Value',
    features: [
      'Everything in Monthly',
      'Extended offline mode (30 days)',
      'Advanced analytics',
      'Custom collections',
      'API access',
      'Early feature access',
      'Premium support',
      '3-day free trial'
    ]
  }
];

export default function SubscriptionScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { subscribe, restorePurchases, subscription, isLoading } = useSubscription();
  const [selectedTier, setSelectedTier] = useState<string>('yearly');
  const [isSubscribing, setIsSubscribing] = useState(false);

  // Helper functions for colors
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const getBorderColor = () => {
    return colorScheme === 'dark' ? '#3C3C3E' : '#E5E5EA';
  };

  const handleSubscribe = async (tierId: string) => {
    const tier = subscriptionTiers.find(t => t.id === tierId);
    if (!tier) return;

    setIsSubscribing(true);

    try {
      const trialText = tier.trial ? `\n\n${tier.trial} included - you won't be charged until the trial ends.` : '';
      const savingsText = tier.savings ? `\n\nYou'll ${tier.savings.toLowerCase()} compared to monthly billing.` : '';

      Alert.alert(
        'Subscribe to ' + tier.name,
        `You selected the ${tier.name} plan for ${tier.price} ${tier.period}.${trialText}${savingsText}`,
        [
          { text: 'Cancel', style: 'cancel', onPress: () => setIsSubscribing(false) },
          {
            text: tier.trial ? 'Start Free Trial' : 'Subscribe',
            onPress: async () => {
              const success = await subscribe(tierId as any);
              if (success) {
                const successMessage = tier.trial
                  ? `Free trial started! You have ${tier.trial} to explore all features.`
                  : 'Subscription activated! Welcome to premium features.';
                Alert.alert('Success', successMessage, [
                  { text: 'OK', onPress: () => router.back() }
                ]);
              } else {
                Alert.alert('Error', 'Failed to process subscription. Please try again.');
              }
              setIsSubscribing(false);
            }
          }
        ]
      );
    } catch (error) {
      console.error('Subscription error:', error);
      Alert.alert('Error', 'Failed to process subscription. Please try again.');
      setIsSubscribing(false);
    }
  };

  const handleRestore = async () => {
    try {
      const success = await restorePurchases();
      if (success) {
        Alert.alert('Success', 'Purchases restored successfully!');
      } else {
        Alert.alert('No Purchases Found', 'No previous purchases were found to restore.');
      }
    } catch (error) {
      console.error('Restore error:', error);
      Alert.alert('Error', 'Failed to restore purchases. Please try again.');
    }
  };

  const renderTierCard = (tier: SubscriptionTier) => {
    const isSelected = selectedTier === tier.id;

    return (
      <TouchableOpacity
        key={tier.id}
        style={[
          styles.tierCard,
          { backgroundColor: getCardColor() },
          isSelected && { borderColor: tier.color, borderWidth: 2 },
          tier.popular && styles.popularCard
        ]}
        onPress={() => setSelectedTier(tier.id)}
      >
        {(tier.popular || tier.badge) && (
          <View style={[styles.popularBadge, { backgroundColor: tier.color }]}>
            <Text style={styles.popularText}>{tier.badge || 'Most Popular'}</Text>
          </View>
        )}

        <View style={styles.tierHeader}>
          <ThemedText style={styles.tierName}>{tier.name}</ThemedText>

          {tier.trial && (
            <View style={[styles.trialBadge, { backgroundColor: tier.color + '20' }]}>
              <Text style={[styles.trialText, { color: tier.color }]}>{tier.trial}</Text>
            </View>
          )}

          <View style={styles.priceContainer}>
            {tier.originalPrice && (
              <Text style={[styles.originalPrice, { color: Colors[colorScheme ?? 'light'].text }]}>
                {tier.originalPrice}
              </Text>
            )}
            <ThemedText style={[styles.price, { color: tier.color }]}>
              {tier.price}
            </ThemedText>
            <ThemedText style={styles.period}>
              {tier.period}
            </ThemedText>
            {tier.savings && (
              <View style={[styles.savingsBadge, { backgroundColor: '#10B981' }]}>
                <Text style={styles.savingsText}>{tier.savings}</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.featuresContainer}>
          {tier.features.map((feature, index) => (
            <View key={index} style={styles.featureRow}>
              <IconSymbol
                size={16}
                name="checkmark.circle.fill"
                color={tier.color}
              />
              <ThemedText style={styles.featureText}>
                {feature}
              </ThemedText>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.selectButton,
            { backgroundColor: isSelected ? tier.color : getBorderColor() },
            isSubscribing && { opacity: 0.6 }
          ]}
          onPress={() => handleSubscribe(tier.id)}
          disabled={isSubscribing}
        >
          <Text style={[
            styles.selectButtonText,
            { color: isSelected ? 'white' : Colors[colorScheme ?? 'light'].text }
          ]}>
            {isSubscribing && isSelected
              ? 'Processing...'
              : tier.trial && isSelected
                ? 'Start Free Trial'
                : isSelected
                  ? 'Subscribe Now'
                  : 'Select Plan'
            }
          </Text>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.back()}
        >
          <IconSymbol size={24} name="arrow.left" color={Colors[colorScheme ?? 'light'].text} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>Subscription</ThemedText>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Current Subscription Status */}
        {subscription.isActive && (
          <ThemedView style={[styles.statusCard, { backgroundColor: getCardColor() }]}>
            <View style={styles.statusHeader}>
              <IconSymbol size={24} name="checkmark.circle.fill" color="#10B981" />
              <ThemedText style={styles.statusTitle}>Active Subscription</ThemedText>
            </View>
            <ThemedText style={styles.statusText}>
              {subscription.tier === 'yearly' ? 'Yearly Plan' : 'Monthly Plan'}
            </ThemedText>
            {subscription.isTrialActive && (
              <ThemedText style={[styles.trialStatus, { color: '#F59E0B' }]}>
                Trial ends in {subscription.trialEndDate ? Math.ceil((subscription.trialEndDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0} days
              </ThemedText>
            )}
          </ThemedView>
        )}

        {/* Hero Section */}
        <LinearGradient
          colors={[Colors[colorScheme ?? 'light'].tint, Colors[colorScheme ?? 'light'].tint + 'CC']}
          style={styles.heroSection}
        >
          <IconSymbol size={64} name="crown.fill" color="white" />
          <Text style={styles.heroTitle}>
            {subscription.isActive ? 'Manage Your Plan' : 'Choose Your Plan'}
          </Text>
          <Text style={styles.heroSubtitle}>
            {subscription.isActive
              ? 'Upgrade or change your subscription plan'
              : 'Unlock unlimited scans, advanced AI, and premium features'
            }
          </Text>
        </LinearGradient>

        {/* Subscription Tiers */}
        <View style={styles.tiersContainer}>
          <ThemedText style={styles.sectionTitle}>Choose Your Plan</ThemedText>
          {subscriptionTiers.map(renderTierCard)}
        </View>

        {/* Features Comparison */}
        <ThemedView style={[styles.comparisonSection, { backgroundColor: getCardColor() }]}> {/* Changed from Colors[colorScheme ?? 'light'].card */}
          <ThemedText style={styles.comparisonTitle}>Why Upgrade?</ThemedText>
          <View style={styles.comparisonGrid}>
            <View style={styles.comparisonItem}>
              <IconSymbol size={24} name="infinity" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.comparisonText}>Unlimited Scans</ThemedText>
            </View>
            <View style={styles.comparisonItem}>
              <IconSymbol size={24} name="brain.head.profile" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.comparisonText}>Advanced AI</ThemedText>
            </View>
            <View style={styles.comparisonItem}>
              <IconSymbol size={24} name="icloud.and.arrow.down" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.comparisonText}>Offline Mode</ThemedText>
            </View>
            <View style={styles.comparisonItem}>
              <IconSymbol size={24} name="chart.bar.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.comparisonText}>Analytics</ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* Footer Actions */}
        <View style={styles.footerActions}>
          <TouchableOpacity
            style={[styles.restoreButton, { backgroundColor: getCardColor() }]} // Changed from Colors[colorScheme ?? 'light'].card
            onPress={handleRestore}
          >
            <ThemedText style={styles.restoreText}>Restore Purchases</ThemedText>
          </TouchableOpacity>
          
          <ThemedText style={styles.disclaimerText}>
            Subscriptions auto-renew unless cancelled. Free trial converts to paid subscription unless cancelled before trial ends. Cancel anytime in your account settings.
          </ThemedText>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  statusCard: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  statusText: {
    fontSize: 14,
    opacity: 0.8,
  },
  trialStatus: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 20,
  },
  heroTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  heroSubtitle: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
    marginTop: 8,
    textAlign: 'center',
  },
  tiersContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  tierCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'transparent',
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  popularCard: {
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    right: 20,
    paddingVertical: 6,
    borderRadius: 12,
    alignItems: 'center',
  },
  popularText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  tierHeader: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 8,
  },
  tierName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  priceContainer: {
    alignItems: 'center',
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  period: {
    fontSize: 14,
    opacity: 0.7,
  },
  originalPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
    opacity: 0.6,
    marginBottom: 4,
  },
  trialBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 8,
  },
  trialText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  savingsBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 4,
  },
  savingsText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  selectButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  selectButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  comparisonSection: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  comparisonTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  comparisonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  comparisonItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  comparisonText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  footerActions: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  restoreButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  restoreText: {
    fontSize: 16,
    fontWeight: '500',
  },
  disclaimerText: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 16,
  },
});
