import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useScaledTextStyle } from '@/hooks/useTextStyle';

export default function TestTextComponent() {
  const { textSizeMultiplier, getScaledFontSize } = useScaledTextStyle();

  return (
    <View style={styles.container}>
      <Text style={[styles.text, { fontSize: getScaledFontSize(16) }]}>
        This text scales with your settings
      </Text>
      <Text style={styles.debug}>
        Current multiplier: {textSizeMultiplier.toFixed(2)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    margin: 16,
  },
  text: {
    fontFamily: 'Inter-Regular',
    color: '#333',
  },
  debug: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
});