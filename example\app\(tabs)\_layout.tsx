// app/(tabs)/_layout.tsx
import { Tabs } from 'expo-router';
import { View, Platform } from 'react-native';
import { BlurView } from 'expo-blur';
import {
  Camera,
  Compass,
  Settings,
  ClipboardList,
  Sparkles
} from 'lucide-react-native';
import { useTheme } from '@/components/ThemeContext';
import { AdaptiveNavigation } from '@/components/AdaptiveNavigation';

export default function TabLayout() {
  const { colors } = useTheme();

  return (
    <AdaptiveNavigation> {/* This wrapper is correct */}
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: {
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: Platform.OS === 'ios' ? 90 : 70,
            paddingBottom: Platform.OS === 'ios' ? 25 : 10,
            paddingTop: 10,
            borderTopWidth: 0,
            backgroundColor: Platform.OS === 'web' ? colors.blur : 'transparent',
            backdropFilter: Platform.OS === 'web' ? 'blur(20px)' : undefined,
          },
          tabBarBackground: Platform.OS !== 'web' ? () => (
            <BlurView
              intensity={100}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
              }}
            />
          ) : undefined,
          tabBarActiveTintColor: colors.primary,
          tabBarInactiveTintColor: colors.textSecondary,
          tabBarLabelStyle: {
            fontFamily: 'Inter-Medium',
            fontSize: 12,
            marginTop: 4,
          },
          tabBarItemStyle: {
            paddingHorizontal: 16,
          },
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: 'Scan',
            tabBarIcon: ({ size, color, focused }) => (
              <View
                style={{
                  padding: 8,
                  borderRadius: 16,
                  backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
                }}>
                {focused ? (
                  <Sparkles size={size} color={color} />
                ) : (
                  <Camera size={size} color={color} />
                )}
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="history"
          options={{
            title: 'History',
            tabBarIcon: ({ size, color, focused }) => (
              <View
                style={{
                  padding: 8,
                  borderRadius: 16,
                  backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
                }}>
                <Compass size={size} color={color} />
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="todo"
          options={{
            title: 'To-Do',
            tabBarIcon: ({ size, color, focused }) => (
              <View
                style={{
                  padding: 8,
                  borderRadius: 16,
                  backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
                }}>
                <ClipboardList size={size} color={color} />
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: 'Settings',
            tabBarIcon: ({ size, color, focused }) => (
              <View
                style={{
                  padding: 8,
                  borderRadius: 16,
                  backgroundColor: focused ? `${colors.primary}1A` : 'transparent',
                }}>
                <Settings size={size} color={color} />
              </View>
            ),
          }}
        />
      </Tabs>
    </AdaptiveNavigation>
  );
}
