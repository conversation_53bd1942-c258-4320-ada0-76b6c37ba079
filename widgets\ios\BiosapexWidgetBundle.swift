import WidgetKit
import SwiftUI

/**
 * Biosapex Widget Bundle
 * Contains all available widgets for the Biosapex app
 */
@main
struct BiosapexWidgetBundle: WidgetBundle {
    var body: some Widget {
        // Main scan widget
        BiosapexWidget()
        
        // Quick scan widget for fast camera access
        QuickScanWidget()
        
        // Todo/Tasks widget with notifications
        TodoWidget()
        
        // Statistics widget for tracking progress
        StatsWidget()
        
        // Subscription status widget
        SubscriptionWidget()
        
        // Daily goals widget (if iOS 16+)
        if #available(iOS 16.0, *) {
            DailyGoalsWidget()
        }
    }
}

// MARK: - Daily Goals Widget (iOS 16+)
@available(iOS 16.0, *)
struct DailyGoalsWidget: Widget {
    let kind: String = "DailyGoalsWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: DailyGoalsProvider()) { entry in
            DailyGoalsWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Daily Goals")
        .description("Track your daily scanning and nature goals.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

@available(iOS 16.0, *)
struct DailyGoalsProvider: TimelineProvider {
    func placeholder(in context: Context) -> DailyGoalsEntry {
        DailyGoalsEntry(
            date: Date(),
            scanGoal: 5,
            scanProgress: 3,
            waterGoal: 8,
            waterProgress: 6,
            streakGoal: 7,
            currentStreak: 5
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (DailyGoalsEntry) -> ()) {
        let entry = DailyGoalsEntry(
            date: Date(),
            scanGoal: 5,
            scanProgress: 4,
            waterGoal: 8,
            waterProgress: 7,
            streakGoal: 7,
            currentStreak: 6
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [DailyGoalsEntry] = []

        let currentDate = Date()
        for hourOffset in 0 ..< 6 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = DailyGoalsEntry(
                date: entryDate,
                scanGoal: 5,
                scanProgress: Int.random(in: 0...5),
                waterGoal: 8,
                waterProgress: Int.random(in: 0...8),
                streakGoal: 7,
                currentStreak: Int.random(in: 1...10)
            )
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

@available(iOS 16.0, *)
struct DailyGoalsEntry: TimelineEntry {
    let date: Date
    let scanGoal: Int
    let scanProgress: Int
    let waterGoal: Int
    let waterProgress: Int
    let streakGoal: Int
    let currentStreak: Int
}

@available(iOS 16.0, *)
struct DailyGoalsWidgetEntryView: View {
    var entry: DailyGoalsProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        if family == .systemSmall {
            SmallDailyGoalsView(entry: entry)
        } else {
            MediumDailyGoalsView(entry: entry)
        }
    }
}

@available(iOS 16.0, *)
struct SmallDailyGoalsView: View {
    let entry: DailyGoalsEntry
    
    var scanProgress: Double {
        return Double(entry.scanProgress) / Double(entry.scanGoal)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Image(systemName: "target")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Goals")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Main goal (scans)
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text("Scans")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(entry.scanProgress)/\(entry.scanGoal)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 6)
                            .cornerRadius(3)
                        
                        Rectangle()
                            .fill(Color.blue)
                            .frame(width: geometry.size.width * scanProgress, height: 6)
                            .cornerRadius(3)
                    }
                }
                .frame(height: 6)
            }
            
            Spacer()
            
            // Streak info
            HStack {
                Image(systemName: "flame.fill")
                    .font(.caption)
                    .foregroundColor(.orange)
                
                Text("\(entry.currentStreak) day streak")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

@available(iOS 16.0, *)
struct MediumDailyGoalsView: View {
    let entry: DailyGoalsEntry
    
    var scanProgress: Double {
        return Double(entry.scanProgress) / Double(entry.scanGoal)
    }
    
    var waterProgress: Double {
        return Double(entry.waterProgress) / Double(entry.waterGoal)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: "target")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Daily Goals")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text(entry.date, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Goals grid
            VStack(spacing: 8) {
                // Scans goal
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "viewfinder")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("Scans")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(entry.scanProgress)/\(entry.scanGoal)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                                .cornerRadius(2)
                            
                            Rectangle()
                                .fill(Color.blue)
                                .frame(width: geometry.size.width * scanProgress, height: 4)
                                .cornerRadius(2)
                        }
                    }
                    .frame(height: 4)
                }
                
                // Water goal
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "drop.fill")
                            .font(.caption)
                            .foregroundColor(.cyan)
                        
                        Text("Water")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(entry.waterProgress)/\(entry.waterGoal)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                                .cornerRadius(2)
                            
                            Rectangle()
                                .fill(Color.cyan)
                                .frame(width: geometry.size.width * waterProgress, height: 4)
                                .cornerRadius(2)
                        }
                    }
                    .frame(height: 4)
                }
            }
            
            // Streak
            HStack {
                Image(systemName: "flame.fill")
                    .font(.caption)
                    .foregroundColor(.orange)
                
                Text("\(entry.currentStreak) day streak")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if entry.currentStreak >= entry.streakGoal {
                    Text("Goal reached! 🎉")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}
