import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  Dimensions,
  Alert,
  Modal,
  Pressable,
  Animated,
  ColorValue,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  CheckCircle,
  Circle,
  Plus,
  Trash2,
  ClipboardList,
  Calendar,
  Sliders,
  Bell,
  ChevronDown,
  X,
  FileText,
  Target,
  TrendingUp,
  Clock,
  AlertCircle,
  Zap,
  ChevronRight,
  Tag,
} from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '@/components/ThemeContext';
import DateTimePicker from '@react-native-community/datetimepicker';
import Slider from '@react-native-community/slider';
import { Swipeable } from 'react-native-gesture-handler';

const { width } = Dimensions.get('window');

interface SubTask {
  id: string;
  text: string;
  completed: boolean;
}

interface TodoItem {
  id: string;
  text: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  progress: number;
  reminder?: string;
  notes?: string;
  subTasks: SubTask[];
  tags: string[];
}

// Priority Configuration
const PRIORITY_CONFIG = {
  low: { 
    color: '#22C55E', 
    gradient: ['#22C55E', '#16A34A'],
    label: 'Low Priority'
  },
  medium: { 
    color: '#F59E0B', 
    gradient: ['#F59E0B', '#D97706'],
    label: 'Medium Priority'
  },
  high: { 
    color: '#EF4444', 
    gradient: ['#EF4444', '#DC2626'],
    label: 'High Priority'
  },
};

// Enhanced Stats Component
const TaskStats = ({ todos, colors }: { todos: TodoItem[]; colors: any }) => {
  const completed = todos.filter(t => t.completed).length;
  const pending = todos.filter(t => !t.completed).length;
  const overdue = todos.filter(t => 
    !t.completed && t.dueDate && new Date(t.dueDate) < new Date()
  ).length;

  return (
    <View style={[styles.statsCard, { backgroundColor: colors.card, shadowColor: colors.shadow }]}>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <LinearGradient 
            colors={['#3B82F6', '#2563EB']} 
            style={styles.statIcon}
          >
            <Target size={20} color="#FFFFFF" />
          </LinearGradient>
          <Text style={[styles.statValue, { color: colors.text }]}>{pending}</Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Active</Text>
        </View>
        
        <View style={styles.statItem}>
          <LinearGradient 
            colors={['#22C55E', '#16A34A']} 
            style={styles.statIcon}
          >
            <CheckCircle size={20} color="#FFFFFF" />
          </LinearGradient>
          <Text style={[styles.statValue, { color: colors.text }]}>{completed}</Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Done</Text>
        </View>
        
        <View style={styles.statItem}>
          <LinearGradient 
            colors={['#EF4444', '#DC2626']} 
            style={styles.statIcon}
          >
            <AlertCircle size={20} color="#FFFFFF" />
          </LinearGradient>
          <Text style={[styles.statValue, { color: colors.text }]}>{overdue}</Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Overdue</Text>
        </View>
      </View>
    </View>
  );
};

// Enhanced Section Component
const TodoSection = ({ title, subtitle, children, colors, rightComponent }: any) => (
  <View style={styles.section}>
    <View style={styles.sectionHeader}>
      <View>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{title}</Text>
        {subtitle && <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>{subtitle}</Text>}
      </View>
      {rightComponent}
    </View>
    <View style={[styles.sectionContent, { backgroundColor: colors.card, shadowColor: colors.shadow }]}>
      {children}
    </View>
  </View>
);

export default function TodoScreen() {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [draftTodo, setDraftTodo] = useState<Partial<TodoItem>>({});
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const { colors } = useTheme();

  useEffect(() => {
    const loadTodos = async () => {
      try {
        const savedTodos = await AsyncStorage.getItem('userTodos-v3');
        if (savedTodos) setTodos(JSON.parse(savedTodos));
      } catch (error) {
        console.error('Failed to load todos:', error);
      }
    };
    loadTodos();
  }, []);

  const saveTodos = async (updatedTodos: TodoItem[]) => {
    try {
      await AsyncStorage.setItem('userTodos-v3', JSON.stringify(updatedTodos));
      setTodos(updatedTodos);
    } catch (error) {
      console.error('Failed to save todos:', error);
    }
  };

  const handleOpenCreateModal = () => {
    setDraftTodo({
      text: '',
      priority: 'medium',
      progress: 0,
      completed: false,
      subTasks: [],
      tags: [],
    });
    setShowCreateModal(true);
  };

  const handleEdit = (todo: TodoItem) => {
    setDraftTodo(todo);
    setShowCreateModal(true);
  };

  const handleSaveTodo = () => {
    if (!draftTodo.text?.trim()) {
      Alert.alert('Task name is required');
      return;
    }
    let updatedTodos;
    if (draftTodo.id) {
      updatedTodos = todos.map(todo =>
        todo.id === draftTodo.id ? ({ ...todo, ...draftTodo } as TodoItem) : todo
      );
    } else {
      const newTodo: TodoItem = {
        id: Date.now().toString(),
        text: draftTodo.text.trim(),
        completed: false,
        priority: draftTodo.priority || 'medium',
        dueDate: draftTodo.dueDate,
        progress: draftTodo.progress || 0,
        reminder: draftTodo.reminder,
        notes: draftTodo.notes,
        subTasks: draftTodo.subTasks || [],
        tags: draftTodo.tags || [],
      };
      updatedTodos = [...todos, newTodo];
    }
    saveTodos(updatedTodos);
    setShowCreateModal(false);
  };

  const toggleTodo = (id: string) => {
    const updatedTodos = todos.map(todo =>
      todo.id === id
        ? { ...todo, completed: !todo.completed, progress: !todo.completed ? 100 : 0 }
        : todo
    );
    saveTodos(updatedTodos);
  };

  const deleteTodo = (id: string) => {
    Alert.alert('Delete Task', 'Are you sure you want to delete this task?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => saveTodos(todos.filter(todo => todo.id !== id)),
      },
    ]);
  };

  const toggleSubTask = (todoId: string, subId: string) => {
    const updated = todos.map(t =>
      t.id === todoId
        ? {
            ...t,
            subTasks: t.subTasks.map(s =>
              s.id === subId ? { ...s, completed: !s.completed } : s
            ),
          }
        : t
    );
    saveTodos(updated);
  };

  const TodoItemComponent = ({ todo }: { todo: TodoItem }) => {
    const swipeRef = useRef<Swipeable>(null);
    const priorityConfig = PRIORITY_CONFIG[todo.priority];
    const isOverdue = todo.dueDate && new Date(todo.dueDate) < new Date() && !todo.completed;

    const renderRightAction = () => (
      <TouchableOpacity
        style={[styles.swipeDelete, { backgroundColor: '#EF4444' }]}
        onPress={() => {
          deleteTodo(todo.id);
          swipeRef.current?.close();
        }}
      >
        <Trash2 size={24} color="#fff" />
      </TouchableOpacity>
    );

    return (
      <Swipeable
        ref={swipeRef}
        renderRightActions={renderRightAction}
        overshootRight={false}
      >
        <TouchableOpacity
          onPress={() => handleEdit(todo)}
          style={[
            styles.todoItem, 
            { 
              backgroundColor: colors.card,
              borderLeftWidth: 4,
              borderLeftColor: priorityConfig.color,
            }
          ]}
          activeOpacity={0.8}
        >
          <TouchableOpacity
            onPress={e => {
              e.stopPropagation();
              toggleTodo(todo.id);
            }}
            style={styles.todoCheck}
          >
            {todo.completed ? (
              <LinearGradient colors={priorityConfig.gradient as [ColorValue, ColorValue]} style={styles.checkboxGradient}>
                <CheckCircle size={24} color="#FFFFFF" />
              </LinearGradient>
            ) : (
              <Circle size={24} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
          
          <View style={styles.todoDetails}>
            <View style={styles.todoHeader}>
              <Text
                style={[
                  styles.todoText,
                  { color: todo.completed ? colors.textSecondary : colors.text },
                  todo.completed && styles.completedText,
                ]}
                numberOfLines={2}
              >
                {todo.text}
              </Text>
              {isOverdue && (
                <View style={[styles.overdueBadge, { backgroundColor: '#EF4444' + '20' }]}>
                  <AlertCircle size={12} color="#EF4444" />
                  <Text style={[styles.overdueText, { color: '#EF4444' }]}>Overdue</Text>
                </View>
              )}
            </View>

            {/* Tags */}
            {todo.tags.length > 0 && (
              <View style={styles.tagRow}>
                {todo.tags.slice(0, 3).map(tag => (
                  <View key={tag} style={[styles.tagPill, { backgroundColor: colors.primary + '15' }]}>
                    <Tag size={10} color={colors.primary} />
                    <Text style={[styles.tagText, { color: colors.primary }]}>{tag}</Text>
                  </View>
                ))}
                {todo.tags.length > 3 && (
                  <Text style={[styles.moreTagsText, { color: colors.textSecondary }]}>
                    +{todo.tags.length - 3} more
                  </Text>
                )}
              </View>
            )}

            {/* Meta Information */}
            <View style={styles.todoMetaRow}>
              {todo.dueDate && (
                <View style={styles.todoMeta}>
                  <Calendar size={14} color={colors.textSecondary} />
                  <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                    {new Date(todo.dueDate).toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </Text>
                </View>
              )}
              
              <View style={styles.todoMeta}>
                <Circle 
                  size={8} 
                  color={priorityConfig.color} 
                  fill={priorityConfig.color}
                />
                <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                  {priorityConfig.label}
                </Text>
              </View>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.backgroundSecondary }]}>
                <LinearGradient
                  colors={priorityConfig.gradient as [ColorValue, ColorValue]}
                  style={[styles.progressFill, { width: `${todo.progress}%` }]}
                />
              </View>
              <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                {todo.progress}%
              </Text>
            </View>

            {/* Sub-tasks Preview */}
            {todo.subTasks.length > 0 && (
              <View style={styles.subTaskPreview}>
                <View style={styles.subTaskHeader}>
                  <CheckCircle size={14} color={colors.textSecondary} />
                  <Text style={[styles.subTaskCount, { color: colors.textSecondary }]}>
                    {todo.subTasks.filter(st => st.completed).length}/{todo.subTasks.length} subtasks
                  </Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.todoRight}>
            {todo.notes && (
              <FileText size={18} color={colors.textSecondary} />
            )}
            <ChevronRight size={16} color={colors.textSecondary} />
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  const activeTodos = todos.filter(t => !t.completed);
  const completedTodos = todos.filter(t => t.completed);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <LinearGradient colors={[colors.background, colors.backgroundSecondary] as [ColorValue, ColorValue]} style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={[styles.title, { color: colors.text }]}>Task Manager</Text>
            <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
              Stay organized and productive
            </Text>
          </View>
          <View style={[styles.headerIcon, { backgroundColor: colors.card, shadowColor: colors.shadow }]}>
            <ClipboardList size={24} color={colors.primary} />
          </View>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {todos.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <View style={[styles.emptyStateIcon, { backgroundColor: colors.card }]}>
                <ClipboardList size={48} color={colors.textSecondary} />
              </View>
              <Text style={[styles.emptyStateTitle, { color: colors.text }]}>No Tasks Yet</Text>
              <Text style={[styles.emptyStateSubtitle, { color: colors.textSecondary }]}>
                Create your first task to get started with productivity tracking.
              </Text>
              <TouchableOpacity
                style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
                onPress={handleOpenCreateModal}
              >
                <Plus size={20} color="#FFFFFF" />
                <Text style={styles.emptyStateButtonText}>Create First Task</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              {/* Statistics */}
              <TaskStats todos={todos} colors={colors} />

              {/* Active Tasks */}
              {activeTodos.length > 0 && (
                <TodoSection 
                  title="Active Tasks" 
                  subtitle={`${activeTodos.length} tasks in progress`}
                  colors={colors}
                  rightComponent={
                    <TouchableOpacity
                      style={[styles.addButton, { backgroundColor: colors.primary }]}
                      onPress={handleOpenCreateModal}
                    >
                      <Plus size={20} color="#FFFFFF" />
                    </TouchableOpacity>
                  }
                >
                  <View style={styles.todoList}>
                    {activeTodos.map(todo => (
                      <TodoItemComponent key={todo.id} todo={todo} />
                    ))}
                  </View>
                </TodoSection>
              )}

              {/* Completed Tasks */}
              {completedTodos.length > 0 && (
                <TodoSection 
                  title="Completed Tasks" 
                  subtitle={`${completedTodos.length} tasks finished`}
                  colors={colors}
                >
                  <View style={styles.todoList}>
                    {completedTodos.slice(0, 5).map(todo => (
                      <TodoItemComponent key={todo.id} todo={todo} />
                    ))}
                    {completedTodos.length > 5 && (
                      <TouchableOpacity style={[styles.viewAllButton, { backgroundColor: colors.backgroundSecondary }]}>
                        <Text style={[styles.viewAllText, { color: colors.primary }]}>
                          View All {completedTodos.length} Completed Tasks
                        </Text>
                        <ChevronRight size={16} color={colors.primary} />
                      </TouchableOpacity>
                    )}
                  </View>
                </TodoSection>
              )}
            </>
          )}

          <View style={styles.bottomSpacing} />
        </ScrollView>

        {/* Floating Action Button */}
        {todos.length > 0 && (
          <TouchableOpacity
            style={[styles.fab, { backgroundColor: colors.primary, shadowColor: colors.shadow }]}
            onPress={handleOpenCreateModal}
          >
            <Plus size={28} color="#FFFFFF" />
          </TouchableOpacity>
        )}

        <CreateEditModal
          visible={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleSaveTodo}
          draftTodo={draftTodo}
          setDraftTodo={setDraftTodo}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

interface CreateEditModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  draftTodo: Partial<TodoItem>;
  setDraftTodo: React.Dispatch<React.SetStateAction<Partial<TodoItem>>>;
}

const CreateEditModal: React.FC<CreateEditModalProps> = ({
  visible,
  onClose,
  onSave,
  draftTodo,
  setDraftTodo,
}) => {
  const { colors } = useTheme();
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [subText, setSubText] = useState('');
  const [tagInput, setTagInput] = useState('');
  const slideAnim = useRef(new Animated.Value(600)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 600,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const addSubTask = () => {
    if (!subText.trim()) return;
    const newSub: SubTask = { id: Date.now().toString(), text: subText.trim(), completed: false };
    setDraftTodo(p => ({ ...p, subTasks: [...(p.subTasks || []), newSub] }));
    setSubText('');
  };

  const addTag = () => {
    const tags = tagInput
      .split(',')
      .map(t => t.trim())
      .filter(Boolean);
    setDraftTodo(p => ({ ...p, tags: [...(p.tags || []), ...tags] }));
    setTagInput('');
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <Animated.View 
          style={[
            styles.modalContent, 
            { backgroundColor: colors.card, transform: [{ translateY: slideAnim }] }
          ]}
        >
          <View style={styles.modalHandle} />
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {draftTodo.id ? 'Edit Task' : 'Create New Task'}
            </Text>
            <TouchableOpacity onPress={onClose} style={[styles.modalCloseButton, { backgroundColor: colors.backgroundSecondary }]}>
              <X size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalScrollView} showsVerticalScrollIndicator={false}>
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Task Details</Text>
              <TextInput
                style={[styles.modalInput, { backgroundColor: colors.backgroundSecondary, color: colors.text, borderColor: colors.border }]}
                placeholder="What needs to be done?"
                placeholderTextColor={colors.textSecondary}
                value={draftTodo.text}
                onChangeText={text => setDraftTodo(p => ({ ...p, text }))}
                multiline
              />

              <TextInput
                style={[styles.modalInput, styles.notesInput, { backgroundColor: colors.backgroundSecondary, color: colors.text, borderColor: colors.border }]}
                placeholder="Add notes or description..."
                placeholderTextColor={colors.textSecondary}
                value={draftTodo.notes}
                onChangeText={notes => setDraftTodo(p => ({ ...p, notes }))}
                multiline
              />
            </View>

            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Priority & Due Date</Text>
              <View style={styles.priorityRow}>
                {(['low', 'medium', 'high'] as const).map(p => {
                  const config = PRIORITY_CONFIG[p];
                  const isSelected = draftTodo.priority === p;
                  return (
                    <TouchableOpacity
                      key={p}
                      style={[
                        styles.priorityChip,
                        { 
                          backgroundColor: isSelected ? config.color + '20' : colors.backgroundSecondary,
                          borderColor: isSelected ? config.color : 'transparent'
                        }
                      ]}
                      onPress={() => setDraftTodo(prev => ({ ...prev, priority: p }))}
                    >
                      <Text style={{ 
                        color: isSelected ? config.color : colors.textSecondary,
                        fontFamily: 'Inter-Medium'
                      }}>
                        {config.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>

              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: colors.backgroundSecondary }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={16} color={colors.textSecondary} />
                <Text style={{ color: colors.text }}>
                  {draftTodo.dueDate ? new Date(draftTodo.dueDate).toLocaleDateString() : 'Set due date'}
                </Text>
                <ChevronRight size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>
                Progress: {Math.round(draftTodo.progress || 0)}%
              </Text>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={0}
                maximumValue={100}
                step={5}
                value={draftTodo.progress}
                onValueChange={progress => setDraftTodo(p => ({ ...p, progress }))}
                minimumTrackTintColor={colors.primary}
                maximumTrackTintColor={colors.border}
                thumbTintColor={colors.primary}
              />
            </View>

            {showDatePicker && (
              <DateTimePicker
                value={draftTodo.dueDate ? new Date(draftTodo.dueDate) : new Date()}
                mode="date"
                display="default"
                onChange={(e, date) => {
                  setShowDatePicker(false);
                  if (date) setDraftTodo(p => ({ ...p, dueDate: date.toISOString() }));
                }}
              />
            )}

            <TouchableOpacity 
              style={[styles.saveButton, { backgroundColor: colors.primary }]} 
              onPress={onSave}
            >
              <Text style={styles.saveButtonText}>
                {draftTodo.id ? 'Save Changes' : 'Create Task'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  gradient: { flex: 1 },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  
  scrollView: { flex: 1 },
  
  // Empty State
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 80,
    paddingHorizontal: 40,
    gap: 20,
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
  },
  emptyStateSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    gap: 8,
    marginTop: 8,
  },
  emptyStateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  
  // Stats
  statsCard: {
    marginHorizontal: 20,
    marginBottom: 32,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    padding: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    gap: 8,
  },
  statIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  
  // Sections
  section: { marginBottom: 32 },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  sectionContent: {
    marginHorizontal: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Todo List
  todoList: { gap: 1 },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    gap: 12,
  },
  todoCheck: { paddingTop: 4 },
  checkboxGradient: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  todoDetails: { flex: 1, gap: 8 },
  todoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 8,
  },
  todoText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    lineHeight: 22,
  },
  completedText: { textDecorationLine: 'line-through' },
  overdueBase: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  overdueText: {
    fontSize: 11,
    fontFamily: 'Inter-Bold',
  },
  tagRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    alignItems: 'center',
  },
  tagPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  tagText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  moreTagsText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  todoMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    minWidth: 35,
  },
  subTaskPreview: {
    gap: 4,
  },
  subTaskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  subTaskCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  todoRight: {
    alignItems: 'center',
    gap: 8,
    paddingTop: 4,
  },
  
  // Swipe Actions
  swipeDelete: {
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
    borderRadius: 16,
  },
  
  // View All Button
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    gap: 8,
    marginTop: 8,
    borderRadius: 12,
  },
  viewAllText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  
  // Floating Action Button
  fab: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  
  bottomSpacing: { height: 40 },
  
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    height: '90%',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 20,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalScrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  modalInput: {
    minHeight: 50,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    fontSize: 16,
    borderWidth: 1,
    marginBottom: 12,
    fontFamily: 'Inter-Regular',
  },
  notesInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  priorityRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  priorityChip: {
    flex: 1,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 2,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
  },
  saveButton: {
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 40,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    fontSize: 16,
  },
  
  // Additional styles for badges
  overdueBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
});
