// FileName: /app/(tabs)/camera.tsx (Updated with RevenueCat subscription checks)
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  FlipHorizontal,
  Settings,
  Images,
  Camera as CameraIcon,
} from 'lucide-react-native';
import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';
import { useTheme } from '@/components/ThemeContext';
import GeminiService, { IdentificationResult } from '@/components/GeminiService';
import { addScanToHistory, ScanHistoryItemType } from './history';
import * as ImagePicker from 'expo-image-picker';
import * as ExpoWidgetsModule from '@bittingz/expo-widgets';
import { useRevenueCatSubscription } from '@/components/RevenueCatSubscriptionContext';

const { width } = Dimensions.get('window');

export default function CameraScreen() {
  const { colors } = useTheme();
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [confidenceLevel, setConfidenceLevel] = useState(0);
  const cameraRef = useRef<CameraView | null>(null);
  const router = useRouter();
  const { subscription, checkSubscription } = useRevenueCatSubscription();

  // Animation values
  const scanLineAnim = useRef(new Animated.Value(0)).current;
  const confidenceRingAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isProcessing) {
      // Animate scan line
      const scanAnimation = Animated.loop(
        Animated.timing(scanLineAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      );
      scanAnimation.start();

      return () => scanAnimation.stop();
    } else {
      scanLineAnim.setValue(0);
    }
  }, [isProcessing, scanLineAnim]);

  useEffect(() => {
    // Animate confidence ring
    Animated.timing(confidenceRingAnim, {
      toValue: confidenceLevel / 100,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [confidenceLevel, confidenceRingAnim]);

  if (!permission) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          We need camera permission to identify species
        </Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.primary }]}
          onPress={requestPermission}
        >
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
    HapticService.impact();
  };

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      HapticService.impact();
      setIsProcessing(true);
      setProcessingStep(0);
      setConfidenceLevel(0);

      // Check subscription limits for free users
      if (!subscription.isSubscribed) {
        Alert.alert(
          'Subscription Required',
          'You need to be subscribed to use the camera. Would you like to upgrade?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Upgrade', onPress: () => router.push('/subscriptionmodal') }
          ]
        );
        return;
      }

      setProcessingStep(1);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        exif: false,
      });

      if (!photo) {
        throw new Error('Failed to take picture');
      }

      setProcessingStep(2);
      // Simulate confidence building
      for (let i = 0; i <= 100; i += 20) {
        setConfidenceLevel(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setProcessingStep(3);
      const result: IdentificationResult = await GeminiService.identifySpecies(photo.uri);

      if (result.species) {
        // Add to history
        const historyItem: ScanHistoryItemType = {
          id: Date.now().toString(),
          imageUri: photo.uri,
          species: result.species,
          confidence: result.confidence,
          timestamp: new Date(),
          details: result.details || {},
          date: '',
          location: '',
          name: '',
          scientificName: '',
          category: 'Animal',
          description: ''
        };

        await addScanToHistory(historyItem);
        
        // Update widget if available
        if (ExpoWidgetsModule && ExpoWidgetsModule.setWidgetData) {
          await ExpoWidgetsModule.setWidgetData('latestScan', {
            species: result.species,
            confidence: result.confidence,
            timestamp: new Date().toISOString(),
          });
        }

        // Navigate to results
        router.push({
          pathname: '/scan-result',
          params: {
            imageUri: photo.uri,
            species: result.species,
            confidence: result.confidence.toString(),
            details: JSON.stringify(result.details),
          },
        });

        // Show notification
        await NotificationService.scheduleNotification(
          'Species Identified!',
          `Found: ${result.species} (${Math.round(result.confidence)}% confidence)`
        );
      } else {
        Alert.alert('No Match', 'Could not identify the species in this image. Try taking another photo.');
      }
    } catch (error) {
      console.error('Error taking/processing picture:', error);
      Alert.alert('Error', 'Failed to process the image. Please try again.');
    } finally {
      setIsProcessing(false);
      setProcessingStep(0);
      setConfidenceLevel(0);
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant photo library permission to select images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        
        // Check subscription limits for free users
        if (!subscription.isSubscribed) {
          Alert.alert(
            'Subscription Required',
            'You need to be subscribed to upload photos. Would you like to upgrade?',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Upgrade', onPress: () => router.push('/subscriptionmodal') }
            ]
          );
          return;
        }

        setIsProcessing(true);
        const identificationResult = await GeminiService.identifySpecies(imageUri);
        setIsProcessing(false);

        if (identificationResult.species) {
          router.push({
            pathname: '/scan-result',
            params: {
              imageUri,
              species: identificationResult.species,
              confidence: identificationResult.confidence.toString(),
              details: JSON.stringify(identificationResult.details),
            },
          });
        }
      }
    } catch (error) {
      setIsProcessing(false);
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to process the selected image.');
    }
  };

  const getProcessingStepText = () => {
    switch (processingStep) {
      case 0:
        return 'Initializing...';
      case 1:
        return 'Capturing image...';
      case 2:
        return 'Analyzing features...';
      case 3:
        return 'Identifying species...';
      default:
        return 'Processing...';
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Species Scanner
        </Text>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.push('/settings')}
        >
          <Settings size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Camera Container */}
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          ref={cameraRef}
        >
          {/* Overlay */}
          <View style={styles.overlay}>
            {/* Scanning Frame */}
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft, { borderColor: colors.primary }]} />
              <View style={[styles.corner, styles.topRight, { borderColor: colors.primary }]} />
              <View style={[styles.corner, styles.bottomLeft, { borderColor: colors.primary }]} />
              <View style={[styles.corner, styles.bottomRight, { borderColor: colors.primary }]} />
              
              {/* Animated scan line */}
              {isProcessing && (
                <Animated.View
                  style={[
                    styles.scanLine,
                    {
                      backgroundColor: colors.primary,
                      opacity: scanLineAnim.interpolate({
                        inputRange: [0, 0.5, 1],
                        outputRange: [0.3, 1, 0.3],
                      }),
                      transform: [
                        {
                          translateY: scanLineAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [-100, 100],
                          }),
                        },
                      ],
                    },
                  ]}
                />
              )}
              
              {/* Confidence Ring */}
              {isProcessing && confidenceLevel > 0 && (
                <Animated.View
                  style={[
                    styles.confidenceRing,
                    {
                      borderColor: colors.primary,
                      borderWidth: confidenceRingAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, 4],
                      }),
                      opacity: confidenceRingAnim,
                    },
                  ]}
                />
              )}
            </View>

            {/* Processing Overlay */}
            {isProcessing && (
              <View style={[styles.processingOverlay, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
                <View style={styles.processingContent}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={[styles.processingText, { color: colors.text }]}>
                    {getProcessingStepText()}
                  </Text>
                  {confidenceLevel > 0 && (
                    <Text style={[styles.confidenceText, { color: colors.primary }]}>
                      {confidenceLevel}% confidence
                    </Text>
                  )}
                </View>
              </View>
            )}
          </View>
        </CameraView>
      </View>

      {/* Controls */}
      <View style={[styles.controls, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: colors.background }]}
          onPress={pickImageFromGallery}
          disabled={isProcessing}
        >
          <Images size={28} color={colors.text} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.captureButton, { backgroundColor: colors.primary }]}
          onPress={takePicture}
          disabled={isProcessing}
        >
          <CameraIcon size={32} color="#fff" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: colors.background }]}
          onPress={toggleCameraFacing}
          disabled={isProcessing}
        >
          <FlipHorizontal size={28} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Subscription Status */}
      <View style={[styles.subscriptionStatus, { backgroundColor: colors.surface }]}>
        <Text style={[styles.subscriptionText, { color: colors.text }]}>
          {subscription.isSubscribed 
            ? `Pro Member - Unlimited Scans` 
            : 'Free Tier - Limited Scans'
          }
        </Text>
        {!subscription.isSubscribed && (
          <TouchableOpacity
            style={[styles.upgradeButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('./subscriptionmodal')} // Try relative path
          >
            <Text style={styles.upgradeText}>Upgrade</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: width * 0.8,
    height: width * 0.8,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 2,
    top: '50%',
  },
  confidenceRing: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    borderRadius: (width * 0.8 + 20) / 2,
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingContent: {
    alignItems: 'center',
  },
  processingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  confidenceText: {
    fontSize: 14,
    marginTop: 8,
    fontWeight: '600',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  subscriptionStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  subscriptionText: {
    fontSize: 14,
    flex: 1,
  },
  upgradeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  upgradeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  permissionText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});