import { Platform } from 'react-native';

type HapticType = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error';

export class HapticService {
  private static instance: HapticService;
  private enabled: boolean = true;

  static getInstance(): HapticService {
    if (!HapticService.instance) {
      HapticService.instance = new HapticService();
    }
    return HapticService.instance;
  }

  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  isEnabled(): boolean {
    return this.enabled;
  }

  async triggerHaptic(type: HapticType): Promise<void> {
    if (!this.enabled) return;

    if (Platform.OS === 'web') {
      // Web implementation using Vibration API
      if ('vibrator' in navigator || 'vibrate' in navigator) {
        try {
          const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            warning: [20, 100, 20],
            error: [50, 100, 50],
          };

          const pattern = patterns[type] || patterns.light;
          navigator.vibrate(pattern);
        } catch (error) {
          console.warn('Vibration not supported:', error);
        }
      }
    } else {
      // Native implementation using expo-haptics
      try {
        const hapticsModule = await import('expo-haptics');

        if (!hapticsModule) {
          throw new Error('Haptics module not available');
        }

        switch (type) {
          case 'light':
            await hapticsModule.impactAsync(hapticsModule.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            await hapticsModule.impactAsync(hapticsModule.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            await hapticsModule.impactAsync(hapticsModule.ImpactFeedbackStyle.Heavy);
            break;
          case 'success':
            await hapticsModule.notificationAsync(hapticsModule.NotificationFeedbackType.Success);
            break;
          case 'warning':
            await hapticsModule.notificationAsync(hapticsModule.NotificationFeedbackType.Warning);
            break;
          case 'error':
            await hapticsModule.notificationAsync(hapticsModule.NotificationFeedbackType.Error);
            break;
        }
      } catch (error) {
        console.warn('Haptic feedback not available:', error);
      }
    }
  }

  // Convenience methods
  async triggerSelectionFeedback(): Promise<void> {
    await this.triggerHaptic('light');
  }

  async triggerImpactFeedback(style: 'light' | 'medium' | 'heavy' = 'medium'): Promise<void> {
    await this.triggerHaptic(style);
  }

  async triggerNotificationFeedback(type: 'success' | 'warning' | 'error'): Promise<void> {
    await this.triggerHaptic(type);
  }
}

export default HapticService;
