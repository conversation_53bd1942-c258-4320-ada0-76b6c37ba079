/**
 * Comprehensive Input Validation Service for Biosapex
 * Provides type-safe validation for all user inputs and API responses
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => boolean;
  sanitizer?: (value: any) => any;
}

export class ValidationService {
  
  /**
   * Validate string inputs with comprehensive rules
   */
  static validateString(value: any, rules: ValidationRule = {}): ValidationResult {
    const errors: string[] = [];
    let sanitizedValue = value;

    // Type check
    if (typeof value !== 'string') {
      if (rules.required) {
        errors.push('Value must be a string');
        return { isValid: false, errors };
      }
      sanitizedValue = String(value || '');
    }

    // Sanitize
    if (rules.sanitizer) {
      sanitizedValue = rules.sanitizer(sanitizedValue);
    } else {
      // Default sanitization: trim whitespace and remove dangerous characters
      sanitizedValue = sanitizedValue.trim().replace(/[<>]/g, '');
    }

    // Required check
    if (rules.required && !sanitizedValue) {
      errors.push('This field is required');
    }

    // Length checks
    if (rules.minLength && sanitizedValue.length < rules.minLength) {
      errors.push(`Minimum length is ${rules.minLength} characters`);
    }

    if (rules.maxLength && sanitizedValue.length > rules.maxLength) {
      errors.push(`Maximum length is ${rules.maxLength} characters`);
    }

    // Pattern check
    if (rules.pattern && sanitizedValue && !rules.pattern.test(sanitizedValue)) {
      errors.push('Invalid format');
    }

    // Custom validation
    if (rules.customValidator && !rules.customValidator(sanitizedValue)) {
      errors.push('Custom validation failed');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue
    };
  }

  /**
   * Validate API key format and security
   */
  static validateApiKey(apiKey: any): ValidationResult {
    return this.validateString(apiKey, {
      required: true,
      minLength: 20,
      maxLength: 200,
      pattern: /^[A-Za-z0-9_-]+$/,
      customValidator: (value: string) => {
        // Check for common API key patterns
        return value.length >= 20 && !value.includes(' ');
      },
      sanitizer: (value: string) => value.trim()
    });
  }

  /**
   * Validate image base64 data
   */
  static validateImageBase64(base64: any): ValidationResult {
    const errors: string[] = [];

    if (typeof base64 !== 'string') {
      errors.push('Image data must be a string');
      return { isValid: false, errors };
    }

    // Check if it's valid base64
    try {
      const decoded = atob(base64);
      if (decoded.length === 0) {
        errors.push('Invalid image data');
      }
    } catch (error) {
      errors.push('Invalid base64 encoding');
    }

    // Check reasonable size limits (max 10MB base64)
    if (base64.length > 13000000) {
      errors.push('Image too large (max 10MB)');
    }

    if (base64.length < 100) {
      errors.push('Image data too small');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: base64
    };
  }

  /**
   * Validate confidence score
   */
  static validateConfidence(confidence: any): ValidationResult {
    const errors: string[] = [];
    let sanitizedValue = confidence;

    if (typeof confidence === 'string') {
      sanitizedValue = parseFloat(confidence);
    }

    if (typeof sanitizedValue !== 'number' || isNaN(sanitizedValue)) {
      errors.push('Confidence must be a number');
      return { isValid: false, errors };
    }

    if (sanitizedValue < 0 || sanitizedValue > 100) {
      errors.push('Confidence must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue
    };
  }

  /**
   * Validate Gemini API response structure
   */
  static validateGeminiResponse(response: any): ValidationResult {
    const errors: string[] = [];

    if (!response || typeof response !== 'object') {
      errors.push('Invalid response format');
      return { isValid: false, errors };
    }

    // Check required structure
    if (!response.candidates || !Array.isArray(response.candidates)) {
      errors.push('Missing candidates array');
    }

    if (response.candidates && response.candidates.length === 0) {
      errors.push('Empty candidates array');
    }

    if (response.candidates && response.candidates[0]) {
      const candidate = response.candidates[0];
      if (!candidate.content || !candidate.content.parts || !Array.isArray(candidate.content.parts)) {
        errors.push('Invalid candidate structure');
      }

      if (candidate.content && candidate.content.parts && candidate.content.parts.length === 0) {
        errors.push('Empty parts array');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: response
    };
  }

  /**
   * Validate identification result from AI
   */
  static validateIdentificationResult(result: any): ValidationResult {
    const errors: string[] = [];

    if (!result || typeof result !== 'object') {
      errors.push('Invalid result format');
      return { isValid: false, errors };
    }

    // Required fields
    const requiredFields = ['name', 'confidence', 'category'];
    for (const field of requiredFields) {
      if (!result[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Validate confidence
    if (result.confidence !== undefined) {
      const confidenceValidation = this.validateConfidence(result.confidence);
      if (!confidenceValidation.isValid) {
        errors.push(...confidenceValidation.errors);
      }
    }

    // Validate arrays
    const arrayFields = ['facts', 'distinguishingFeatures', 'usage', 'safety'];
    for (const field of arrayFields) {
      if (result[field] && !Array.isArray(result[field])) {
        errors.push(`${field} must be an array`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: result
    };
  }

  /**
   * Sanitize user input for display
   */
  static sanitizeForDisplay(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+=/gi, '') // Remove event handlers
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: any): ValidationResult {
    const errors: string[] = [];

    if (typeof url !== 'string') {
      errors.push('URL must be a string');
      return { isValid: false, errors };
    }

    try {
      const urlObj = new URL(url);
      
      // Only allow https for security
      if (urlObj.protocol !== 'https:') {
        errors.push('Only HTTPS URLs are allowed');
      }

      // Check for reasonable length
      if (url.length > 2000) {
        errors.push('URL too long');
      }

    } catch (error) {
      errors.push('Invalid URL format');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: url
    };
  }

  /**
   * Batch validate multiple values
   */
  static validateBatch(validations: Array<{ value: any; validator: () => ValidationResult }>): ValidationResult {
    const allErrors: string[] = [];
    let allValid = true;

    for (const validation of validations) {
      const result = validation.validator();
      if (!result.isValid) {
        allValid = false;
        allErrors.push(...result.errors);
      }
    }

    return {
      isValid: allValid,
      errors: allErrors
    };
  }
}

export default ValidationService;
