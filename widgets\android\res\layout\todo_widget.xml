<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_check_circle"
            android:contentDescription="Tasks Icon"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Tasks"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary" />

        <!-- Pending count badge -->
        <TextView
            android:id="@+id/widget_pending_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/widget_accent"
            android:background="@drawable/widget_badge_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:minWidth="32dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Next Task Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Next Task"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/widget_next_task"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Scan 5 different plant species"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginBottom="6dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/ic_clock"
                android:contentDescription="Due time"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/widget_task_due"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2h"
                android:textSize="12sp"
                android:textColor="@color/widget_text_secondary" />

        </LinearLayout>

    </LinearLayout>

    <!-- Bottom Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Progress text -->
        <TextView
            android:id="@+id/widget_completed_today"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="2/5 completed today"
            android:textSize="10sp"
            android:textColor="@color/widget_text_secondary" />

        <!-- Add task button -->
        <LinearLayout
            android:id="@+id/widget_add_task_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/widget_button_small"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/ic_add"
                android:contentDescription="Add task"
                android:layout_marginEnd="2dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/widget_button_text" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
