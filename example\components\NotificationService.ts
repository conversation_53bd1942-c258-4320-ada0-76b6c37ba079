interface NotificationPermission {
  granted: boolean;
  denied: boolean;
}

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  requireInteraction?: boolean;
}

export class NotificationService {
  static scheduleNotification(arg0: string, arg1: string) {
    throw new Error('Method not implemented.');
  }
  private static instance: NotificationService;
  private permission: NotificationPermission = { granted: false, denied: false };

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async requestPermission(): Promise<boolean> {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      console.warn('Notifications not supported in this environment');
      return false;
    }

    if (Notification.permission === 'granted') {
      this.permission.granted = true;
      return true;
    }

    if (Notification.permission === 'denied') {
      this.permission.denied = true;
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      this.permission.granted = permission === 'granted';
      this.permission.denied = permission === 'denied';
      return this.permission.granted;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  async sendNotification(options: NotificationOptions): Promise<boolean> {
    if (!this.permission.granted) {
      const granted = await this.requestPermission();
      if (!granted) {
        console.warn('Notification permission not granted');
        return false;
      }
    }

    try {
      if (typeof Notification === 'undefined') {
        console.warn('Notifications not supported in this environment');
        return false;
      }

      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/favicon.png',
        badge: options.badge,
        tag: options.tag,
        requireInteraction: options.requireInteraction || false,
      });

      // Auto-close after 5 seconds unless requireInteraction is true
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  async sendScanResultNotification(speciesName: string, confidence: number): Promise<void> {
    await this.sendNotification({
      title: 'Scan Complete! 🔍',
      body: `Identified: ${speciesName} (${confidence}% confidence)`,
      tag: 'scan-result',
      requireInteraction: true,
    });
  }

  async sendAchievementNotification(achievement: string): Promise<void> {
    await this.sendNotification({
      title: 'Achievement Unlocked! 🏆',
      body: achievement,
      tag: 'achievement',
      requireInteraction: true,
    });
  }

  async sendCommunityNotification(message: string): Promise<void> {
    await this.sendNotification({
      title: 'BioScan Community 👥',
      body: message,
      tag: 'community',
    });
  }

  getPermissionStatus(): NotificationPermission {
    return this.permission;
  }
}

export default NotificationService;