import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSettings } from './SettingsContext';

interface TextStyleContextType {
  textSizeMultiplier: number;
}

const TextStyleContext = createContext<TextStyleContextType | undefined>(undefined);

export const useTextStyle = () => {
  const context = useContext(TextStyleContext);
  if (!context) {
    throw new Error('useTextStyle must be used within a TextStyleProvider');
  }
  return context;
};

interface TextStyleProviderProps {
  children: ReactNode;
}

export const TextStyleProvider: React.FC<TextStyleProviderProps> = ({ children }) => {
  const { settings } = useSettings();
  const [textSizeMultiplier, setTextSizeMultiplier] = useState(1.0);

  useEffect(() => {
    // Update text size multiplier based on text size setting
    switch (settings.textSize) {
      case 'small':
        setTextSizeMultiplier(0.9);
        break;
      case 'normal':
        setTextSizeMultiplier(1.0);
        break;
      case 'large':
        setTextSizeMultiplier(1.2);
        break;
      case 'extra-large':
        setTextSizeMultiplier(1.4);
        break;
      default:
        setTextSizeMultiplier(1.0);
    }
  }, [settings.textSize]);

  return (
    <TextStyleContext.Provider value={{ textSizeMultiplier }}>
      {children}
    </TextStyleContext.Provider>
  );
};

export default TextStyleContext;