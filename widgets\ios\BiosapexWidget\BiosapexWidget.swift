import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), recentScan: "Loading...", scanCount: 0)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), recentScan: "Oak Tree", scanCount: 42)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [SimpleEntry] = []

        // Generate a timeline consisting of five entries an hour apart, starting from the current date.
        let currentDate = Date()
        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SimpleEntry(date: entryDate, recentScan: getRecentScan(), scanCount: getScanCount())
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func getRecentScan() -> String {
        // In a real implementation, this would fetch from shared storage
        let samples = ["Oak Tree", "Robin", "Monarch Butterfly", "Rose", "Pine Cone"]
        return samples.randomElement() ?? "No recent scans"
    }
    
    private func getScanCount() -> Int {
        // In a real implementation, this would fetch from shared storage
        return Int.random(in: 1...99)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let recentScan: String
    let scanCount: Int
}

struct BiosapexWidgetEntryView : View {
    var entry: Provider.Entry

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "leaf.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Biosapex")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Recent Scan")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(entry.recentScan)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Total Scans")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text("\(entry.scanCount)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Updated")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(entry.date, style: .time)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct BiosapexWidget: Widget {
    let kind: String = "BiosapexWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            BiosapexWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Biosapex Scanner")
        .description("Quick access to your recent species scans and statistics.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct BiosapexWidget_Previews: PreviewProvider {
    static var previews: some View {
        BiosapexWidgetEntryView(entry: SimpleEntry(date: Date(), recentScan: "Oak Tree", scanCount: 42))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
    }
}
