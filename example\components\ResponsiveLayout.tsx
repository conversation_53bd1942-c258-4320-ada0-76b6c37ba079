import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { useBreakpoints } from '@/hooks/useBreakpoints';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  phoneStyle?: StyleProp<ViewStyle>;
  tabletStyle?: StyleProp<ViewStyle>;
  desktopStyle?: StyleProp<ViewStyle>;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  style,
  phoneStyle,
  tabletStyle,
  desktopStyle,
}) => {
  const { isPhone, isTablet, isDesktop } = useBreakpoints();

  let responsiveStyle: StyleProp<ViewStyle> = style;

  if (isDesktop && desktopStyle) {
    responsiveStyle = [style, desktopStyle];
  } else if (isTablet && tabletStyle) {
    responsiveStyle = [style, tabletStyle];
  } else if (isPhone && phoneStyle) {
    responsiveStyle = [style, phoneStyle];
  }

  return <View style={[styles.container, responsiveStyle]}>{children}</View>;
};

interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: number | { phone?: number; tablet?: number; desktop?: number };
  gap?: number;
  style?: StyleProp<ViewStyle>;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { phone: 1, tablet: 2, desktop: 3 },
  gap = 16,
  style,
}) => {
  const { isPhone, isTablet, isDesktop } = useBreakpoints();

  let colCount: number;
  if (typeof columns === 'number') {
    colCount = columns;
  } else {
    if (isDesktop && columns.desktop) {
      colCount = columns.desktop;
    } else if (isTablet && columns.tablet) {
      colCount = columns.tablet;
    } else {
      colCount = columns.phone || 1;
    }
  }

  const gridStyle = {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    marginHorizontal: -gap / 2,
    marginTop: -gap / 2,
  };

  const childStyle = {
    width: `${100 / colCount}%` as `${number}%`,
    paddingHorizontal: gap / 2,
    paddingTop: gap / 2,
  };

  return (
    <View style={[styles.grid, gridStyle, style]}>
      {React.Children.map(children, child => (
        <View style={childStyle}>{child}</View>
      ))}
    </View>
  );
};

interface ResponsiveColumnProps {
  children: React.ReactNode;
  span?: number | { phone?: number; tablet?: number; desktop?: number };
  style?: StyleProp<ViewStyle>;
}

export const ResponsiveColumn: React.FC<ResponsiveColumnProps> = ({
  children,
  span = 1,
  style,
}) => {
  return <View style={[styles.column, style]}>{children}</View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  grid: {},
  column: {},
});