import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import 'react-native-reanimated';

import ErrorBoundary from '@/components/ErrorBoundary';
import NotificationManager from '@/components/NotificationManager';
import RevenueCatService from '@/components/RevenueCatService';
import { SettingsProvider } from '@/components/SettingsContext';
import { SubscriptionProvider } from '@/components/SubscriptionContext';
import { ThemeProvider as CustomThemeProvider } from '@/components/ThemeContext';
import WidgetDataManager from '@/components/WidgetDataManager';
import { useColorScheme } from '@/hooks/useColorScheme';


export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Initialize notification, widget, and RevenueCat services
  React.useEffect(() => {
    const initializeServices = async () => {
      const notificationManager = NotificationManager.getInstance();
      const widgetDataManager = WidgetDataManager.getInstance();
      const revenueCatService = RevenueCatService.getInstance();

      // Initialize RevenueCat first
      await revenueCatService.initialize();

      // Initialize notifications
      await notificationManager.initialize();

      // Schedule daily reminders
      await widgetDataManager.scheduleDailyReminders();
    };

    initializeServices();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('App Error Boundary caught error:', error, errorInfo);
      }}
    >
      <SettingsProvider>
        <SubscriptionProvider>
          <CustomThemeProvider>
            <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="paywall" options={{ headerShown: false, presentation: 'modal' }} />
                <Stack.Screen name="subscription" options={{ headerShown: false }} />
                <Stack.Screen name="camera" options={{ headerShown: false }} />
                <Stack.Screen name="scan-result" options={{ headerShown: false }} />
                <Stack.Screen name="api-config" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </CustomThemeProvider>
        </SubscriptionProvider>
      </SettingsProvider>
    </ErrorBoundary>
  );
}
