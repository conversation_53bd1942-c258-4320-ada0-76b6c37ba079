import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import 'react-native-reanimated';

import ErrorBoundary from '@/components/ErrorBoundary';
import NotificationManager from '@/components/NotificationManager';
import { SettingsProvider } from '@/components/SettingsContext';
import { SubscriptionProvider } from '@/components/SubscriptionContext';
import { ThemeProvider as CustomThemeProvider } from '@/components/ThemeContext';
import WidgetDataManager from '@/components/WidgetDataManager';
import { useColorScheme } from '@/hooks/useColorScheme';


export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Initialize notification and widget services
  React.useEffect(() => {
    const initializeServices = async () => {
      const notificationManager = NotificationManager.getInstance();
      const widgetDataManager = WidgetDataManager.getInstance();

      // Initialize notifications
      await notificationManager.initialize();

      // Schedule daily reminders
      await widgetDataManager.scheduleDailyReminders();
    };

    initializeServices();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('App Error Boundary caught error:', error, errorInfo);
      }}
    >
      <SettingsProvider>
        <SubscriptionProvider>
          <CustomThemeProvider>
            <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </CustomThemeProvider>
        </SubscriptionProvider>
      </SettingsProvider>
    </ErrorBoundary>
  );
}
