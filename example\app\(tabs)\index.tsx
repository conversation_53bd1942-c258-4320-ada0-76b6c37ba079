// FileName: /app/(tabs)/index.tsx (Updated Main Screen)
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useFocusEffect } from 'expo-router';
import * as ImagePicker from 'expo-image-picker'; // Still needed for handleUploadImage if it were re-added to this screen
import {
  Flame, // For the fire icon
  Minus,
  Plus,
  Settings,
  Scan, // For recent discoveries / scans today
  TrendingUp, // For streak
  Award, // For total species / unique categories
  Droplet, // For water
  Apple, // For Apex logo placeholder
} from 'lucide-react-native';
import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';
import { useTheme } from '@/components/ThemeContext';
import { useTextScaling } from '@/components/TextScalingContext';
// import GeminiService from '@/components/GeminiService'; // Not needed on this dashboard screen
import { addScanToHistory, getHistory, ScanHistoryItemType } from './history';
import { useSubscription } from '@/components/SubscriptionContext';
import SubscriptionModal from '@/components/SubscriptionModal';

const { width, height } = Dimensions.get('window');

// Extend ScanHistoryItemType (Keep this as it was)
declare module './history' {
  export interface ScanHistoryItemType {
    [x: string]: ReactNode;
    emoji?: string;
    facts?: string[];
    physicalTraits?: string[];
    alternatives?: string[];
    distinguishingFeatures?: string[];
  }
}
// --- End Interfaces ---

export default function ScanScreen() {
  const { colors } = useTheme();
  const { getScaledFontSize } = useTextScaling();
  const router = useRouter();
  const { isPremiumUser } = useSubscription();

  // State for dynamic data
  const [currentDate, setCurrentDate] = useState(new Date());
  const [waterIntake, setWaterIntake] = useState(24); // in fl oz (this remains local state)

  // States for data from history.tsx
  const [recentDiscoveries, setRecentDiscoveries] = useState<ScanHistoryItemType[]>([]);
  const [stats, setStats] = useState({ todayScans: 0, streak: 0, totalSpecies: 0, uniqueCategories: 0 });

  const [isSubscriptionModalVisible, setSubscriptionModalVisible] = useState(false);

  // Services (Keep as is)
  const hapticService = HapticService.getInstance();
  const notificationService = NotificationService.getInstance();


  // Function to load and update history and stats
  const loadHistoryAndStats = useCallback(async () => {
    const history = await getHistory();
    // Sort history by date descending to get most recent first
    const sortedHistory = history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    setRecentDiscoveries(sortedHistory.slice(0, 5)); // Show up to 5 most recent discoveries

    const today = new Date().toDateString();
    const todayScans = history.filter(item => new Date(item.date).toDateString() === today).length;
    const uniqueSpecies = new Set(history.map(item => item.name)).size;
    const uniqueCategories = new Set(history.map(item => item.category)).size;

    // Streak calculation: count consecutive days with at least one scan
    let streak = 0;
    if (history.length > 0) {
      const dates = [...new Set(history.map(item => new Date(item.date).toDateString()))].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
      if (dates.length > 0) {
        let currentStreak = 0;
        let lastDate = new Date(dates[dates.length - 1]);
        const todayDate = new Date();
        todayDate.setHours(0, 0, 0, 0); // Normalize today's date to start of day

        // Check if the last scan was today or yesterday to start streak calculation correctly
        if (lastDate.toDateString() === todayDate.toDateString()) {
          currentStreak = 1;
        } else {
          const yesterdayDate = new Date(todayDate);
          yesterdayDate.setDate(todayDate.getDate() - 1);
          if (lastDate.toDateString() === yesterdayDate.toDateString()) {
            currentStreak = 1;
          }
        }

        for (let i = dates.length - 2; i >= 0; i--) {
          const prevDate = new Date(dates[i]);
          const expectedPrevDate = new Date(lastDate);
          expectedPrevDate.setDate(lastDate.getDate() - 1);

          if (prevDate.toDateString() === expectedPrevDate.toDateString()) {
            currentStreak++;
            lastDate = prevDate;
          } else {
            // Break streak if not consecutive
            if (prevDate.toDateString() !== lastDate.toDateString()) { // Ensure not same day
               break;
            }
          }
        }
        streak = currentStreak;
      }
    }

    setStats({
      todayScans: todayScans,
      streak: streak,
      totalSpecies: uniqueSpecies,
      uniqueCategories: uniqueCategories,
    });
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadHistoryAndStats();
      return () => {};
    }, [loadHistoryAndStats])
  );

  // Date navigation functions
  const getDayLabel = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    if (date.toDateString() === today.toDateString()) return 'Today';
    if (date.toDateString() === yesterday.toDateString()) return 'Yesterday';
    if (date.toDateString() === tomorrow.toDateString()) return 'Tomorrow';
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  const getDayNumber = (date: Date) => {
    return date.getDate();
  };

  const getWeekDates = () => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay()); // Start from Sunday
    const weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      weekDates.push(date);
    }
    return weekDates;
  };

  const incrementWater = () => {
    hapticService.triggerSelectionFeedback();
    setWaterIntake(prev => prev + 8); // Add 8 fl oz (approx 1 cup)
  };

  const decrementWater = () => {
    hapticService.triggerSelectionFeedback();
    setWaterIntake(prev => Math.max(0, prev - 8)); // Subtract 8 fl oz, min 0
  };

  // Helper function to get an emoji for a category (from original index.tsx)
  const getEmojiForCategory = (category: string) => {
    switch (category) {
      case 'Animal': return '🐾';
      case 'Plant': return '🌿';
      case 'Food': return '🍎';
      case 'Insect': return '🐞';
      case 'Rock / Mineral': return '🪨';
      case 'Coin / Currency': return '💰';
      case 'Wine': return '🍷';
      case 'Product': return '📦';
      case 'Electronics': return '🔌';
      case 'Clothing': return '👕';
      case 'Book': return '📚';
      case 'Medicine': return '💊';
      case 'Tool': return '🛠️';
      case 'Art': return '🎨';
      default: return '🔍';
    }
  };


  const styles = getDashboardStyles(colors, getScaledFontSize);

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient colors={[colors.background, colors.backgroundSecondary]} style={StyleSheet.absoluteFill} />
      <ScrollView contentContainerStyle={styles.scrollContentContainer} showsVerticalScrollIndicator={false}>
        {/* Header Section - Apex branding with fire icon */}
        <View style={styles.header}>
          <View style={styles.appLogoContainer}>
            {/* Using Apple icon as a placeholder for a generic app logo, or you can replace with a custom SVG/Image */}
            <Apple size={28} color={colors.text} />
            <Text style={styles.appNameText}>Apex</Text>
          </View>
          <View style={styles.fireIconContainer}>
            <Flame size={20} color={colors.primary} />
            <Text style={styles.fireIconText}>1</Text>
          </View>
        </View>

        {/* Date Selector */}
        <View style={styles.dateSelectorContainer}>
          {getWeekDates().map((date, index) => {
            const isSelected = date.toDateString() === currentDate.toDateString();
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateBubble,
                  isSelected && { backgroundColor: colors.primary },
                  !isSelected && { backgroundColor: colors.card, borderColor: colors.border, borderWidth: 1 }
                ]}
                onPress={() => {
                  hapticService.triggerSelectionFeedback();
                  setCurrentDate(date);
                }}
              >
                <Text style={[styles.dateDayText, { color: isSelected ? '#FFFFFF' : colors.textSecondary }]}>
                  {getDayLabel(date).charAt(0)}
                </Text>
                <Text style={[styles.dateNumberText, { color: isSelected ? '#FFFFFF' : colors.text }]}>
                  {getDayNumber(date)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Activity Summary Cards (Now using data from history) */}
        <View style={styles.activityCardsRow}>
          {/* Scans Today Card */}
          <View style={[styles.activityCard, { backgroundColor: colors.card }]}>
            <Text style={[styles.activityValue, { color: colors.text }]}>
              {stats.todayScans}
            </Text>
            <Text style={[styles.activityLabel, { color: colors.textSecondary }]}>Scans Today</Text>
            <View style={styles.statIconLargeContainer}>
              <Scan size={40} color={colors.primary} />
            </View>
          </View>

          {/* Total Species Found Card */}
          <View style={[styles.activityCard, { backgroundColor: colors.card }]}>
            <Text style={[styles.activityValue, { color: colors.text }]}>{stats.totalSpecies}</Text>
            <Text style={[styles.activityLabel, { color: colors.textSecondary }]}>Total Species Found</Text>
            <View style={styles.calorieDetailRow}>
              <View style={styles.calorieIconContainer}>
                <TrendingUp size={18} color={colors.text} />
              </View>
              <Text style={[styles.calorieDetailText, { color: colors.text }]}>Day Streak</Text>
              <Text style={[styles.calorieDetailValue, { color: colors.success }]}>{stats.streak}</Text>
            </View>
            <View style={styles.calorieDetailRow}>
              <View style={styles.calorieIconContainer}>
                <Award size={18} color={colors.text} />
              </View>
              <Text style={[styles.calorieDetailText, { color: colors.text }]}>Unique Categories</Text>
              <Text style={[styles.calorieDetailValue, { color: colors.success }]}>{stats.uniqueCategories}</Text>
            </View>
          </View>
        </View>

        {/* Water Intake Section (Remains local state) */}
        <View style={[styles.waterCard, { backgroundColor: colors.card }]}>
          <View style={styles.waterIconContainer}>
            <Droplet size={24} color={colors.primary} />
          </View>
          <View style={styles.waterContent}>
            <Text style={[styles.waterLabel, { color: colors.text }]}>Water</Text>
            <Text style={[styles.waterValue, { color: colors.textSecondary }]}>
              {waterIntake} fl oz ({Math.round(waterIntake / 8)} cups)
            </Text>
          </View>
          <TouchableOpacity style={styles.waterSettingsButton}>
            <Settings size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          <View style={styles.waterControls}>
            <TouchableOpacity style={styles.waterControlButton} onPress={decrementWater}>
              <Minus size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.waterControlButton} onPress={incrementWater}>
              <Plus size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Recently Logged Section (Now displays actual scan history) */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Discoveries</Text>
          {recentDiscoveries.length > 0 ? (
            recentDiscoveries.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={[styles.loggedItemCard, { backgroundColor: colors.card }]}
                onPress={() => router.push({ pathname: '/scan-result', params: { scanResult: JSON.stringify(item), imageData: item.imageData } })}
              >
                <View style={styles.loggedItemImageContainer}>
                  {item.imageData ? (
                    <Image
                      source={{ uri: item.imageData }}
                      style={styles.loggedItemImage}
                      onError={(e) => console.log('Image error:', e.nativeEvent.error)}
                    />
                  ) : (
                    <View style={[styles.loggedItemIconContainer, { backgroundColor: colors.primary + '20' }]}>
                      <Text style={{ fontSize: 32 }}>{item.emoji || '🔍'}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.loggedItemContent}>
                  <Text style={[styles.loggedItemName, { color: colors.text }]} numberOfLines={1}>
                    {item.name}
                  </Text>
                  <View style={styles.loggedItemDetailRow}>
                    <Text style={[styles.loggedItemDetailText, { color: colors.textSecondary }]}>
                      {item.scientificName}
                    </Text>
                  </View>
                  <View style={[styles.confidenceBadge, {backgroundColor: colors.success + '20'}]}>
                    <Text style={[styles.confidenceBadgeText, {color: colors.success}]}>{item.confidence}% Confidence</Text>
                  </View>
                </View>
                <Text style={[styles.loggedItemTime, { color: colors.textSecondary }]}>
                  {new Date(item.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </TouchableOpacity>
            ))
          ) : (
            <View style={[styles.emptyStateCard, {backgroundColor: colors.card}]}>
              <Text style={[styles.emptyStateText, {color: colors.textSecondary}]}>Your discoveries will appear here after your first scan!</Text>
            </View>
          )}
        </View>
      </ScrollView>
      <SubscriptionModal
          visible={isSubscriptionModalVisible}
          onClose={() => setSubscriptionModalVisible(false)}
        />
    </SafeAreaView>
  );
}

// Styles adapted to match the provided image
const getDashboardStyles = (colors: any, getScaledFontSize: (size: number) => number) => StyleSheet.create({
  container: { flex: 1, backgroundColor: colors.background },
  scrollContentContainer: { paddingVertical: 20, paddingHorizontal: 20 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 0,
  },
  appLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  appNameText: {
    color: colors.text,
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(24),
  },
  fireIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  fireIconText: {
    color: colors.primary,
    fontFamily: 'Inter-SemiBold',
    fontSize: getScaledFontSize(14),
  },
  dateSelectorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  dateBubble: {
    width: 48,
    height: 60,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dateDayText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: getScaledFontSize(12),
    marginBottom: 4,
  },
  dateNumberText: {
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(18),
  },
  activityCardsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 16,
  },
  activityCard: {
    flex: 1,
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 5,
  },
  activityValue: {
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(28),
    marginBottom: 4,
  },
  activityGoalText: { // This style is no longer used, but kept for reference
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(18),
    color: colors.textSecondary,
  },
  activityLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(14),
    marginBottom: 16,
  },
  statIconLargeContainer: { // New style for larger icons in activity cards
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 10,
  },
  circularProgressContainer: { // This style is no longer used, but kept for reference
    width: 80,
    height: 80,
    borderRadius: 40,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginTop: 10,
  },
  circularProgressBackground: { // This style is no longer used, but kept for reference
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 8,
    opacity: 0.3,
    position: 'absolute',
  },
  circularProgressFill: { // This style is no longer used, but kept for reference
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 8,
    borderLeftColor: 'transparent',
    borderBottomColor: 'transparent',
    position: 'absolute',
    transform: [{ rotateZ: '-135deg' }],
  },
  circularProgressIcon: { // This style is no longer used, but kept for reference
    position: 'absolute',
  },
  calorieDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  calorieIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  calorieDetailText: {
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(14),
    flex: 1,
  },
  calorieDetailValue: {
    fontFamily: 'Inter-SemiBold',
    fontSize: getScaledFontSize(14),
  },
  waterCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 24,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 5,
  },
  waterIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 16,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  waterContent: {
    flex: 1,
  },
  waterLabel: {
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(16),
    marginBottom: 2,
  },
  waterValue: {
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(14),
  },
  waterSettingsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    marginRight: 10,
  },
  waterControls: {
    flexDirection: 'row',
    gap: 8,
  },
  waterControlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: { marginBottom: 24 },
  sectionTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(18),
    marginBottom: 16,
  },
  loggedItemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    padding: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 5,
    elevation: 2,
  },
  loggedItemImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 12,
  },
  loggedItemImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  loggedItemIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  loggedItemContent: {
    flex: 1,
  },
  loggedItemName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: getScaledFontSize(16),
    marginBottom: 4,
  },
  loggedItemDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 2,
  },
  loggedItemDetailText: {
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(13),
  },
  macroContainer: { // Not used for scan history, but kept in styles if needed elsewhere
    flexDirection: 'row',
    marginTop: 4,
    gap: 12,
  },
  macroText: { // Not used for scan history
    fontFamily: 'Inter-SemiBold',
    fontSize: getScaledFontSize(13),
  },
  loggedItemTime: {
    fontFamily: 'Inter-Regular',
    fontSize: getScaledFontSize(12),
    alignSelf: 'flex-start',
  },
  confidenceBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  confidenceBadgeText: {
    fontFamily: 'Inter-Bold',
    fontSize: getScaledFontSize(11),
  },
  emptyStateCard: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  emptyStateText: {
    fontFamily: 'Inter-Medium',
    fontSize: getScaledFontSize(14),
    textAlign: 'center',
  },
});