import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface TodoItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

export default function TodoScreen() {
  const colorScheme = useColorScheme();
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [newTodoText, setNewTodoText] = useState('');
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);

  // Helper functions for colors
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const getBorderColor = () => {
    return colorScheme === 'dark' ? '#3C3C3E' : '#E5E5EA';
  };

  // Mock data for initial todos
  useEffect(() => {
    const mockTodos: TodoItem[] = [
      {
        id: '1',
        text: 'Scan 5 different plant species',
        completed: false,
        createdAt: new Date(),
      },
      {
        id: '2',
        text: 'Complete daily water intake goal',
        completed: true,
        createdAt: new Date(),
      },
      {
        id: '3',
        text: 'Review scan history from this week',
        completed: false,
        createdAt: new Date(),
      },
    ];
    setTodos(mockTodos);
  }, []);

  const addTodo = () => {
    if (newTodoText.trim()) {
      const newTodo: TodoItem = {
        id: Date.now().toString(),
        text: newTodoText.trim(),
        completed: false,
        createdAt: new Date(),
      };
      setTodos([newTodo, ...todos]);
      setNewTodoText('');
      setIsAddModalVisible(false);
    }
  };

  const toggleTodo = (id: string) => {
    setTodos(todos.map(todo => 
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id: string) => {
    Alert.alert(
      'Delete Todo',
      'Are you sure you want to delete this todo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => {
          setTodos(todos.filter(todo => todo.id !== id));
        }},
      ]
    );
  };

  const completedCount = todos.filter(todo => todo.completed).length;
  const totalCount = todos.length;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Todo List</ThemedText>
        <ThemedText style={styles.subtitle}>
          {completedCount} of {totalCount} completed
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: getBorderColor() }]}> {/* Changed from Colors[colorScheme ?? 'light'].border */}
          <View 
            style={[
              styles.progressFill, 
              { 
                backgroundColor: Colors[colorScheme ?? 'light'].tint,
                width: `${totalCount > 0 ? (completedCount / totalCount) * 100 : 0}%`
              }
            ]} 
          />
        </View>
        <ThemedText style={styles.progressText}>
          {totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0}% Complete
        </ThemedText>
      </ThemedView>

      <ScrollView style={styles.todoList} showsVerticalScrollIndicator={false}>
        {todos.map((todo) => (
          <TouchableOpacity
            key={todo.id}
            style={[
              styles.todoItem,
              { backgroundColor: getCardColor() }, // Changed from Colors[colorScheme ?? 'light'].card
              todo.completed && styles.completedTodo
            ]}
            onPress={() => toggleTodo(todo.id)}
          >
            <View style={styles.todoContent}>
              <IconSymbol 
                size={24} 
                name={todo.completed ? "checkmark.circle.fill" : "circle"} 
                color={todo.completed ? Colors[colorScheme ?? 'light'].tint : Colors[colorScheme ?? 'light'].text} 
              />
              <ThemedText 
                style={[
                  styles.todoText,
                  todo.completed && styles.completedText
                ]}
                numberOfLines={2}
              >
                {todo.text}
              </ThemedText>
            </View>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => deleteTodo(todo.id)}
            >
              <IconSymbol size={20} name="trash" color={Colors[colorScheme ?? 'light'].text} />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
        
        {todos.length === 0 && (
          <ThemedView style={styles.emptyState}>
            <IconSymbol size={64} name="checklist" color={Colors[colorScheme ?? 'light'].text} />
            <ThemedText style={styles.emptyText}>No todos yet</ThemedText>
            <ThemedText style={styles.emptySubtext}>
              Add your first todo to get started
            </ThemedText>
          </ThemedView>
        )}
      </ScrollView>

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
        onPress={() => setIsAddModalVisible(true)}
      >
        <IconSymbol size={24} name="plus" color="white" />
      </TouchableOpacity>

      <Modal
        visible={isAddModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsAddModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { backgroundColor: getCardColor() }]}> {/* Changed from Colors[colorScheme ?? 'light'].card */}
            <ThemedText type="subtitle" style={styles.modalTitle}>Add New Todo</ThemedText>
            
            <TextInput
              style={[
                styles.textInput,
                { 
                  backgroundColor: Colors[colorScheme ?? 'light'].background,
                  color: Colors[colorScheme ?? 'light'].text,
                  borderColor: getBorderColor() // Changed from Colors[colorScheme ?? 'light'].border
                }
              ]}
              placeholder="Enter todo text..."
              placeholderTextColor={Colors[colorScheme ?? 'light'].text + '80'}
              value={newTodoText}
              onChangeText={setNewTodoText}
              multiline
              autoFocus
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: getBorderColor() }]} // Changed from Colors[colorScheme ?? 'light'].border
                onPress={() => {
                  setNewTodoText('');
                  setIsAddModalVisible(false);
                }}
              >
                <ThemedText>Cancel</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
                onPress={addTodo}
              >
                <Text style={styles.addButtonText}>Add Todo</Text>
              </TouchableOpacity>
            </View>
          </ThemedView>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  progressContainer: {
    marginBottom: 30,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 14,
    opacity: 0.7,
  },
  todoList: {
    flex: 1,
  },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedTodo: {
    opacity: 0.6,
  },
  todoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  todoText: {
    marginLeft: 12,
    flex: 1,
    fontSize: 16,
  },
  completedText: {
    textDecorationLine: 'line-through',
    opacity: 0.7,
  },
  deleteButton: {
    padding: 8,
  },
  addButton: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 8,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 16,
    padding: 24,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
