<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_leaf"
            android:contentDescription="Biosapex Icon"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Biosapex"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary" />

    </LinearLayout>

    <!-- Recent Scan Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Recent Scan"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/widget_recent_scan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Oak Tree"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- Stats and Actions Row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Scan Count -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Scans"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:id="@+id/widget_scan_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="42"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/widget_accent" />

        </LinearLayout>

        <!-- Quick Scan Button -->
        <LinearLayout
            android:id="@+id/widget_scan_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/widget_button_background"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="center">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_camera"
                android:contentDescription="Scan"
                android:layout_marginEnd="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Scan"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/widget_button_text" />

        </LinearLayout>

    </LinearLayout>

    <!-- Last Updated -->
    <TextView
        android:id="@+id/widget_last_updated"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Updated: 12:34"
        android:textSize="10sp"
        android:textColor="@color/widget_text_secondary"
        android:gravity="end"
        android:layout_marginTop="8dp" />

</LinearLayout>
