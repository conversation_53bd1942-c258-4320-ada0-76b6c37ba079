<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/quick_scan_widget_background"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_camera_white"
            android:contentDescription="Camera Icon"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Quick Scan"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_white" />

    </LinearLayout>

    <!-- Scan Button -->
    <LinearLayout
        android:id="@+id/widget_scan_button"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/widget_scan_button_background"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="12dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_viewfinder"
            android:contentDescription="Scan"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/widget_scan_button_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Scan Now"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_white" />

    </LinearLayout>

    <!-- Scan Count Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Remaining Scans -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="start|center_vertical">

            <TextView
                android:id="@+id/widget_scan_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/widget_text_white"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/widget_scan_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="scans left"
                android:textSize="10sp"
                android:textColor="@color/widget_text_white_secondary" />

        </LinearLayout>

        <!-- Today's Count -->
        <TextView
            android:id="@+id/widget_today_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3 today"
            android:textSize="10sp"
            android:textColor="@color/widget_text_white_secondary" />

    </LinearLayout>

</LinearLayout>
