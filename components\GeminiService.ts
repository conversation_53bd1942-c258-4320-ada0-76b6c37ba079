    // ========== TYPESCRIPT DEFINITIONS ==========
    import ValidationService from './ValidationService';
    interface GeminiResponse {
      candidates: Array<{
        content: {
          parts: Array<{ text: string }>;
        };
      }>;
    }

    /* ---------- CATEGORIES ---------- */
    export type Category =
      | 'Electronics'
      | 'Plant'
      | 'Animal'
      | 'Food'
      | 'Wine / Beverage'
      | 'Coin / Currency'
      | 'Unknown'
      | 'Insect'
      | 'Rock / Mineral'
      | 'Product';


    /* ---------- CORE INTERFACES ---------- */
    export interface PhysicalTraits {
      size?: string;
      color?: string;
      shape?: string;
      texture?: string;
      weight?: string;
      [key: string]: any;
    }

    export interface NutritionalInfo {
      totalCalories: number;
      caloriesPerServing?: number; // Made optional to match new prompt
      servingSize?: string;
      protein: number; // grams
      carbs: number; // grams
      fat: number; // grams
      fiber?: number; // Made optional
      sugar?: number; // Made optional
      sodium?: number; // Made optional
      allergens?: string[]; // Made optional
      dietaryRestrictions?: string[]; // Made optional
    }

    export interface FoodAnalysis {
      dishName: string;
      cuisine?: string;
      mealType?: string;
      preparationMethod?: string;
      mainIngredients: string[];
      ingredientBreakdown?: Array<{ name: string; amount: string; calories: number }>;
      healthBenefits: string[];
      potentialConcerns: string[];
      healthScore?: number; // Made optional
      dietaryTags?: string[];
      foodGroup?: string;
    }

    export interface PlantInfo {
        family?: string;
        nativeRegion?: string;
        sunRequirements?: string;
        waterNeeds?: string;
        careInstructions?: string[];
        toxicity?: string;
    }

    export interface AnimalInfo {
        habitat?: string;
        diet?: string;
        lifespan?: string;
        conservationStatus?: string;
        behavior?: string[]; // Kept from old interface
        interestingFacts?: string[];
    }

    export interface ElectronicsInfo {
        brand?: string;
        model?: string;
        category?: string;
        estimatedPrice?: string;
        specifications?: Record<string, string>;
        features?: string[];
    }

    export interface WineInfo {
        wineType?: string;
        vintage?: number;
        region?: string;
        alcoholContent?: number;
        servingTemperature?: string;
        grapeVariety?: string[];
        tastingNotes?: string[];
        pairings?: string[];
    }

    export interface CoinInfo {
        country?: string;
        year?: number;
        denomination?: string;
        composition?: string;
        condition?: string;
        rarity?: string;
        estimatedValue?: string;
    }


    /* ---------- MAIN RESULT INTERFACE ---------- */
    export interface IdentificationResult {
      name: string;
      scientificName: string;
      confidence: number;
      category: Category;
      description: string;
      scanQuality?: 'excellent' | 'good' | 'fair' | 'poor';
      physicalTraits?: PhysicalTraits;
      alternatives?: Array<{ name: string; scientificName: string; confidence: number }>;
      distinguishingFeatures?: string[];
      usage?: string[];
      safety?: string[];
      recommendedActions?: string[];

      /* Category-specific info from new prompt */
      electronicsInfo?: ElectronicsInfo;
      plantInfo?: PlantInfo;
      animalInfo?: AnimalInfo;
      nutritionalInfo?: NutritionalInfo; // This was already here, but matches new prompt
      foodAnalysis?: FoodAnalysis; // This was already here
      wineInfo?: WineInfo; // This was already here
      coinInfo?: CoinInfo; // This was already here
    }

    // ========== GEMINI SERVICE CLASS ==========
    export class GeminiService {
      private apiKey: string;
      private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent';
      // Caching mechanism
      private cache: Map<string, IdentificationResult> = new Map();
      private maxCacheSize = 50; // Store up to 50 recent results

      constructor() {
        this.apiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';
        if (!this.apiKey) {
          console.warn('Gemini API key not found. Ensure EXPO_PUBLIC_GEMINI_API_KEY is set.');
        }
      }

      async identifySpecimen(imageBase64: string, additionalContext?: string): Promise<IdentificationResult> {
        // Create a unique key for the cache based on the image and context.
        // Using a slice of the base64 to keep the key reasonably short.
        const cacheKey = `${imageBase64.slice(0, 20)}${imageBase64.slice(-20)}:${additionalContext || ''}`;

        // Check cache first
        if (this.cache.has(cacheKey)) {
          console.log('Returning response from cache.');
          // Return a deep copy to prevent mutation of the cached object
          return JSON.parse(JSON.stringify(this.cache.get(cacheKey)!));
        }

        // Validate API key
        if (!this.apiKey) {
          throw new Error('AI model API key is not configured. Please set EXPO_PUBLIC_GEMINI_API_KEY.');
        }

        const apiKeyValidation = ValidationService.validateApiKey(this.apiKey);
        if (!apiKeyValidation.isValid) {
          throw new Error(`Invalid API key: ${apiKeyValidation.errors.join(', ')}`);
        }

        // Validate image data
        const imageValidation = ValidationService.validateImageBase64(imageBase64);
        if (!imageValidation.isValid) {
          throw new Error(`Invalid image data: ${imageValidation.errors.join(', ')}`);
        }

        // Validate additional context if provided
        if (additionalContext) {
          const contextValidation = ValidationService.validateString(additionalContext, {
            maxLength: 1000,
            sanitizer: ValidationService.sanitizeForDisplay
          });
          if (!contextValidation.isValid) {
            throw new Error(`Invalid context: ${contextValidation.errors.join(', ')}`);
          }
          additionalContext = contextValidation.sanitizedValue;
        }

        const prompt = this.buildPrompt(additionalContext);

        try {
          const payload = {
            contents: [{
              parts: [
                { text: prompt },
                { inlineData: { mimeType: 'image/jpeg', data: imageBase64 } }
              ]
            }],
            generationConfig: {
              temperature: 0.1,
              topK: 32,
              topP: 1,
              maxOutputTokens: 4096
            }
          };

          const apiUrl = `${this.baseUrl}?key=${this.apiKey}`;

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            const errorBody = await response.json().catch(() => ({}));
            console.error('AI model API error:', response.status, errorBody);
            throw new Error(`AI model error: ${errorBody.error?.message || response.statusText || 'Unknown error'}`);
          }

          const data: GeminiResponse = await response.json();

          // Validate API response structure
          const responseValidation = ValidationService.validateGeminiResponse(data);
          if (!responseValidation.isValid) {
            throw new Error(`Invalid API response: ${responseValidation.errors.join(', ')}`);
          }

          const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
          if (!text) {
            throw new Error('Empty or malformed response from AI model');
          }

          const result = this.parseResult(text);

          // Store successful result in cache
          this.updateCache(cacheKey, result);

          // Validate the parsed result
          const resultValidation = ValidationService.validateIdentificationResult(result);
          if (!resultValidation.isValid) {
            console.warn('Result validation warnings:', resultValidation.errors);
            // Don't throw error for result validation, just log warnings
          }

          return result;
        } catch (error) {
          console.error('Error in identifySpecimen:', error);
          throw error;
        }
      }

      // Simple method for testing API connection
      async testConnection(): Promise<{ success: boolean; responseTime: number; error?: string }> {
        const startTime = Date.now();

        try {
          const testPayload = {
            contents: [{
              parts: [{ text: 'Hello, this is a test message. Please respond with "API connection successful".' }]
            }],
            generationConfig: {
              temperature: 0.1,
              maxOutputTokens: 50
            }
          };

          const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testPayload)
          });

          const responseTime = Date.now() - startTime;

          if (response.ok) {
            return { success: true, responseTime };
          } else {
            const errorData = await response.json().catch(() => ({}));
            return {
              success: false,
              responseTime,
              error: errorData.error?.message || `HTTP ${response.status}`
            };
          }
        } catch (error) {
          const responseTime = Date.now() - startTime;
          return {
            success: false,
            responseTime,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      }

      /**
       * Clears the entire identification cache.
       */
      public clearCache(): void {
        this.cache.clear();
        console.log('Identification cache cleared.');
      }

      /**
       * Adds a result to the cache and enforces the size limit.
       * @param key The cache key for the result.
       * @param result The identification result to cache.
       */
      private updateCache(key: string, result: IdentificationResult): void {
        if (this.cache.size >= this.maxCacheSize) {
          // Evict the oldest entry (Map maintains insertion order)
          const oldestKey = this.cache.keys().next().value;
          // Ensure oldestKey is not undefined before deleting
          if (oldestKey !== undefined) {
              this.cache.delete(oldestKey);
          }
        }
        this.cache.set(key, result);
      }

      // ========== UPDATED PROMPT METHOD ==========
      private buildPrompt(ctx?: string): string {
        return `
    You are a **device identification expert**. Your job is to identify objects and provide **technical specifications** rather than visual descriptions.

    ${ctx ? `User context: ${ctx}` : ''}

    ## CRITICAL INSTRUCTIONS BY CATEGORY:

    **📱 ELECTRONICS (Computers, Phones, Tablets):**
    - **NEVER** describe visual appearance (e.g., "silver laptop")
    - **MUST** provide: Brand, Exact Model, OS, Release Year, Price Range
    - **MUST** include: Processor, RAM, Storage, Screen Size, Key Features
    - Example: Instead of "I see a computer" → "MacBook Pro 16" M2 Max, 32GB RAM, 1TB SSD, $3,499"

    **🌿 PLANTS:**
    - Species name, care instructions, watering needs, sunlight requirements
    - Toxicity warnings for pets/children
    - Growth patterns and maintenance tips

    **🐾 ANIMALS:**
    - Species identification, habitat information
    - Diet, behavior patterns, interesting facts
    - Conservation status if applicable

    **🍽️ FOOD:**
    - Dish name, nutritional breakdown, ingredients
    - Health benefits, allergens, dietary tags
    - Calorie count per serving

    **🍷 WINE/BEVERAGES:**
    - Type, vintage, region, alcohol content
    - Tasting notes, food pairings, serving temperature

    **💰 COINS/CURRENCY:**
    - Country, year, denomination, composition
    - Estimated value, rarity, condition
    - Historical significance

    **📊 RESPONSE FORMAT:**
    Return **only** JSON with this exact structure:

    {
      "name": "Exact product/species name",
      "scientificName": "Scientific name or N/A",
      "confidence": 0-100,
      "category": "Electronics|Plant|Animal|Food|Wine|Coin|Unknown",
      "description": "Technical specifications and key details",
      "electronicsInfo": {
        "brand": "Apple",
        "model": "MacBook Pro 16-inch M2 Max",
        "category": "Laptop",
        "estimatedPrice": "$2,999-$4,299",
        "specifications": {
          "Processor": "Apple M2 Max 12-core CPU",
          "RAM": "32GB unified memory",
          "Storage": "1TB SSD",
          "Display": "16.2\" Liquid Retina XDR",
          "OS": "macOS Sonoma",
          "Release Year": "2023"
        },
        "features": ["38-core GPU", "22hr battery", "MagSafe 3", "Thunderbolt 4"]
      },
      "plantInfo": {
        "family": "Araceae",
        "nativeRegion": "Central America",
        "sunRequirements": "Bright indirect light",
        "waterNeeds": "Water when top 2 inches dry",
        "careInstructions": ["Rotate weekly", "Mist leaves", "Well-draining soil"],
        "toxicity": "Toxic to cats and dogs"
      },
      "animalInfo": {
        "habitat": "North American forests",
        "diet": "Omnivorous - fruits, nuts, small animals",
        "lifespan": "12-18 years in wild",
        "conservationStatus": "Least Concern",
        "interestingFacts": ["Can remember human faces", "Uses tools to solve problems"]
      },
      "nutritionalInfo": {
        "totalCalories": 350,
        "protein": 25,
        "carbs": 45,
        "fat": 12,
        "servingSize": "1 cup"
      }
    }

    **CONFIDENCE RULES:**
    - 90-100%: Clear identification with all specs
    - 70-89%: Good identification, some specs estimated
    - 50-69%: Uncertain, provide closest match
    - <50%: Mark as unknown

    Focus on **accuracy over verbosity**. Skip visual descriptions entirely.
    `.trim();
      }

      private parseResult(raw: string): IdentificationResult {
        try {
          const cleaned = raw.replace(/^```(?:json)?\s*|```\s*$/g, '').trim();
          const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
          if (!jsonMatch) throw new Error('No JSON found');
          const p = JSON.parse(jsonMatch[0]);

          // Explicitly determine category value to help TypeScript's type inference
          const categoryValue = (typeof p.category === 'string' && p.category) 
                                ? p.category 
                                : 'Unknown';

          const result: IdentificationResult = {
            name: p.name || 'Unidentified',
            scientificName: p.scientificName || 'N/A',
            confidence: Math.min(100, Math.max(0, p.confidence ?? 0)),
            category: categoryValue as Category, // Apply the cast here
            description: p.description || 'No description available.',
            scanQuality: p.scanQuality || 'good',
            physicalTraits: p.physicalTraits || {},
            alternatives: Array.isArray(p.alternatives) ? p.alternatives : [],
            distinguishingFeatures: Array.isArray(p.distinguishingFeatures) ? p.distinguishingFeatures : [],
            usage: Array.isArray(p.usage) ? p.usage : [],
            safety: Array.isArray(p.safety) ? p.safety : [],
            recommendedActions: Array.isArray(p.recommendedActions) ? p.recommendedActions : [],

            // Category-specific info
            nutritionalInfo: p.nutritionalInfo,
            foodAnalysis: p.foodAnalysis,
            plantInfo: p.plantInfo,
            animalInfo: p.animalInfo,
            electronicsInfo: p.electronicsInfo,
            wineInfo: p.wineInfo,
            coinInfo: p.coinInfo,
          };
          return result;
        } catch (err) {
          console.error('Parse error:', err);
          const errorResult: IdentificationResult = {
            name: 'Parsing Failed',
            scientificName: 'N/A',
            confidence: 0,
            category: 'Unknown',
            description: 'Could not parse AI model response. Please try again.',
          };
          return errorResult;
        }
      }
    }

    export default GeminiService;