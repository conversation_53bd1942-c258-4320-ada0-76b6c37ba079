package com.biosapex.app

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews

/**
 * Quick Scan Widget Provider for Android
 * Provides fast access to camera scanning with subscription status
 */
class QuickScanWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }

    companion object {
        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Get widget data
            val subscriptionData = getSubscriptionData(context)
            val remainingScans = subscriptionData.remainingScans
            val isUnlimited = subscriptionData.isUnlimited
            val todayScans = getTodayScans(context)

            // Construct the RemoteViews object
            val views = RemoteViews(context.packageName, R.layout.quick_scan_widget)
            
            // Update scan count display
            if (isUnlimited) {
                views.setTextViewText(R.id.widget_scan_count, "∞")
                views.setTextViewText(R.id.widget_scan_label, "Unlimited")
            } else if (remainingScans == 0) {
                views.setTextViewText(R.id.widget_scan_count, "0")
                views.setTextViewText(R.id.widget_scan_label, "Subscribe")
            } else {
                views.setTextViewText(R.id.widget_scan_count, remainingScans.toString())
                views.setTextViewText(R.id.widget_scan_label, "scans left")
            }
            
            views.setTextViewText(R.id.widget_today_count, "$todayScans today")

            // Create intent to open camera when scan button is tapped
            val scanIntent = Intent(context, MainActivity::class.java)
            scanIntent.putExtra("action", "quick_scan")
            scanIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val scanPendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId,
                scanIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_scan_button, scanPendingIntent)

            // Create intent to open app when widget is tapped
            val appIntent = Intent(context, MainActivity::class.java)
            appIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val appPendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId + 1000,
                appIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, appPendingIntent)

            // Update widget appearance based on subscription status
            if (!isUnlimited) {
                // Show subscription prompt for users without subscription
                views.setInt(R.id.widget_scan_button, "setBackgroundResource", R.drawable.widget_button_disabled)
                views.setTextViewText(R.id.widget_scan_button_text, "Start Trial")

                // Create intent to subscription page
                val upgradeIntent = Intent(context, MainActivity::class.java)
                upgradeIntent.putExtra("action", "show_subscription")
                upgradeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                val upgradePendingIntent = PendingIntent.getActivity(
                    context,
                    appWidgetId + 2000,
                    upgradeIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                views.setOnClickPendingIntent(R.id.widget_scan_button, upgradePendingIntent)
            } else {
                views.setInt(R.id.widget_scan_button, "setBackgroundResource", R.drawable.widget_button_background)
                views.setTextViewText(R.id.widget_scan_button_text, "Scan")
            }

            // Instruct the widget manager to update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }

        private data class SubscriptionData(
            val isUnlimited: Boolean,
            val remainingScans: Int,
            val tier: String?
        )

        private fun getSubscriptionData(context: Context): SubscriptionData {
            // In real implementation, read from SharedPreferences or database
            val prefs = context.getSharedPreferences("biosapex_prefs", Context.MODE_PRIVATE)
            val isActive = prefs.getBoolean("subscription_active", false)
            val isTrialActive = prefs.getBoolean("trial_active", false)
            val tier = prefs.getString("subscription_tier", null)

            return if (isActive || isTrialActive) {
                SubscriptionData(isUnlimited = true, remainingScans = -1, tier = tier)
            } else {
                // No free tier - require subscription
                SubscriptionData(
                    isUnlimited = false,
                    remainingScans = 0,
                    tier = null
                )
            }
        }

        private fun getTodayScans(context: Context): Int {
            val prefs = context.getSharedPreferences("biosapex_prefs", Context.MODE_PRIVATE)
            return prefs.getInt("daily_scans_used", 0)
        }
    }
}
