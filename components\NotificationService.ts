import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

interface NotificationPermission {
  granted: boolean;
  denied: boolean;
}

interface NotificationOptions {
  title: string;
  body: string;
  data?: any;
  sound?: boolean;
  badge?: number;
  categoryIdentifier?: string;
  image?: string;
}

interface ScheduledNotificationOptions extends NotificationOptions {
  trigger: Date | number; // Date for specific time, number for seconds from now
}

// Configure notification behavior with enhanced settings
Notifications.setNotificationHandler({
  handleNotification: async (notification) => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    priority: Notifications.AndroidNotificationPriority.HIGH,
  }),
});

// iOS Notification Categories for interactive notifications
const notificationCategories = [
  {
    identifier: 'SCAN_RESULT',
    actions: [
      {
        identifier: 'VIEW_DETAILS',
        buttonTitle: 'View Details',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'SAVE_SCAN',
        buttonTitle: 'Save',
        options: {
          opensAppToForeground: false,
        },
      },
    ],
  },
  {
    identifier: 'ACHIEVEMENT',
    actions: [
      {
        identifier: 'VIEW_ACHIEVEMENT',
        buttonTitle: 'View',
        options: {
          opensAppToForeground: true,
        },
      },
    ],
  },
];

export class NotificationService {
  private static instance: NotificationService;
  private permission: NotificationPermission = { granted: false, denied: false };
  private pushToken: string | null = null;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<void> {
    try {
      // Set up iOS notification categories
      if (Platform.OS === 'ios') {
        await Notifications.setNotificationCategoryAsync('SCAN_RESULT', notificationCategories[0].actions);
        await Notifications.setNotificationCategoryAsync('ACHIEVEMENT', notificationCategories[1].actions);
      }

      // Set up Android notification channels
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      // Get push token for remote notifications
      await this.registerForPushNotifications();
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  private async setupAndroidChannels(): Promise<void> {
    // Default channel
    await Notifications.setNotificationChannelAsync('default', {
      name: 'Default Notifications',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#6366F1',
      sound: 'default',
    });

    // Scan results channel
    await Notifications.setNotificationChannelAsync('scan_results', {
      name: 'Scan Results',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#10B981',
      sound: 'default',
      description: 'Notifications for completed scans',
    });

    // Achievements channel
    await Notifications.setNotificationChannelAsync('achievements', {
      name: 'Achievements',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 100, 100, 100],
      lightColor: '#F59E0B',
      sound: 'default',
      description: 'Achievement and milestone notifications',
    });

    // Community channel
    await Notifications.setNotificationChannelAsync('community', {
      name: 'Community Updates',
      importance: Notifications.AndroidImportance.LOW,
      vibrationPattern: [0, 150],
      lightColor: '#8B5CF6',
      sound: 'default',
      description: 'Community news and updates',
    });
  }

  async requestPermission(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowDisplayInCarPlay: true,
            allowCriticalAlerts: false,
            provideAppNotificationSettings: true,
            allowProvisional: false,
            allowAnnouncements: true,
          },
        });
        finalStatus = status;
      }

      this.permission.granted = finalStatus === 'granted';
      this.permission.denied = finalStatus === 'denied';

      if (this.permission.granted) {
        await this.initialize();
      }

      return this.permission.granted;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  private async registerForPushNotifications(): Promise<void> {
    try {
      if (Device.isDevice) {
        const token = await Notifications.getExpoPushTokenAsync({
          projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
        });
        this.pushToken = token.data;
        console.log('Push token:', this.pushToken);
      } else {
        console.log('Must use physical device for Push Notifications');
      }
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  }

  async scheduleNotification(title: string, body: string, data?: any, trigger?: Date | number): Promise<string | null> {
    if (!this.permission.granted) {
      const granted = await this.requestPermission();
      if (!granted) {
        console.warn('Notification permission not granted');
        return null;
      }
    }

    try {
      let notificationTrigger = null;

      if (trigger) {
        if (trigger instanceof Date) {
          notificationTrigger = trigger;
        } else {
          notificationTrigger = { seconds: trigger };
        }
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
          badge: 1,
        },
        trigger: notificationTrigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  }

  async sendNotification(options: NotificationOptions): Promise<boolean> {
    if (!this.permission.granted) {
      const granted = await this.requestPermission();
      if (!granted) {
        console.warn('Notification permission not granted');
        return false;
      }
    }

    try {
      const content: any = {
        title: options.title,
        body: options.body,
        data: options.data || {},
        sound: options.sound !== false,
        badge: options.badge,
      };

      // Add category for iOS interactive notifications
      if (Platform.OS === 'ios' && options.categoryIdentifier) {
        content.categoryIdentifier = options.categoryIdentifier;
      }

      // Add image attachment if provided
      if (options.image) {
        content.attachments = [
          {
            identifier: 'image',
            url: options.image,
            options: {
              typeHint: 'public.jpeg',
            },
          },
        ];
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content,
        trigger: null, // Show immediately
      });

      return notificationId !== null;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  async scheduleDelayedNotification(options: ScheduledNotificationOptions): Promise<string | null> {
    return await this.scheduleNotification(
      options.title,
      options.body,
      options.data,
      options.trigger
    );
  }

  async sendScanResultNotification(speciesName: string, confidence: number, imageUrl?: string): Promise<void> {
    await this.sendNotification({
      title: 'Scan Complete! 🔍',
      body: `Identified: ${speciesName} (${Math.round(confidence)}% confidence)`,
      data: {
        type: 'scan-result',
        species: speciesName,
        confidence,
        screen: 'scan-result'
      },
      categoryIdentifier: Platform.OS === 'ios' ? 'SCAN_RESULT' : undefined,
      image: imageUrl,
    });
  }

  async sendAchievementNotification(achievement: string, description?: string): Promise<void> {
    await this.sendNotification({
      title: 'Achievement Unlocked! 🏆',
      body: achievement,
      data: {
        type: 'achievement',
        achievement,
        description,
        screen: 'achievements'
      },
      categoryIdentifier: Platform.OS === 'ios' ? 'ACHIEVEMENT' : undefined,
    });
  }

  async sendCommunityNotification(message: string, actionUrl?: string): Promise<void> {
    await this.sendNotification({
      title: 'BioScan Community 👥',
      body: message,
      data: {
        type: 'community',
        message,
        actionUrl,
        screen: 'community'
      },
    });
  }

  async sendReminderNotification(title: string, body: string, delayInSeconds: number): Promise<string | null> {
    return await this.scheduleDelayedNotification({
      title,
      body,
      data: { type: 'reminder' },
      trigger: delayInSeconds,
    });
  }

  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  async cancelNotificationsByType(type: string): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const notificationsToCancel = scheduledNotifications.filter(
        notification => notification.content.data?.type === type
      );

      for (const notification of notificationsToCancel) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Error canceling notifications by type:', error);
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  async clearBadge(): Promise<void> {
    await this.setBadgeCount(0);
  }

  getPushToken(): string | null {
    return this.pushToken;
  }

  getPermissionStatus(): NotificationPermission {
    return this.permission;
  }

  async checkPermissionStatus(): Promise<NotificationPermission> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      this.permission.granted = status === 'granted';
      this.permission.denied = status === 'denied';
      return this.permission;
    } catch (error) {
      console.error('Error checking notification permission:', error);
      return { granted: false, denied: false };
    }
  }

  // Handle notification responses (when user taps on notification)
  addNotificationResponseListener(listener: (response: Notifications.NotificationResponse) => void) {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  // Handle notifications received while app is in foreground
  addNotificationReceivedListener(listener: (notification: Notifications.Notification) => void) {
    return Notifications.addNotificationReceivedListener(listener);
  }

  // Get all scheduled notifications
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }
}

export default NotificationService;
