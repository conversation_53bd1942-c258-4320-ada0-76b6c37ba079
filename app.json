{"expo": {"name": "Biosapex", "slug": "Biosapex", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "biosapex", "userInterfaceStyle": "automatic", "newArchEnabled": true, "description": "AI-powered species identification app for plants, animals, and biological specimens using advanced computer vision technology.", "primaryColor": "#6366F1", "backgroundColor": "#F8FAFC", "ios": {"supportsTablet": true, "bundleIdentifier": "com.biosapex.app", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "Biosapex needs camera access to capture images of biological specimens for AI-powered species identification.", "NSPhotoLibraryUsageDescription": "Biosapex needs photo library access to analyze existing images for species identification.", "NSMicrophoneUsageDescription": "Biosapex may use microphone for voice commands and audio features.", "NSLocationWhenInUseUsageDescription": "Biosapex uses location to provide habitat and regional species information."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.biosapex.app", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "RECORD_AUDIO", "INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-barcode-scanner", ["expo-camera", {"cameraPermission": "Allow Biosapex to access your camera to capture images for species identification."}], ["expo-image-picker", {"photosPermission": "Allow Biosapex to access your photo library to analyze existing images."}], ["@bittingz/expo-widgets", {"ios": {"src": "./widgets/ios", "devTeamId": "your-apple-dev-team-id", "mode": "production", "moduleDependencies": [], "useLiveActivities": false, "frequentUpdates": true, "entitlements": {}}, "android": {"src": "./widgets/android", "widgets": [{"name": "BiosapexWidgetProvider", "resourceName": "@xml/biosapex_widget_info"}, {"name": "QuickScanWidgetProvider", "resourceName": "@xml/quick_scan_widget_info"}, {"name": "TodoWidgetProvider", "resourceName": "@xml/todo_widget_info"}]}}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "your-project-id-here"}}, "owner": "your-expo-username", "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"]}}