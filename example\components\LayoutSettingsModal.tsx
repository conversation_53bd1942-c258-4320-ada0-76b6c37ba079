import React from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Pressable } from 'react-native';
import { useTheme } from './ThemeContext'; // Assuming this path is correct
import { useTextScaling } from './TextScalingContext'; // Assuming this path is correct
import { X } from 'lucide-react-native';
import LayoutSettings from './LayoutSettings';

interface LayoutSettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

// Using React.memo to prevent unnecessary re-renders when parent state changes
const LayoutSettingsModal = React.memo(({ visible, onClose }: LayoutSettingsModalProps) => {
  const { colors } = useTheme();
  const { getScaledFontSize } = useTextScaling();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      {/* Use Pressable for the overlay to allow dismissing the modal by tapping outside */}
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        {/* This Pressable prevents the modal from closing when tapping inside it */}
        <Pressable style={[styles.modalContainer, { backgroundColor: colors.card }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
              Layout Preferences
            </Text>
            <TouchableOpacity onPress={onClose} style={[styles.closeButton, { backgroundColor: colors.backgroundSecondary }]}>
              <X size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            <LayoutSettings />
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    // FIX: Using a slightly more transparent overlay to feel less intrusive.
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20, // Add padding to avoid touching screen edges
  },
  modalContainer: {
    // FIX: Changed from '100%' to '95%' and added maxWidth.
    // This prevents the modal from taking up the full screen width on larger devices (like tablets),
    // which can help avoid unintended layout interference with other UI elements.
    width: '95%',
    maxWidth: 500,
    borderRadius: 24, // Increased border radius for a softer look
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    // Use theme color for the border
  },
  modalTitle: {
    fontFamily: 'Inter-Bold',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    paddingTop: 16,
  },
});

export default LayoutSettingsModal;
