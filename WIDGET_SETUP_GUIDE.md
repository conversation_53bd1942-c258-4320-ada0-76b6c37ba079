# Biosapex Widget Setup Guide

## Overview
Your Biosapex app now has comprehensive widget support with the following widgets:
- **Quick Scan Widget**: Fast access to camera scanning
- **Stats Widget**: Display scan statistics and streaks
- **Subscription Widget**: Show subscription status and trial info
- **Todo Widget**: Task management and reminders

## Current Configuration Status ✅

### 1. Dependencies
- ✅ `@bittingz/expo-widgets` is already installed
- ✅ Widget implementations are complete for both iOS and Android

### 2. App Configuration
- ✅ Expo widgets plugin is configured in `app.json`
- ✅ iOS widget bundle is set up with proper Swift implementations
- ✅ Android widget providers are configured with XML layouts

### 3. Widget Data Management
- ✅ `WidgetDataManager` handles data synchronization between app and widgets
- ✅ Updated to support new subscription model (no free tier + 3-day trial)

## Next Steps to Complete Setup

### Step 1: Update Apple Developer Team ID
Edit `app.json` and replace `"your-apple-dev-team-id"` with your actual Apple Developer Team ID:

```json
"devTeamId": "YOUR_ACTUAL_TEAM_ID"
```

### Step 2: Prebuild for Native Platforms
Run these commands to generate native code with widget support:

```bash
# For iOS
npm run prebuild:ios

# For Android  
npm run prebuild:android

# Or both
npx expo prebuild --clean
```

### Step 3: Build and Test
```bash
# iOS
npx expo run:ios

# Android
npx expo run:android
```

## Widget Features

### Quick Scan Widget
- Shows remaining scans (now shows "Subscribe" for non-subscribers)
- Direct camera access button
- Subscription status indicator

### Stats Widget  
- Daily scan progress
- Species discovery streak
- Weekly goals tracking
- Favorite category display

### Subscription Widget
- Current plan status
- Trial countdown (if active)
- Quick access to subscription screen

### Todo Widget
- Pending tasks count
- Next task preview
- Completion tracking
- Task reminders

## Integration Points

### In Your App Code
Use `WidgetDataManager` to update widget data:

```typescript
import WidgetDataManager from './components/WidgetDataManager';

const widgetManager = WidgetDataManager.getInstance();

// Update after successful scan
await widgetManager.updateScanStats(todayScans, totalSpecies, streak);

// Update subscription status
await widgetManager.updateSubscriptionStatus(subscription);

// Add todo task
await widgetManager.addTodoTask({
  id: 'unique-id',
  text: 'Task description',
  completed: false,
  priority: 'medium',
  createdAt: new Date()
});
```

## Troubleshooting

### Android R Reference Issues
If you encounter Android R reference errors during build:
1. Clean the project: `npx expo prebuild --clean`
2. Ensure all XML files are properly formatted
3. Check that widget provider names match in app.json

### iOS Widget Not Appearing
1. Verify Apple Developer Team ID is correct
2. Ensure proper code signing
3. Check iOS deployment target compatibility

## Testing Widgets

### iOS
1. Build and install app on device/simulator
2. Long press on home screen → Add Widget
3. Find "Biosapex" in widget gallery
4. Add desired widgets to home screen

### Android
1. Build and install app on device/emulator
2. Long press on home screen → Widgets
3. Find "Biosapex" widgets
4. Drag to home screen

Your widget setup is now complete and ready for testing!
