// FileName: components/SubscriptionContext.tsx
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import Purchases, { LOG_LEVEL } from 'react-native-purchases';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

export type SubscriptionTier = 'free' | 'pro' | 'expert';

interface SubscriptionContextType {
  subscription: {
    tier: SubscriptionTier;
    expiration: Date | null;
    trialUsed: boolean;
  };
  upgradeTier: (tier: SubscriptionTier) => Promise<void>;
  isPremiumUser: () => boolean;
}

const SubscriptionContext = createContext<SubscriptionContextType | null>(null);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    const initializeRevenueCat = async () => {
      try {
        if (Platform.OS === 'android') {
          const apiKey = process.env.EXPO_PUBLIC_REVENUECAT_GOOGLE_API_KEY;
          if (!apiKey) {
            throw new Error('RevenueCat API key for Google is not defined');
          }
          Purchases.configure({ apiKey });
        } else if (Platform.OS === 'ios') {
          const apiKey = process.env.EXPO_PUBLIC_REVENUECAT_APPLE_API_KEY;
          if (!apiKey) {
            throw new Error('RevenueCat API key for Apple is not defined');
          }
          Purchases.configure({ apiKey });
        }
        Purchases.setLogLevel(LOG_LEVEL.VERBOSE);
        console.log('RevenueCat SDK initialized successfully');
      } catch (error: unknown) {
        console.error('Failed to initialize RevenueCat SDK:', error);
      }
    };

    initializeRevenueCat();
  }, []);

  const [subscription, setSubscription] = useState<{
    tier: SubscriptionTier;
    expiration: Date | null;
    trialUsed: boolean;
  }>({
    tier: 'free',
    expiration: null,
    trialUsed: false,
  });

  const updateSubscriptionStatus = useCallback(async () => {
    try {
      const customerInfo = await Purchases.getCustomerInfo();
      const premiumEntitlement = customerInfo.entitlements.active['premium'];

      if (premiumEntitlement) {
        setSubscription({
          tier: 'pro',
          expiration: premiumEntitlement.expirationDate ? new Date(premiumEntitlement.expirationDate) : null,
          trialUsed: premiumEntitlement.isSandbox,
        });
      } else {
        setSubscription({
          tier: 'free',
          expiration: null,
          trialUsed: false,
        });
      }
    } catch (error) {
      console.error('Failed to fetch customer info:', error);
    }
  }, []);

  useEffect(() => {
    updateSubscriptionStatus();
    Purchases.addCustomerInfoUpdateListener(updateSubscriptionStatus);
    return () => {
      Purchases.removeCustomerInfoUpdateListener(updateSubscriptionStatus);
    };
  }, []); // Remove updateSubscriptionStatus from dependencies to prevent infinite re-renders

  const upgradeTier = async (newTier: SubscriptionTier) => {
    try {
      console.log(`Attempting to upgrade to tier: ${newTier}`);
      const offerings = await Purchases.getOfferings();
      const availablePackages = offerings.current?.availablePackages;
      if (!availablePackages) {
        Alert.alert('Upgrade Failed', 'No products found.');
        return;
      }

      const packageToPurchase = availablePackages.find(
        (pkg) => pkg.product.identifier === process.env.EXPO_PUBLIC_REVENUECAT_PRODUCT_ID
      );

      if (!packageToPurchase) {
        Alert.alert('Upgrade Failed', 'Product not found.');
        return;
      }

      const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
      if (customerInfo.entitlements.active['premium']) {
        await updateSubscriptionStatus();
        Alert.alert('Subscription Successful', 'You are now a premium user.', [
          { text: 'OK', onPress: () => promptGoogleSignIn() },
        ]);
      }
    } catch (error: any) {
      if (!error.userCancelled) {
        console.error('Upgrade failed:', error);
        Alert.alert('Upgrade Failed', error.message);
      }
    }
  };

  const promptGoogleSignIn = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      // Here you would typically send the token to your backend to associate the user with the subscription
      console.log('Google Sign-In successful, user:', userInfo);
      Alert.alert('Sync Enabled', 'Your subscription is now linked to your Google account and will sync across devices.');
    } catch (error) {
      console.error('Google Sign-In failed:', error);
      Alert.alert('Sync Failed', 'Could not link your Google account. Please try again from settings.');
    }
  };

  const isPremiumUser = () => {
    return subscription.tier !== 'free';
  };

  return (
    <SubscriptionContext.Provider value={{ subscription, upgradeTier, isPremiumUser }}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};