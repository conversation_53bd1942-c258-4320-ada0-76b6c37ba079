import { router } from 'expo-router';
import { Alert } from 'react-native';
import RevenueCatService from './RevenueCatService';

export interface FeatureAccess {
  hasAccess: boolean;
  reason?: string;
  action?: 'upgrade' | 'trial' | 'login';
  requiredTier?: 'monthly' | 'yearly';
}

export interface SubscriptionFeatures {
  // Subscription tier features
  maxScansPerDay: number;
  maxHistoryItems: number;
  hasAdvancedAI: boolean;
  hasOfflineMode: boolean;
  hasCloudSync: boolean;
  hasExportData: boolean;
  hasCustomCollections: boolean;
  hasAPIAccess: boolean;
  hasEarlyFeatures: boolean;
  hasAds: boolean;
  hasPrioritySupport: boolean;
}

export class PaywallService {
  private static instance: PaywallService;
  private revenueCatService: RevenueCatService;

  constructor() {
    this.revenueCatService = RevenueCatService.getInstance();
  }

  static getInstance(): PaywallService {
    if (!PaywallService.instance) {
      PaywallService.instance = new PaywallService();
    }
    return PaywallService.instance;
  }

  /**
   * Check if user has premium entitlement via RevenueCat
   */
  async checkRevenueCatEntitlement(): Promise<boolean> {
    try {
      if (!this.revenueCatService.isRevenueCatInitialized()) {
        return false;
      }

      const customerInfo = await this.revenueCatService.getCustomerInfo();
      return customerInfo.isActive || customerInfo.isTrialActive;
    } catch (error) {
      console.error('Error checking RevenueCat entitlement:', error);
      return false;
    }
  }

  /**
   * Get feature access based on subscription status
   */
  getFeatureAccess(subscription: any): SubscriptionFeatures {
    const isActive = subscription?.isActive || false;
    const tier = subscription?.tier;
    const isTrialActive = subscription?.isTrialActive || false;

    // No free tier - users must have subscription or trial
    if (!isActive && !isTrialActive) {
      return {
        maxScansPerDay: 0, // No scans without subscription
        maxHistoryItems: 0, // No history without subscription
        hasAdvancedAI: false,
        hasOfflineMode: false,
        hasCloudSync: false,
        hasExportData: false,
        hasCustomCollections: false,
        hasAPIAccess: false,
        hasEarlyFeatures: false,
        hasAds: false,
        hasPrioritySupport: false,
      };
    }

    // Monthly tier features
    if (tier === 'monthly' || isTrialActive) {
      return {
        maxScansPerDay: -1, // Unlimited
        maxHistoryItems: -1, // Unlimited
        hasAdvancedAI: true,
        hasOfflineMode: true,
        hasCloudSync: true,
        hasExportData: true,
        hasCustomCollections: false,
        hasAPIAccess: false,
        hasEarlyFeatures: false,
        hasAds: false,
        hasPrioritySupport: true,
      };
    }

    // Yearly tier features (all features)
    if (tier === 'yearly') {
      return {
        maxScansPerDay: -1, // Unlimited
        maxHistoryItems: -1, // Unlimited
        hasAdvancedAI: true,
        hasOfflineMode: true,
        hasCloudSync: true,
        hasExportData: true,
        hasCustomCollections: true,
        hasAPIAccess: true,
        hasEarlyFeatures: true,
        hasAds: false,
        hasPrioritySupport: true,
      };
    }

    // Default to no access without subscription
    return {
      maxScansPerDay: 0,
      maxHistoryItems: 0,
      hasAdvancedAI: false,
      hasOfflineMode: false,
      hasCloudSync: false,
      hasExportData: false,
      hasCustomCollections: false,
      hasAPIAccess: false,
      hasEarlyFeatures: false,
      hasAds: false,
      hasPrioritySupport: false,
    };
  }

  /**
   * Check if user can perform a scan
   */
  async checkScanAccess(subscription: any, todayScans: number = 0): Promise<FeatureAccess> {
    const features = this.getFeatureAccess(subscription);

    // Unlimited scans for premium users
    if (features.maxScansPerDay === -1) {
      return { hasAccess: true };
    }

    // No free tier - require subscription for scanning
    if (features.maxScansPerDay === 0) {
      return {
        hasAccess: false,
        reason: 'Scanning requires a subscription. Start your free trial or subscribe to begin identifying species!',
        action: 'trial',
        requiredTier: 'yearly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Check if user can access advanced AI features
   */
  checkAdvancedAIAccess(subscription: any): FeatureAccess {
    const features = this.getFeatureAccess(subscription);

    if (!features.hasAdvancedAI) {
      return {
        hasAccess: false,
        reason: 'Advanced AI identification requires a premium subscription for more accurate results.',
        action: 'upgrade',
        requiredTier: 'monthly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Check if user can save to history
   */
  checkHistoryAccess(subscription: any, currentHistoryCount: number = 0): FeatureAccess {
    const features = this.getFeatureAccess(subscription);

    // Unlimited history for premium users
    if (features.maxHistoryItems === -1) {
      return { hasAccess: true };
    }

    // No free tier - require subscription for history
    if (features.maxHistoryItems === 0) {
      return {
        hasAccess: false,
        reason: 'Scan history requires a subscription. Start your free trial or subscribe to save your discoveries!',
        action: 'trial',
        requiredTier: 'yearly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Check if user can export data
   */
  checkExportAccess(subscription: any): FeatureAccess {
    const features = this.getFeatureAccess(subscription);

    if (!features.hasExportData) {
      return {
        hasAccess: false,
        reason: 'Data export is a premium feature. Upgrade to export your scan history and data.',
        action: 'upgrade',
        requiredTier: 'monthly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Check if user can use offline mode
   */
  checkOfflineAccess(subscription: any): FeatureAccess {
    const features = this.getFeatureAccess(subscription);

    if (!features.hasOfflineMode) {
      return {
        hasAccess: false,
        reason: 'Offline mode requires a premium subscription to scan without internet connection.',
        action: 'upgrade',
        requiredTier: 'monthly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Check if user can create custom collections
   */
  checkCustomCollectionsAccess(subscription: any): FeatureAccess {
    const features = this.getFeatureAccess(subscription);

    if (!features.hasCustomCollections) {
      return {
        hasAccess: false,
        reason: 'Custom collections are available with the yearly subscription for better organization.',
        action: 'upgrade',
        requiredTier: 'yearly',
      };
    }

    return { hasAccess: true };
  }

  /**
   * Show paywall modal with appropriate message
   */
  showPaywall(access: FeatureAccess, customTitle?: string): void {
    if (access.hasAccess) return;

    const title = customTitle || 'Subscription Required';
    const buttons = [
      { text: 'Maybe Later', style: 'cancel' as const },
      {
        text: access.action === 'trial' ? 'Start Free Trial' : 'Subscribe Now',
        onPress: () => {
          this.presentPaywall();
        },
      },
    ];

    Alert.alert(title, access.reason || 'This feature requires a subscription.', buttons);
  }

  /**
   * Present RevenueCat paywall or fallback to custom subscription screen
   */
  async presentPaywall(): Promise<void> {
    try {
      if (this.revenueCatService.isRevenueCatInitialized()) {
        // Check if we have offerings available
        const offerings = await this.revenueCatService.loadOfferings();
// After (correct):
  if (offerings) {
  router.push('/paywall');
}
      }

      // Fallback to custom subscription screen
      router.push('/subscription');
    } catch (error) {
      console.error('Error presenting paywall:', error);
      // Fallback to custom subscription screen
      router.push('/subscription');
    }
  }

  /**
   * Check and enforce scan limit with paywall
   */
  async enforceScanLimit(subscription: any, todayScans: number = 0): Promise<boolean> {
    const access = await this.checkScanAccess(subscription, todayScans);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '🔍 Scan Limit Reached');
      return false;
    }

    return true;
  }

  /**
   * Check and enforce history limit with paywall
   */
  enforceHistoryLimit(subscription: any, currentHistoryCount: number = 0): boolean {
    const access = this.checkHistoryAccess(subscription, currentHistoryCount);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '📚 History Limit Reached');
      return false;
    }

    return true;
  }

  /**
   * Check and enforce advanced AI access with paywall
   */
  enforceAdvancedAI(subscription: any): boolean {
    const access = this.checkAdvancedAIAccess(subscription);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '🤖 Advanced AI');
      return false;
    }

    return true;
  }

  /**
   * Check and enforce export access with paywall
   */
  enforceExportAccess(subscription: any): boolean {
    const access = this.checkExportAccess(subscription);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '📤 Export Data');
      return false;
    }

    return true;
  }

  /**
   * Check and enforce offline access with paywall
   */
  enforceOfflineAccess(subscription: any): boolean {
    const access = this.checkOfflineAccess(subscription);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '📱 Offline Mode');
      return false;
    }

    return true;
  }

  /**
   * Check and enforce custom collections access with paywall
   */
  enforceCustomCollectionsAccess(subscription: any): boolean {
    const access = this.checkCustomCollectionsAccess(subscription);
    
    if (!access.hasAccess) {
      this.showPaywall(access, '📁 Custom Collections');
      return false;
    }

    return true;
  }

  /**
   * Get remaining scans for free users
   */
  getRemainingScans(subscription: any, todayScans: number = 0): number {
    const features = this.getFeatureAccess(subscription);
    
    if (features.maxScansPerDay === -1) {
      return -1; // Unlimited
    }

    return Math.max(0, features.maxScansPerDay - todayScans);
  }

  /**
   * Get remaining history slots for free users
   */
  getRemainingHistorySlots(subscription: any, currentHistoryCount: number = 0): number {
    const features = this.getFeatureAccess(subscription);
    
    if (features.maxHistoryItems === -1) {
      return -1; // Unlimited
    }

    return Math.max(0, features.maxHistoryItems - currentHistoryCount);
  }
}

export default PaywallService;
