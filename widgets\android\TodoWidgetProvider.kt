package com.biosapex.app

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import java.text.SimpleDateFormat
import java.util.*

/**
 * Todo Widget Provider for Android
 * Displays pending tasks and reminders with notification support
 */
class TodoWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }

    companion object {
        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Get todo data
            val todoData = getTodoData(context)
            val pendingTasks = todoData.pendingTasks
            val nextTask = todoData.nextTask
            val nextTaskDue = todoData.nextTaskDue
            val completedToday = todoData.completedToday
            val totalTasks = todoData.totalTasks

            // Construct the RemoteViews object
            val views = RemoteViews(context.packageName, R.layout.todo_widget)
            
            // Update task counts
            views.setTextViewText(R.id.widget_pending_count, pendingTasks.toString())
            views.setTextViewText(R.id.widget_completed_today, "$completedToday/$totalTasks completed today")
            
            // Update next task
            if (nextTask.isNotEmpty()) {
                views.setTextViewText(R.id.widget_next_task, nextTask)
                views.setTextViewText(R.id.widget_task_due, formatDueTime(nextTaskDue))
            } else {
                views.setTextViewText(R.id.widget_next_task, "No pending tasks")
                views.setTextViewText(R.id.widget_task_due, "Great job!")
            }

            // Create intent to open todo screen when widget is tapped
            val todoIntent = Intent(context, MainActivity::class.java)
            todoIntent.putExtra("action", "open_todo")
            todoIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val todoPendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId,
                todoIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, todoPendingIntent)

            // Create intent for add task button
            val addTaskIntent = Intent(context, MainActivity::class.java)
            addTaskIntent.putExtra("action", "add_task")
            addTaskIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val addTaskPendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId + 1000,
                addTaskIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_add_task_button, addTaskPendingIntent)

            // Update widget appearance based on pending tasks
            if (pendingTasks == 0) {
                views.setInt(R.id.widget_pending_count, "setTextColor", 
                    context.getColor(R.color.widget_success))
            } else if (pendingTasks > 5) {
                views.setInt(R.id.widget_pending_count, "setTextColor", 
                    context.getColor(R.color.widget_warning))
            } else {
                views.setInt(R.id.widget_pending_count, "setTextColor", 
                    context.getColor(R.color.widget_accent))
            }

            // Instruct the widget manager to update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }

        private data class TodoData(
            val pendingTasks: Int,
            val nextTask: String,
            val nextTaskDue: Date,
            val completedToday: Int,
            val totalTasks: Int
        )

        private fun getTodoData(context: Context): TodoData {
            // In real implementation, read from database or SharedPreferences
            val prefs = context.getSharedPreferences("biosapex_todos", Context.MODE_PRIVATE)
            
            // Mock data for demonstration
            val tasks = arrayOf(
                "Scan 5 different plant species",
                "Complete daily water intake goal",
                "Review scan history from this week",
                "Take nature photos",
                "Study botanical terms"
            )
            
            return TodoData(
                pendingTasks = prefs.getInt("pending_tasks", (1..5).random()),
                nextTask = tasks.random(),
                nextTaskDue = Date(System.currentTimeMillis() + (1..6).random() * 3600000L),
                completedToday = prefs.getInt("completed_today", (0..3).random()),
                totalTasks = prefs.getInt("total_tasks", (3..8).random())
            )
        }

        private fun formatDueTime(dueDate: Date): String {
            val now = Date()
            val diffInMillis = dueDate.time - now.time
            
            return when {
                diffInMillis < 0 -> "Overdue"
                diffInMillis < 3600000 -> "${diffInMillis / 60000}m"
                diffInMillis < 86400000 -> "${diffInMillis / 3600000}h"
                else -> SimpleDateFormat("MMM dd", Locale.getDefault()).format(dueDate)
            }
        }
    }
}
