import { Platform } from 'react-native';

/**
 * RevenueCat Configuration
 * 
 * Replace these with your actual RevenueCat API keys from your dashboard:
 * https://app.revenuecat.com/
 */
export const REVENUECAT_CONFIG = {
  // API Keys - Replace with your actual keys
  apiKeys: {
    ios: 'appl_YOUR_IOS_API_KEY_HERE', // Replace with your Apple App Store API key
    android: 'goog_YOUR_ANDROID_API_KEY_HERE', // Replace with your Google Play API key
    amazon: 'amzn_YOUR_AMAZON_API_KEY_HERE', // Replace with your Amazon API key (if using Amazon)
  },
  
  // Entitlement identifiers - these should match your RevenueCat dashboard
  entitlements: {
    premium: 'premium', // Main premium entitlement identifier
  },
  
  // Product identifiers - these should match your App Store/Google Play products
  products: {
    monthly: 'biosapex_monthly_19_99', // Monthly subscription product ID
    yearly: 'biosapex_yearly_29_99', // Yearly subscription product ID
  },
  
  // Offering identifiers - these should match your RevenueCat dashboard
  offerings: {
    default: 'default', // Default offering identifier
  },
  
  // User attributes for analytics and targeting
  userAttributes: {
    // Add any custom user attributes you want to track
  },
  
  // Configuration options
  options: {
    // Enable debug logging in development
    enableDebugLogging: __DEV__,
    
    // User ID configuration
    // Set to true if you want to use your own user IDs
    // Set to false to let RevenueCat generate anonymous IDs
    useCustomUserIds: false,
    
    // Amazon configuration (only needed if supporting Amazon)
    useAmazon: false,
  },
};

/**
 * Get the appropriate API key for the current platform
 */
export const getRevenueCatApiKey = (): string => {
  if (Platform.OS === 'ios') {
    return REVENUECAT_CONFIG.apiKeys.ios;
  } else if (Platform.OS === 'android') {
    if (REVENUECAT_CONFIG.options.useAmazon) {
      return REVENUECAT_CONFIG.apiKeys.amazon;
    }
    return REVENUECAT_CONFIG.apiKeys.android;
  }
  
  throw new Error(`Unsupported platform: ${Platform.OS}`);
};

/**
 * Validate that API keys are properly configured
 */
export const validateRevenueCatConfig = (): boolean => {
  const apiKey = getRevenueCatApiKey();
  
  // Check if API key is still the placeholder
  if (apiKey.includes('YOUR_') && apiKey.includes('_API_KEY_HERE')) {
    console.error('❌ RevenueCat API key not configured! Please update RevenueCatConfig.ts with your actual API keys.');
    return false;
  }
  
  return true;
};

/**
 * RevenueCat initialization configuration
 */
export const getRevenueCatInitConfig = () => {
  const apiKey = getRevenueCatApiKey();
  
  const config: any = {
    apiKey,
  };
  
  // Add Amazon configuration if needed
  if (Platform.OS === 'android' && REVENUECAT_CONFIG.options.useAmazon) {
    config.useAmazon = true;
  }
  
  return config;
};

export default REVENUECAT_CONFIG;
