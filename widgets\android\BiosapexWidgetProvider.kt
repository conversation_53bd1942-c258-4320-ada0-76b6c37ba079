package com.biosapex.app

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import java.text.SimpleDateFormat
import java.util.*

/**
 * Biosapex Widget Provider for Android
 * Displays recent scan information and quick access to the app
 */
class BiosapexWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }

    companion object {
        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Get widget data
            val recentScan = getRecentScan(context)
            val scanCount = getScanCount(context)
            val lastUpdated = getCurrentTime()

            // Construct the RemoteViews object
            val views = RemoteViews(context.packageName, R.layout.biosapex_widget)
            
            // Update widget content
            views.setTextViewText(R.id.widget_recent_scan, recentScan)
            views.setTextViewText(R.id.widget_scan_count, scanCount.toString())
            views.setTextViewText(R.id.widget_last_updated, "Updated: $lastUpdated")

            // Create intent to open the app when widget is tapped
            val intent = Intent(context, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val pendingIntent = PendingIntent.getActivity(
                context, 
                0, 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)

            // Create intent for quick scan action
            val scanIntent = Intent(context, MainActivity::class.java)
            scanIntent.putExtra("action", "quick_scan")
            scanIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            val scanPendingIntent = PendingIntent.getActivity(
                context,
                1,
                scanIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_scan_button, scanPendingIntent)

            // Instruct the widget manager to update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }

        private fun getRecentScan(context: Context): String {
            // In a real implementation, this would read from SharedPreferences or database
            val samples = arrayOf("Oak Tree", "Robin", "Monarch Butterfly", "Rose", "Pine Cone")
            return samples.random()
        }

        private fun getScanCount(context: Context): Int {
            // In a real implementation, this would read from SharedPreferences or database
            return (1..99).random()
        }

        private fun getCurrentTime(): String {
            val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
            return formatter.format(Date())
        }
    }
}
