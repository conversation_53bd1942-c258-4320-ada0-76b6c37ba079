{"name": "biospex", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "EXPO_NO_TELEMETRY=1 expo start", "dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@bittingz/expo-widgets": "^3.0.2", "@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/slider": "4.5.6", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@sentry/react-native": "^6.19.0", "dayjs": "^1.11.13", "expo": "^53.0.20", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.3", "expo-build-properties": "~0.14.8", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.3", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.2.0", "fs-extra": "^11.3.0", "lucide-react-native": "^0.539.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-adapty": "^3.8.2", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.63", "react-native-purchases": "^9.1.0", "react-native-purchases-ui": "^9.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}