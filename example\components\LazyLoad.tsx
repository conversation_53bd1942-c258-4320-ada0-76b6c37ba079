import React, { Suspense, lazy, ComponentType } from 'react';
import { View, ActivityIndicator, Image, ImageProps, StyleSheet } from 'react-native';
import { useTheme } from './ThemeContext';

interface LazyComponentProps {
  importFunc: () => Promise<{ default: ComponentType<any> }>;
  fallback?: React.ReactNode;
  [key: string]: any;
}

export const LazyComponent: React.FC<LazyComponentProps> = ({
  importFunc,
  fallback,
  ...props
}) => {
  const { colors } = useTheme();
  const LazyComp = lazy(importFunc);

  return (
    <Suspense fallback={fallback || <ActivityIndicator size="small" color={colors.primary} />}>
      <LazyComp {...props} />
    </Suspense>
  );
};

interface OptimizedImageProps extends ImageProps {
  thumbnailSource?: string;
  loadingIndicator?: React.ReactNode;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  thumbnailSource,
  style,
  loadingIndicator,
  ...props
}) => {
  const { colors } = useTheme();
  const [loaded, setLoaded] = React.useState(false);
  const [error, setError] = React.useState(false);

  if (error) {
    return null;
  }

  return (
    <View style={style}>
      {thumbnailSource && !loaded && (
        <Image
          source={{ uri: thumbnailSource }}
          style={[StyleSheet.absoluteFill, style]}
          blurRadius={10}
        />
      )}
      <Image
        source={source}
        style={style}
        onLoad={() => setLoaded(true)}
        onError={() => setError(true)}
        {...props}
      />
      {!loaded && (
        <View style={[StyleSheet.absoluteFill, styles.loadingOverlay]}>
          {loadingIndicator || <ActivityIndicator size="small" color={colors.primary} />}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
});