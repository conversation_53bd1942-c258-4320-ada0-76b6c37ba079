import Purchases from 'react-native-purchases';
import { Platform } from 'react-native';

class SubscriptionManager {
  async initialize() {
    try {
      const appleApiKey = process.env.EXPO_PUBLIC_REVENUECAT_APPLE_API_KEY;
      const googleApiKey = process.env.EXPO_PUBLIC_REVENUECAT_GOOGLE_API_KEY;

      if (!appleApiKey || !googleApiKey) {
        throw new Error('RevenueCat API keys are not defined');
      }

      Purchases.setLogLevel(Purchases.LOG_LEVEL.VERBOSE);

      if (Platform.OS === 'ios') {
        Purchases.configure({ apiKey: appleApiKey });
      } else if (Platform.OS === 'android') {
        Purchases.configure({ apiKey: googleApiKey });
      }

      console.log('RevenueCat SDK initialized successfully');
    } catch (error) {
      console.error('RevenueCat initialization error:', error);
    }
  }

  async getOfferings() {
    try {
      const offerings = await Purchases.getOfferings();
      return offerings;
    } catch (error) {
      console.error('Error getting offerings:', error);
      return null;
    }
  }

  async makePurchase(packageToPurchase: any) {
    try {
      const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
      return customerInfo;
    } catch (error) {
      console.error('Error making purchase:', error);
      return null;
    }
  }

  async restorePurchases() {
    try {
      const customerInfo = await Purchases.restorePurchases();
      return customerInfo;
    } catch (error) {
      console.error('Error restoring purchases:', error);
      return null;
    }
  }

  async getCustomerInfo() {
    try {
      const customerInfo = await Purchases.getCustomerInfo();
      return customerInfo;
    } catch (error) {
      console.error('Error getting customer info:', error);
      return null;
    }
  }

  async logIn(userId: string) {
    try {
      const result = await Purchases.logIn(userId);
      return result;
    } catch (error) {
      console.error('Error logging in:', error);
      return null;
    }
  }

  async logOut() {
    try {
      await Purchases.logOut();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  }
}

export default new SubscriptionManager();