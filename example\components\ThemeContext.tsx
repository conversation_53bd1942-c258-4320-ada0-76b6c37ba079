import React, { createContext, useContext, ReactNode } from 'react';
import { ColorValue } from 'react-native';
import { useSettings } from './SettingsContext';

export interface ThemeColors {
  [x: string]: ColorValue | undefined;
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  backgroundSecondary: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  error: string;
  tabActive: string;
  tabInactive: string;
  card: string;
  cardSecondary: string;
  inputBackground: string;
  inputBorder: string;
  inputText: string;
  buttonPrimary: string;
  buttonSecondary: string;
  buttonText: string;
  headerBackground: string;
  modalBackground: string;
  overlay: string;
  shadow: string;
  blur: string;
}

interface ThemeContextType {
  colors: ThemeColors;
  isDark: boolean;
}

const lightColors: ThemeColors = {
  primary: '#6366F1',
  secondary: '#8B5CF6',
  accent: '#F59E0B',
  background: '#F8FAFC',
  backgroundSecondary: '#FFFFFF',
  text: '#1F2937',
  textSecondary: '#6B7280',
  border: '#E5E7EB',
  success: '#10B981',
  error: '#EF4444',
  tabActive: '#6366F1',
  tabInactive: '#9CA3AF',
  card: '#FFFFFF',
  cardSecondary: '#F9FAFB',
  inputBackground: '#FFFFFF',
  inputBorder: '#D1D5DB',
  inputText: '#1F2937',
  buttonPrimary: '#6366F1',
  buttonSecondary: '#F3F4F6',
  buttonText: '#FFFFFF',
  headerBackground: '#FFFFFF',
  modalBackground: '#FFFFFF',
  overlay: 'rgba(0, 0, 0, 0.5)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  blur: 'rgba(255, 255, 255, 0.8)',
};

const darkColors: ThemeColors = {
  primary: '#8B5CF6',
  secondary: '#A855F7',
  accent: '#F59E0B',
  background: '#111827',
  backgroundSecondary: '#1F2937',
  text: '#F9FAFB',
  textSecondary: '#D1D5DB',
  border: '#374151',
  success: '#10B981',
  error: '#EF4444',
  tabActive: '#8B5CF6',
  tabInactive: '#9CA3AF',
  card: '#1F2937',
  cardSecondary: '#374151',
  inputBackground: '#1F2937',
  inputBorder: '#4B5563',
  inputText: '#F9FAFB',
  buttonPrimary: '#8B5CF6',
  buttonSecondary: '#374151',
  buttonText: '#FFFFFF',
  headerBackground: '#1F2937',
  modalBackground: '#1F2937',
  overlay: 'rgba(0, 0, 0, 0.7)',
  shadow: 'rgba(0, 0, 0, 0.3)',
  blur: 'rgba(0, 0, 0, 0.8)',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { settings, getEffectiveTheme } = useSettings();

  // Determine the effective theme directly from the settings context
  const effectiveTheme = getEffectiveTheme();
  const isDark = effectiveTheme === 'dark';

  const colors = isDark ? darkColors : lightColors;

  return (
    <ThemeContext.Provider value={{ colors, isDark }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
