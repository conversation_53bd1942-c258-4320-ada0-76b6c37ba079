# RevenueCat Setup Instructions for Biosapex

This guide will help you configure RevenueCat for your Biosapex app to enable real in-app purchases and subscriptions.

## 🚀 Quick Start

Your app is already configured with RevenueCat integration! You just need to:

1. **Get your RevenueCat API keys**
2. **Update the configuration file**
3. **Set up your products in RevenueCat dashboard**
4. **Test your implementation**

## 📋 Prerequisites

- ✅ RevenueCat packages already installed (`react-native-purchases` & `react-native-purchases-ui`)
- ✅ RevenueCat service and paywall components already implemented
- ✅ Subscription context integrated with RevenueCat
- ✅ Paywall screens and navigation set up

## 🔧 Configuration Steps

### Step 1: Create RevenueCat Account & Project

1. Go to [RevenueCat Dashboard](https://app.revenuecat.com/)
2. Create an account or sign in
3. Create a new project for your app
4. Note down your project ID

### Step 2: Get API Keys

1. In RevenueCat dashboard, go to **Project Settings** → **API Keys**
2. Copy your API keys:
   - **Apple App Store API Key** (starts with `appl_`)
   - **Google Play API Key** (starts with `goog_`)
   - **Amazon API Key** (starts with `amzn_`) - if supporting Amazon

### Step 3: Update Configuration

Edit `components/RevenueCatConfig.ts` and replace the placeholder API keys:

```typescript
export const REVENUECAT_CONFIG = {
  apiKeys: {
    ios: 'appl_YOUR_ACTUAL_IOS_API_KEY_HERE',
    android: 'goog_YOUR_ACTUAL_ANDROID_API_KEY_HERE',
    amazon: 'amzn_YOUR_ACTUAL_AMAZON_API_KEY_HERE',
  },
  // ... rest of config
};
```

### Step 4: Configure Products in RevenueCat

1. **Connect to App Stores:**
   - Go to **Project Settings** → **Integrations**
   - Connect your Apple App Store Connect account
   - Connect your Google Play Console account

2. **Add Products:**
   - Go to **Products** section
   - Add your subscription products:
     - Monthly: `biosapex_monthly_19_99` ($19.99/month)
     - Yearly: `biosapex_yearly_29_99` ($29.99/year)

3. **Create Entitlements:**
   - Go to **Entitlements** section
   - Create entitlement: `premium`
   - Attach your products to this entitlement

4. **Create Offerings:**
   - Go to **Offerings** section
   - Create offering: `default`
   - Add your products to this offering

5. **Configure Paywall (Optional):**
   - Go to **Paywalls** section
   - Create a paywall template
   - Assign it to your offering

### Step 5: Update Product Identifiers (if needed)

If your actual product IDs differ from the defaults, update them in `components/RevenueCatConfig.ts`:

```typescript
products: {
  monthly: 'your_actual_monthly_product_id',
  yearly: 'your_actual_yearly_product_id',
},
```

## 🧪 Testing

### Development Testing

1. **iOS Simulator:** Use sandbox accounts
2. **Android Emulator:** Use test accounts
3. **Physical Devices:** Use sandbox/test accounts

### Test Flow

1. Open the app
2. Try to use a premium feature (scanning, history, etc.)
3. Paywall should appear
4. Test purchase flow
5. Test restore purchases
6. Verify subscription status

### Debug Logs

The app includes debug logging. Check console for:
- ✅ RevenueCat initialized successfully
- ⚠️ RevenueCat not properly configured
- ❌ Various error messages

## 🔄 Fallback Behavior

The app is designed to gracefully handle RevenueCat unavailability:

- **If RevenueCat is not configured:** Falls back to mock subscription system
- **If RevenueCat fails to load:** Shows custom subscription screen
- **If network is unavailable:** Uses cached subscription state

## 📱 App Store Configuration

### iOS (App Store Connect)

1. Create your app in App Store Connect
2. Set up In-App Purchases:
   - Create subscription groups
   - Add monthly and yearly subscriptions
   - Set pricing and availability
3. Enable In-App Purchase capability in Xcode

### Android (Google Play Console)

1. Create your app in Google Play Console
2. Set up In-App Products:
   - Create subscription products
   - Set pricing and availability
3. Add BILLING permission in AndroidManifest.xml (already added)

## 🚨 Important Notes

### Security
- Never expose API keys in client code (they're in config file for development)
- Use environment variables in production
- Implement server-side receipt validation for production

### Testing
- Always test on physical devices before release
- Test subscription lifecycle (purchase, renewal, cancellation)
- Test restore purchases functionality
- Test offline behavior

### Production Checklist
- [ ] API keys configured correctly
- [ ] Products created in app stores
- [ ] RevenueCat dashboard configured
- [ ] Entitlements and offerings set up
- [ ] Paywall tested on both platforms
- [ ] Restore purchases working
- [ ] Server-side validation implemented (recommended)

## 🆘 Troubleshooting

### Common Issues

1. **"RevenueCat not properly configured"**
   - Check API keys in `RevenueCatConfig.ts`
   - Ensure keys don't contain placeholder text

2. **"No offerings found"**
   - Check RevenueCat dashboard offerings
   - Ensure products are attached to offerings
   - Verify app store connections

3. **Purchase fails**
   - Check product IDs match between app and stores
   - Verify sandbox/test accounts
   - Check app store configurations

4. **Restore doesn't work**
   - Ensure user is signed in to same App Store/Google account
   - Check if purchases were made in sandbox vs production

### Getting Help

- RevenueCat Documentation: https://docs.revenuecat.com/
- RevenueCat Community: https://community.revenuecat.com/
- App logs and console output for debugging

## 🎉 You're Ready!

Once configured, your app will have:
- ✅ Real in-app purchases
- ✅ Subscription management
- ✅ Cross-platform support
- ✅ Automatic receipt validation
- ✅ Analytics and insights
- ✅ Remote paywall configuration

Happy monetizing! 🚀
