/* HistoryScreen.tsx - Complete Implementation */
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Image,
  FlatList,
  TextInput,
  Modal,
  Alert,
  RefreshControl,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  Search,
  Filter,
  Calendar,
  Clock,
  MapPin,
  Trash2,
  Share2,
  Edit3,
  MoreVertical,
  ArrowUpDown,
  Grid3x3,
  List,
  Star,
  Heart,
  Flame,
  Beef,
  Wheat,
  Droplet,
  X,
  CheckCircle,
  AlertCircle,
  Package,
  Utensils,
  Leaf,
  Mountain,
  CircleDollarSign,
  GlassWater,
  Cpu,
  Shirt,
  Book,
  Pill,
  Hammer,
  Palette,
  Info,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  Download,
  Upload,
  Settings,
  Eye,
  EyeOff,
} from 'lucide-react-native';
import { useTheme } from '@/components/ThemeContext';
import { OptimizedImage } from '@/components/LazyLoad';
import { useTextScaling } from '@/components/TextScalingContext';
import HapticService from '@/components/HapticService';
import NotificationService from '@/components/NotificationService';
import { Category, IdentificationResult as GeminiIdentificationResult, NutritionalInfo, FoodAnalysis } from '@/components/GeminiService'; // Import necessary types
const { width } = Dimensions.get('window');

// Define the PhysicalTraits type for consistency
export interface PhysicalTraits {
  size?: string;
  weight?: string;
  lifespan?: string;
  diet?: string;
  // Add any other physical trait properties you might use
}

// Enhanced ScanHistoryItem type
export interface ScanHistoryItemType extends Omit<GeminiIdentificationResult, 'physicalTraits' | 'facts' | 'alternatives' | 'distinguishingFeatures' | 'usage' | 'maintenance' | 'safety' | 'relatedItems' | 'threats'> {
  // Override physicalTraits to ensure it's an object, if GeminiIdentificationResult defines it differently
  // Or, if GeminiIdentificationResult already defines it as an object, simply extend it.
  // Given the error, it seems the original ScanHistoryItemType might have had it as string[].
  // So, we explicitly redefine it here to ensure it's an object.
  physicalTraits?: PhysicalTraits; // Now defined as an object
  facts?: string[]; // Ensure facts is an array of strings
  alternatives?: Array<{ name: string; scientificName: string; confidence: number }>; // Ensure alternatives is an array of objects
  distinguishingFeatures?: string[]; // Ensure distinguishingFeatures is an array of strings
  usage?: string[];
  maintenance?: string[];
  safety?: string[];
  relatedItems?: string[];
  threats?: string[];

  // Additional fields specific to history items
  id: string; // Unique ID for history item
  date: string; // ISO string date
  location: string; // Location of scan
  imageUri: string; // URI of the scanned image
  isFavorite?: boolean; // Whether the item is favorited
  tags?: string[]; // Tags for filtering/categorization
  isPrivate?: boolean; // Whether the item is private
  // Include optional nutritionalInfo and foodAnalysis if they exist in GeminiIdentificationResult
  nutritionalInfo?: NutritionalInfo;
  foodAnalysis?: FoodAnalysis;
}

// Mock data for demonstration - replace with your actual storage solution
let scanHistory: ScanHistoryItemType[] = [
  {
    id: '1',
    name: 'Grilled Chicken Salad',
    category: 'Food',
    confidence: 95,
    description: 'A healthy grilled chicken salad with mixed greens, tomatoes, and light vinaigrette.',
    scientificName: 'N/A',
    facts: ['High protein content', 'Low in carbohydrates', 'Rich in vitamins A and C'],
    date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    location: 'Home Kitchen',
    imageUri: 'https://example.com/chicken-salad.jpg',
    isFavorite: true,
    tags: ['healthy', 'protein', 'lunch'],
    nutritionalInfo: {
      totalCalories: 350,
      caloriesPerServing: 350,
      protein: 35,
      carbs: 12,
      fat: 18,
      fiber: 5,
      sugar: 8,
      sodium: 420,
      cholesterol: 85,
      saturatedFat: 5,
      transFat: 0,
      monounsaturatedFat: 8,
      polyunsaturatedFat: 3,
      healthMetrics: {
        healthScore: 8.5,
        processedFoodScore: 2,
        sugarLevel: 'Low',
        sodiumLevel: 'Moderate',
        fiberLevel: 'Good',
        nutrientDensity: 7, // Added missing property
        processingLevel: 'Minimal', // Added missing property
      },
      allergens: [],
      dietaryRestrictions: [], // Added missing property
      ingredients: [],
      servingInfo: { // Added missing property
        servingSize: '1 plate',
        totalWeight: '300g',
        estimatedPortions: 1
      }
    },
    physicalTraits: { // Example of physicalTraits as an object
      size: 'medium',
      weight: '300g'
    }
  },
  {
    id: '2',
    name: 'Red Rose',
    category: 'Plant',
    confidence: 88,
    description: 'A beautiful red rose flower, commonly used in gardens and floral arrangements.',
    scientificName: 'Rosa rubiginosa',
    facts: ['Symbol of love and passion', 'Rich in vitamin C', 'Used in perfumes and cosmetics'],
    date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    location: 'Central Park',
    imageUri: 'https://example.com/red-rose.jpg',
    isFavorite: false,
    tags: ['flower', 'garden', 'nature'],
    physicalTraits: { // Example of physicalTraits as an object
      size: 'small',
      lifespan: '7 days (cut)'
    }
  },
  {
    id: '3',
    name: 'Chocolate Chip Cookie',
    category: 'Food',
    confidence: 92,
    description: 'A sweet baked cookie with chocolate chips, perfect as a dessert or snack.',
    scientificName: 'N/A',
    facts: ['High in calories', 'Contains dairy and gluten', 'Popular dessert worldwide'],
    date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    location: 'Local Bakery',
    imageUri: 'https://example.com/cookie.jpg',
    isFavorite: true,
    tags: ['dessert', 'sweet', 'baked'],
    nutritionalInfo: {
      totalCalories: 280,
      caloriesPerServing: 280,
      protein: 4,
      carbs: 38,
      fat: 12,
      fiber: 2,
      sugar: 18,
      sodium: 180,
      cholesterol: 25,
      saturatedFat: 7,
      transFat: 0.5,
      monounsaturatedFat: 3,
      polyunsaturatedFat: 1.5,
      healthMetrics: {
        healthScore: 3.5,
        processedFoodScore: 8,
        sugarLevel: 'High',
        sodiumLevel: 'Low',
        fiberLevel: 'Low',
        nutrientDensity: 2, // Added missing property
        processingLevel: 'Highly Processed', // Added missing property
      },
      allergens: ['Dairy', 'Gluten'],
      dietaryRestrictions: [], // Added missing property
      ingredients: [],
      servingInfo: { // Added missing property
        servingSize: '1 cookie',
        totalWeight: '50g',
        estimatedPortions: 1
      }
    },
    physicalTraits: { // Example of physicalTraits as an object
      size: 'medium',
      weight: '50g'
    }
  }
];

// Add item to history function
export const addScanToHistory = (item: ScanHistoryItemType) => {
  scanHistory.unshift(item);
};

// Helper to get icon for category
const getCategoryIcon = (category: Category) => {
  switch (category) {
    case 'Animal': return Beef;
    case 'Plant': return Leaf;
    case 'Food': return Utensils;
    case 'Insect': return Leaf;
    case 'Rock / Mineral': return Mountain;
    case 'Coin / Currency': return CircleDollarSign;
    case 'Wine': return GlassWater;
    case 'Product': return Package;
    case 'Electronics': return Cpu;
    case 'Clothing': return Shirt;
    case 'Book': return Book;
    case 'Medicine': return Pill;
    case 'Tool': return Hammer;
    case 'Art': return Palette;
    default: return Info;
  }
};

// Helper to format relative time
const getRelativeTime = (dateString: string) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) return `${diffInWeeks}w ago`;
  
  const diffInMonths = Math.floor(diffInDays / 30);
  return `${diffInMonths}mo ago`;
};

// Helper to get health score color
const getHealthScoreColor = (score: number, colors: any) => {
  if (score >= 8) return colors.success;
  if (score >= 6) return '#F59E0B';
  if (score >= 4) return '#F97316';
  return colors.error;
};

type ViewMode = 'grid' | 'list';
type SortBy = 'date' | 'name' | 'category' | 'health';
type FilterBy = 'all' | 'food' | 'plants' | 'favorites' | 'recent';

export default function HistoryScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { getScaledFontSize } = useTextScaling();
  const hapticService = HapticService.getInstance();
  const notificationService = NotificationService.getInstance();

  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [filterBy, setFilterBy] = useState<FilterBy>('all');
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showPrivateItems, setShowPrivateItems] = useState(true);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-50)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Filter and sort history
  const getFilteredAndSortedHistory = () => {
    let filtered = [...scanHistory];

    // Apply privacy filter
    if (!showPrivateItems) {
      filtered = filtered.filter(item => !item.isPrivate);
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'food':
        filtered = filtered.filter(item => item.category === 'Food');
        break;
      case 'plants':
        filtered = filtered.filter(item => item.category === 'Plant');
        break;
      case 'favorites':
        filtered = filtered.filter(item => item.isFavorite);
        break;
      case 'recent':
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        filtered = filtered.filter(item => new Date(item.date) > oneDayAgo);
        break;
    }

    // Apply sorting
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'category':
        filtered.sort((a, b) => a.category.localeCompare(b.category));
        break;
      case 'health':
        filtered.sort((a, b) => {
          const scoreA = a.nutritionalInfo?.healthMetrics?.healthScore || 0;
          const scoreB = b.nutritionalInfo?.healthMetrics?.healthScore || 0;
          return scoreB - scoreA;
        });
        break;
      case 'date':
      default:
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        break;
    }

    return filtered;
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    hapticService.triggerSelectionFeedback();
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // Handle favorite toggle
  const toggleFavorite = (itemId: string) => {
    hapticService.triggerSelectionFeedback();
    const itemIndex = scanHistory.findIndex(item => item.id === itemId);
    if (itemIndex !== -1) {
      scanHistory[itemIndex].isFavorite = !scanHistory[itemIndex].isFavorite;
      // Force re-render
      setSearchQuery(prev => prev);
    }
  };

  // Handle delete items
  const deleteSelectedItems = () => {
    Alert.alert(
      'Delete Items',
      `Are you sure you want to delete ${selectedItems.length} item(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            hapticService.triggerNotificationFeedback('warning');
            scanHistory = scanHistory.filter(item => !selectedItems.includes(item.id));
            setSelectedItems([]);
            setIsSelectionMode(false);
            notificationService.sendNotification({
              title: 'Items Deleted',
              body: `${selectedItems.length} scan(s) removed from history`,
            });
          }
        }
      ]
    );
  };

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
      hapticService.triggerSelectionFeedback();
    }, 1000);
  };

  // Navigate to scan result
  const navigateToScanResult = (item: ScanHistoryItemType) => {
    if (isSelectionMode) {
      toggleItemSelection(item.id);
      return;
    }

    hapticService.triggerSelectionFeedback();
    router.push({
      pathname: '/scan-result',
      params: {
        result: JSON.stringify(item),
        imageData: item.imageUri
      }
    });
  };

  // Render grid item
  const renderGridItem = ({ item }: { item: ScanHistoryItemType }) => {
    const isSelected = selectedItems.includes(item.id);
    const CategoryIcon = getCategoryIcon(item.category);
    
    return (
      <TouchableOpacity
        style={[
          styles.gridItem,
          { backgroundColor: colors.card, borderColor: colors.border },
          isSelected && { borderColor: colors.primary, borderWidth: 2 }
        ]}
        onPress={() => navigateToScanResult(item)}
        onLongPress={() => {
          if (!isSelectionMode) {
            setIsSelectionMode(true);
            hapticService.triggerSelectionFeedback();
          }
          toggleItemSelection(item.id);
        }}
      >
        {/* Image */}
        <View style={styles.gridImageContainer}>
          <OptimizedImage
            source={{ uri: item.imageUri }}
            style={styles.gridImage}
            resizeMode="cover"
          />
          {item.isFavorite && (
            <View style={[styles.favoriteIcon, { backgroundColor: colors.error }]}>
              <Heart size={12} color="white" fill="white" />
            </View>
          )}
          {item.isPrivate && (
            <View style={[styles.privateIcon, { backgroundColor: colors.textSecondary }]}>
              <EyeOff size={12} color="white" />
            </View>
          )}
        </View>

        {/* Content */}
        <View style={styles.gridContent}>
          <View style={styles.gridHeader}>
            <CategoryIcon size={16} color={colors.primary} />
            <Text style={[styles.gridCategory, { color: colors.textSecondary, fontSize: getScaledFontSize(11) }]}>
              {item.category}
            </Text>
          </View>
          <Text style={[styles.gridTitle, { color: colors.text, fontSize: getScaledFontSize(14) }]} numberOfLines={2}>
            {item.name}
          </Text>
          
          {/* Food-specific info */}
          {item.category === 'Food' && item.nutritionalInfo && (
            <View style={styles.gridNutrition}>
              <Text style={[styles.gridCalories, { color: colors.accent, fontSize: getScaledFontSize(12) }]}>
                {item.nutritionalInfo.totalCalories} cal
              </Text>
              {item.nutritionalInfo.healthMetrics && (
                <View style={styles.gridHealthScore}>
                  <Heart size={10} color={getHealthScoreColor(item.nutritionalInfo.healthMetrics.healthScore, colors)} />
                  <Text style={[
                    styles.gridHealthText,
                    { 
                      color: getHealthScoreColor(item.nutritionalInfo.healthMetrics.healthScore, colors),
                      fontSize: getScaledFontSize(10)
                    }
                  ]}>
                    {item.nutritionalInfo.healthMetrics.healthScore}
                  </Text>
                </View>
              )}
            </View>
          )}
          
          <Text style={[styles.gridTime, { color: colors.textSecondary, fontSize: getScaledFontSize(10) }]}>
            {getRelativeTime(item.date)}
          </Text>
        </View>

        {isSelectionMode && (
          <View style={[styles.selectionCheckbox, { backgroundColor: isSelected ? colors.primary : colors.border }]}>
            {isSelected && <CheckCircle size={16} color="white" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Render list item
  const renderListItem = ({ item }: { item: ScanHistoryItemType }) => {
    const isSelected = selectedItems.includes(item.id);
    const CategoryIcon = getCategoryIcon(item.category);
    
    return (
      <TouchableOpacity
        style={[
          styles.listItem,
          { backgroundColor: colors.card, borderColor: colors.border },
          isSelected && { borderColor: colors.primary, borderWidth: 2 }
        ]}
        onPress={() => navigateToScanResult(item)}
        onLongPress={() => {
          if (!isSelectionMode) {
            setIsSelectionMode(true);
            hapticService.triggerSelectionFeedback();
          }
          toggleItemSelection(item.id);
        }}
      >
        <View style={styles.listContent}>
          {/* Image */}
          <View style={styles.listImageContainer}>
            <OptimizedImage
              source={{ uri: item.imageUri }}
              style={styles.listImage}
              resizeMode="cover"
            />
            {item.isFavorite && (
              <View style={[styles.favoriteIcon, { backgroundColor: colors.error }]}>
                <Heart size={10} color="white" fill="white" />
              </View>
            )}
          </View>

          {/* Main content */}
          <View style={styles.listMainContent}>
            <View style={styles.listHeader}>
              <View style={styles.listTitleRow}>
                <CategoryIcon size={16} color={colors.primary} />
                <Text style={[styles.listTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
                  {item.name}
                </Text>
                {item.isPrivate && <EyeOff size={14} color={colors.textSecondary} />}
              </View>
              <TouchableOpacity onPress={() => toggleFavorite(item.id)}>
                <Heart
                  size={20}
                  color={item.isFavorite ? colors.error : colors.textSecondary}
                  fill={item.isFavorite ? colors.error : 'none'}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.listMeta}>
              <Text style={[styles.listCategory, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
                {item.category}
              </Text>
              <View style={styles.listMetaDivider}>
                <Text style={[styles.listTime, { color: colors.textSecondary, fontSize: getScaledFontSize(13) }]}>
                  {getRelativeTime(item.date)}
                </Text>
              </View>
            </View>

            {/* Food nutrition info */}
            {item.category === 'Food' && item.nutritionalInfo && (
              <View style={styles.listNutrition}>
                <View style={styles.listNutritionItem}>
                  <Flame size={14} color={colors.accent} />
                  <Text style={[styles.listNutritionText, { color: colors.text, fontSize: getScaledFontSize(13) }]}>
                    {item.nutritionalInfo.totalCalories} cal
                  </Text>
                </View>
                <View style={styles.listNutritionItem}>
                  <Beef size={14} color={colors.primary} />
                  <Text style={[styles.listNutritionText, { color: colors.text, fontSize: getScaledFontSize(13) }]}>
                    {item.nutritionalInfo.protein}g
                  </Text>
                </View>
                {item.nutritionalInfo.healthMetrics && (
                  <View style={styles.listNutritionItem}>
                    <Heart size={14} color={getHealthScoreColor(item.nutritionalInfo.healthMetrics.healthScore, colors)} />
                    <Text style={[
                      styles.listNutritionText,
                      { 
                        color: getHealthScoreColor(item.nutritionalInfo.healthMetrics.healthScore, colors),
                        fontSize: getScaledFontSize(13)
                      }
                    ]}>
                      {item.nutritionalInfo.healthMetrics.healthScore}/10
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Tags */}
            {item.tags && item.tags.length > 0 && (
              <View style={styles.tagContainer}>
                {item.tags.slice(0, 3).map((tag: string, index: React.Key | null | undefined) => ( // Explicitly type tag as string
                  <View key={index} style={[styles.tag, { backgroundColor: colors.primary + '20' }]}>
                    <Text style={[styles.tagText, { color: colors.primary, fontSize: getScaledFontSize(11) }]}>
                      {tag}
                    </Text>
                  </View>
                ))}
                {item.tags.length > 3 && (
                  <Text style={[styles.tagMore, { color: colors.textSecondary, fontSize: getScaledFontSize(11) }]}>
                    +{item.tags.length - 3}
                  </Text>
                )}
              </View>
            )}

            {/* Location */}
            {item.location && (
              <View style={styles.listLocation}>
                <MapPin size={12} color={colors.textSecondary} />
                <Text style={[styles.listLocationText, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
                  {item.location}
                </Text>
              </View>
            )}
          </View>
        </View>

        {isSelectionMode && (
          <View style={[styles.selectionCheckbox, { backgroundColor: isSelected ? colors.primary : colors.border }]}>
            {isSelected && <CheckCircle size={16} color="white" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const filteredHistory = getFilteredAndSortedHistory();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <Animated.View 
        style={[
          styles.header, 
          { 
            borderBottomColor: colors.border,
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <Text style={[styles.headerTitle, { color: colors.text, fontSize: getScaledFontSize(24) }]}>
          History
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={() => setShowPrivateItems(!showPrivateItems)}>
            {showPrivateItems ? (
              <Eye size={24} color={colors.textSecondary} />
            ) : (
              <EyeOff size={24} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            {viewMode === 'grid' ? (
              <List size={24} color={colors.textSecondary} />
            ) : (
              <Grid3x3 size={24} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setIsFilterModalVisible(true)}>
            <Filter size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Selection Mode Header */}
      {isSelectionMode && (
        <View style={[styles.selectionHeader, { backgroundColor: colors.primary + '10', borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={() => { setIsSelectionMode(false); setSelectedItems([]); }}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.selectionCount, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
            {selectedItems.length} selected
          </Text>
          <View style={styles.selectionActions}>
            <TouchableOpacity onPress={deleteSelectedItems} style={styles.selectionAction}>
              <Trash2 size={20} color={colors.error} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.selectionAction}>
              <Share2 size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <Search size={20} color={colors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: colors.text, fontSize: getScaledFontSize(16) }]}
          placeholder="Search your scans..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery !== '' && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <X size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Stats Row */}
      <View style={styles.statsContainer}>
        <View style={[styles.statItem, { backgroundColor: colors.card }]}>
          <Text style={[styles.statNumber, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
            {scanHistory.length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
            Total Scans
          </Text>
        </View>
        <View style={[styles.statItem, { backgroundColor: colors.card }]}>
          <Text style={[styles.statNumber, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
            {scanHistory.filter(item => item.category === 'Food').length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
            Foods
          </Text>
        </View>
        <View style={[styles.statItem, { backgroundColor: colors.card }]}>
          <Text style={[styles.statNumber, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
            {scanHistory.filter(item => item.isFavorite).length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary, fontSize: getScaledFontSize(12) }]}>
            Favorites
          </Text>
        </View>
      </View>

      {/* Content */}
      {filteredHistory.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Package size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text, fontSize: getScaledFontSize(20) }]}>
            {searchQuery ? 'No results found' : 'No scans yet'}
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary, fontSize: getScaledFontSize(14) }]}>
            {searchQuery 
              ? `No scans match "${searchQuery}"`
              : 'Start scanning to build your history'
            }
          </Text>
          {!searchQuery && (
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: colors.primary }]}
              onPress={() => router.push('/camera')}
            >
              <Text style={[styles.primaryButtonText, { color: colors.buttonText, fontSize: getScaledFontSize(16) }]}>
                Start Scanning
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={filteredHistory}
          renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
          keyExtractor={(item) => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary}
            />
          }
        />
      )}

      {/* Filter Modal */}
      <Modal
        visible={isFilterModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <TouchableOpacity onPress={() => setIsFilterModalVisible(false)}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text, fontSize: getScaledFontSize(18) }]}>
              Filter & Sort
            </Text>
            <TouchableOpacity onPress={() => setIsFilterModalVisible(false)}>
              <Text style={[styles.modalDoneButton, { color: colors.primary, fontSize: getScaledFontSize(16) }]}>
                Done
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Filter Section */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
                Filter by Category
              </Text>
              <View style={styles.filterOptions}>
                {[
                  { key: 'all', label: 'All Items', icon: Package },
                  { key: 'food', label: 'Food', icon: Utensils },
                  { key: 'plants', label: 'Plants', icon: Leaf },
                  { key: 'favorites', label: 'Favorites', icon: Heart },
                  { key: 'recent', label: 'Recent (24h)', icon: Clock },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    style={[
                      styles.filterOption,
                      { 
                        backgroundColor: filterBy === option.key ? colors.primary + '20' : colors.card,
                        borderColor: filterBy === option.key ? colors.primary : colors.border
                      }
                    ]}
                    onPress={() => {
                      setFilterBy(option.key as FilterBy);
                      hapticService.triggerSelectionFeedback();
                    }}
                  >
                    <option.icon 
                      size={20} 
                      color={filterBy === option.key ? colors.primary : colors.textSecondary} 
                    />
                    <Text style={[
                      styles.filterOptionText,
                      { 
                        color: filterBy === option.key ? colors.primary : colors.text,
                        fontSize: getScaledFontSize(14)
                      }
                    ]}>
                      {option.label}
                    </Text>
                    {filterBy === option.key && (
                      <CheckCircle size={16} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Sort Section */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
                Sort by
              </Text>
              <View style={styles.filterOptions}>
                {[
                  { key: 'date', label: 'Date (Newest First)', icon: Calendar },
                  { key: 'name', label: 'Name (A-Z)', icon: ArrowUpDown },
                  { key: 'category', label: 'Category', icon: Package },
                  { key: 'health', label: 'Health Score', icon: Heart },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    style={[
                      styles.filterOption,
                      { 
                        backgroundColor: sortBy === option.key ? colors.primary + '20' : colors.card,
                        borderColor: sortBy === option.key ? colors.primary : colors.border
                      }
                    ]}
                    onPress={() => {
                      setSortBy(option.key as SortBy);
                      hapticService.triggerSelectionFeedback();
                    }}
                  >
                    <option.icon 
                      size={20} 
                      color={sortBy === option.key ? colors.primary : colors.textSecondary} 
                    />
                    <Text style={[
                      styles.filterOptionText,
                      { 
                        color: sortBy === option.key ? colors.primary : colors.text,
                        fontSize: getScaledFontSize(14)
                      }
                    ]}>
                      {option.label}
                    </Text>
                    {sortBy === option.key && (
                      <CheckCircle size={16} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Advanced Options */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text, fontSize: getScaledFontSize(16) }]}>
                Advanced Options
              </Text>
              <TouchableOpacity
                style={[styles.filterOption, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={() => {
                  Alert.alert(
                    'Export History',
                    'Export your scan history as a CSV file?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      { text: 'Export', onPress: () => {
                        hapticService.triggerNotificationFeedback('success');
                        notificationService.sendNotification({
                          title: 'Export Complete',
                          body: 'Your scan history has been exported successfully',
                        });
                      }}
                    ]
                  );
                }}
              >
                <Download size={20} color={colors.textSecondary} />
                <Text style={[styles.filterOptionText, { color: colors.text, fontSize: getScaledFontSize(14) }]}>
                  Export History
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.filterOption, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={() => {
                  Alert.alert(
                    'Clear All History',
                    'This will permanently delete all your scan history. This action cannot be undone.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      { 
                        text: 'Clear All', 
                        style: 'destructive',
                        onPress: () => {
                          scanHistory.length = 0;
                          setIsFilterModalVisible(false);
                          hapticService.triggerNotificationFeedback('warning');
                          notificationService.sendNotification({
                            title: 'History Cleared',
                            body: 'All scan history has been deleted',
                          });
                        }
                      }
                    ]
                  );
                }}
              >
                <Trash2 size={20} color={colors.error} />
                <Text style={[styles.filterOptionText, { color: colors.error, fontSize: getScaledFontSize(14) }]}>
                  Clear All History
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  selectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  selectionCount: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 12,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  // Grid styles
  gridItem: {
    flex: 1,
    margin: 6,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  gridImageContainer: {
    height: 120,
    position: 'relative',
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  gridContent: {
    padding: 12,
  },
  gridHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },
  gridCategory: {
    fontSize: 11,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  gridTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    lineHeight: 18,
  },
  gridNutrition: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  gridCalories: {
    fontSize: 12,
    fontWeight: '600',
  },
  gridHealthScore: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  gridHealthText: {
    fontSize: 10,
    fontWeight: '600',
  },
  gridTime: {
    fontSize: 10,
    fontWeight: '500',
  },
  // List styles
  listItem: {
    marginBottom: 12,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  listContent: {
    padding: 16,
  },
  listImageContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 60,
    height: 60,
    borderRadius: 12,
    overflow: 'hidden',
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listMainContent: {
    paddingRight: 80,
  },
  listHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  listTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  listMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  listCategory: {
    fontSize: 13,
    fontWeight: '500',
  },
  listMetaDivider: {
    width: 1,
    height: 12,
    backgroundColor: '#E5E7EB',
  },
  listTime: {
    fontSize: 13,
    fontWeight: '500',
  },
  listNutrition: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 8,
  },
  listNutritionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  listNutritionText: {
    fontSize: 13,
    fontWeight: '500',
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 11,
    fontWeight: '500',
  },
  tagMore: {
    fontSize: 11,
    fontWeight: '500',
  },
  listLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  listLocationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Common styles
  favoriteIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  privateIcon: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionCheckbox: {
    position: 'absolute',
    top: 12,
    left: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    gap: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 8,
  },
  primaryButton: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalDoneButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalSection: {
    marginBottom: 32,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  filterOptions: {
    gap: 12,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  filterOptionText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
});
