import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  ScrollView,
  Platform,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  X,
  Crown,
  Check,
  Bell,
  Folder,
  ArrowLeft,
  Zap,
  Star,
  Sparkles,
  Shield,
  Infinity,
  Camera,
  Brain,
  FileText,
  Users,
  Trophy,
  Rocket,
  ChevronRight,
  AlertTriangle,
} from 'lucide-react-native';
import { useRevenueCatSubscription } from './RevenueCatSubscriptionContext';
import { useTheme } from '@/components/ThemeContext';
import dayjs from 'dayjs';
import Purchases from 'react-native-purchases';
import { router, useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
}

// Updated subscription plans structure for RevenueCat
const subscriptionPlans = [
  {
    productId: process.env.EXPO_PUBLIC_REVENUECAT_MONTHLY_PRODUCT_ID || 'apex_pro_monthly',
    name: '1 Month',
    price: '$19.99',
    period: '', // Price is per month already
    originalPrice: null,
    badge: null,
    billingCycle: 'monthly' as 'monthly',
    disclaimer: 'Just $19.99 per month. Cancel anytime.',
    buttonLabel: 'Subscribe Now',
    popular: false,
    savings: null,
  },
  {
    productId: process.env.EXPO_PUBLIC_REVENUECAT_YEARLY_PRODUCT_ID || 'apex_pro_yearly',
    name: '1 Year',
    price: '$29.99',
    period: '', // Price is per year already
    originalPrice: null,
    badge: '3 DAYS FREE',
    billingCycle: 'yearly' as 'yearly',
    disclaimer: '3 days free, then $29.99 per year (billed annually)',
    buttonLabel: 'Start 3-Day Free Trial',
    popular: true,
    savings: null,
  },
];

// Feature list with icons and descriptions
const premiumFeatures = [
  {
    icon: Camera,
    title: 'Unlimited Scans',
    description: 'Scan as many specimens as you want without limits',
    free: '5 per day',
    pro: 'Unlimited',
  },
  {
    icon: Brain,
    title: 'Advanced AI Analysis',
    description: 'Get detailed insights with 99% accuracy',
    free: 'Basic',
    pro: 'Advanced',
  },
  {
    icon: FileText,
    title: 'Detailed Fact Sheets',
    description: 'In-depth information about every specimen',
    free: 'Limited',
    pro: 'Complete',
  },
  {
    icon: Folder,
    title: 'Scan History & Collections',
    description: 'Organize and track all your discoveries',
    free: 'Last 10',
    pro: 'Unlimited',
  },
  {
    icon: Users,
    title: 'Priority Support',
    description: 'Get help faster with dedicated support',
    free: 'Community',
    pro: '24/7 Priority',
  },
  {
    icon: Trophy,
    title: 'Achievement System',
    description: 'Unlock badges and track your progress',
    free: false,
    pro: true,
  },
];

// Enhanced Timeline Component (only for yearly trial plan)
const TrialTimeline = ({ colors }: { colors: any }) => {
  const billingDate = dayjs().add(3, 'day').format('MMM D, YYYY');
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const timelineItems = [
    {
      icon: Rocket,
      title: "Today",
      description: "Start your free trial and unlock all premium features instantly",
      color: colors.success,
    },
    {
      icon: Bell,
      title: "Day 2",
      description: "We'll send you a friendly reminder about your trial ending",
      color: colors.accent,
    },
    {
      icon: Crown,
      title: `Day 3 - ${billingDate}`,
      description: "Your subscription begins. Cancel anytime before to avoid charges",
      color: colors.primary,
    }
  ];

  return (
    <Animated.View style={[styles.timelineContainer, { opacity: fadeAnim }]}>
      <View style={styles.timelineHeader}>
        <LinearGradient
          colors={[colors.primary, colors.accent]}
          style={styles.timelineHeaderGradient}>
          <Sparkles size={24} color="#FFFFFF" />
          <Text style={styles.timelineHeaderText}>Your 3-Day Journey</Text>
        </LinearGradient>
      </View>

      <View style={styles.timelineContent}>
        {timelineItems.map((item, index) => (
          <View key={index} style={styles.timelineItem}>
            <View style={styles.timelineIconContainer}>
              <View style={[styles.timelineIcon, { backgroundColor: item.color + '20' }]}>
                <item.icon size={20} color={item.color} />
              </View>
              {index < timelineItems.length - 1 && (
                <View style={[styles.timelineConnector, { backgroundColor: colors.border }]} />
              )}
            </View>
            <View style={styles.timelineTextContainer}>
              <Text style={[styles.timelineTitle, { color: colors.text }]}>{item.title}</Text>
              <Text style={[styles.timelineDescription, { color: colors.textSecondary }]}>
                {item.description}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </Animated.View>
  );
};

// Feature Comparison Component
const FeatureComparison = ({ colors }: { colors: any }) => {
  return (
    <View style={styles.featuresContainer}>
      <Text style={[styles.featuresTitle, { color: colors.text }]}>What You'll Get</Text>

      <View style={styles.featuresList}>
        {premiumFeatures.map((feature, index) => (
          <Animated.View
            key={index}
            style={[styles.featureItem, { backgroundColor: colors.card }]}>
            <View style={[styles.featureIcon, { backgroundColor: colors.primary + '20' }]}>
              <feature.icon size={20} color={colors.primary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>{feature.title}</Text>
              <Text style={[styles.featureDescription, { color: colors.textSecondary }]}>
                {feature.description}
              </Text>
              <View style={styles.featureComparison}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonLabel, { color: colors.textSecondary }]}>Free:</Text>
                  <Text style={[styles.comparisonValue, { color: colors.textSecondary }]}>
                    {typeof feature.free === 'boolean'
                      ? (feature.free ? '✓' : '✗')
                      : feature.free
                    }
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonLabel, { color: colors.primary }]}>Pro:</Text>
                  <Text style={[styles.comparisonValue, { color: colors.primary }]}>
                    {typeof feature.pro === 'boolean'
                      ? (feature.pro ? '✓' : '✗')
                      : feature.pro
                    }
                  </Text>
                </View>
              </View>
            </View>
          </Animated.View>
        ))}
      </View>
    </View>
  );
};

export default function SubscriptionModal({ visible, onClose }: SubscriptionModalProps) {
  const { subscription, checkSubscription } = useRevenueCatSubscription();
  const { colors } = useTheme();

  const [selectedBillingCycle, setSelectedBillingCycle] = useState<'monthly' | 'yearly'>(
    subscriptionPlans.find(plan => plan.popular)?.billingCycle || 'yearly'
  );
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);
  const [availableProducts, setAvailableProducts] = useState<any[]>([]);
  const slideAnim = useRef(new Animated.Value(height)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const selectedPlan = subscriptionPlans.find(plan => plan.billingCycle === selectedBillingCycle);

  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
      loadProducts();
    } else {
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const loadProducts = async () => {
    try {
      const offerings = await Purchases.getOfferings();
      if (offerings.current && offerings.current.availablePackages) {
        setAvailableProducts(offerings.current.availablePackages);
      }
    } catch (error) {
      console.error('Failed to load products:', error);
    }
  };

  const handleSubscription = async () => {
    if (!selectedPlan) return;
    setIsUpgrading(true);

    // Animate button
    Animated.sequence([
      Animated.timing(buttonScale, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScale, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    try {
      // Find the package to purchase
      const packageToPurchase = availableProducts.find(
        (pkg) => pkg.product.identifier === selectedPlan.productId
      );

      if (!packageToPurchase) {
        Alert.alert('Error', 'Product not found. Please try again.');
        return;
      }

      // Make the purchase
      const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);

      // Check if the purchase was successful
      if (customerInfo.entitlements.active.premium) {
        Alert.alert(
          'Subscription Successful!',
          'Welcome to Apex Pro! You now have access to all premium features.',
          [
            {
              text: 'Great!',
              onPress: async () => {
                checkSubscription(); // Refresh subscription status

                // Prompt for Google sign-in
                Alert.alert(
                  'Sync Across Devices',
                  'Would you like to sign in with Google to sync your subscription across devices?',
                  [
                    { text: 'Later', style: 'cancel' },
                    { text: 'Sign In', onPress: () => router.push('/loginscreen') }
                  ]
                );

                onClose(); // Close the modal
              },
            },
          ]
        );
      } else {
        Alert.alert('Purchase Error', 'There was an issue with your purchase. Please contact support.');
      }
    } catch (error: any) {
      if (!error.userCancelled) {
        console.error('Purchase failed:', error);
        Alert.alert('Purchase Failed', error.message || 'An unexpected error occurred.');
      }
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleRestorePurchases = async () => {
    try {
      setIsUpgrading(true);
      await Purchases.restorePurchases();
      await checkSubscription();
      
      if (subscription.isSubscribed) {
        Alert.alert('Restore Successful', 'Your subscription has been restored!');
        onClose();
      } else {
        Alert.alert('No Purchases Found', 'No active subscriptions found to restore.');
      }
    } catch (error) {
      console.error('Restore failed:', error);
      Alert.alert('Restore Failed', 'Unable to restore purchases. Please try again.');
    } finally {
      setIsUpgrading(false);
    }
  };

  if (!colors) return null;

  return (
    <Modal
      visible={visible}
      animationType="none"
      presentationStyle="overFullScreen"
      onRequestClose={onClose}
      transparent={true}>
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              backgroundColor: colors.background,
              transform: [{ translateY: slideAnim }]
            }
          ]}>

          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <ArrowLeft size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: colors.text }]}>Upgrade to Pro</Text>
            <TouchableOpacity 
              style={styles.restoreButton} 
              onPress={handleRestorePurchases}
              disabled={isUpgrading}>
              <Text style={[styles.restoreText, { color: colors.primary }]}>Restore</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}>

            {/* Hero Section */}
            <LinearGradient
              colors={[colors.primary + '20', colors.accent + '10']}
              style={styles.heroSection}>
              <View style={styles.heroContent}>
                <Crown size={48} color={colors.primary} />
                <Text style={[styles.heroTitle, { color: colors.text }]}>
                  Unlock the Full Power of Apex
                </Text>
                <Text style={[styles.heroSubtitle, { color: colors.textSecondary }]}>
                  Get unlimited scans, advanced AI analysis, and premium features
                </Text>
              </View>
            </LinearGradient>

            {/* Content based on selected plan */}
            {selectedBillingCycle === 'yearly' && selectedPlan?.badge === '3 DAYS FREE' ? (
              <TrialTimeline colors={colors} />
            ) : (
              <FeatureComparison colors={colors} />
            )}

            {/* Plan Selection */}
            <View style={styles.planSection}>
              <Text style={[styles.planSectionTitle, { color: colors.text }]}>Choose Your Plan</Text>

              <View style={styles.planContainer}>
                {subscriptionPlans.map((plan) => {
                  const isSelected = selectedBillingCycle === plan.billingCycle;
                  const product = availableProducts.find(p => p.product.identifier === plan.productId);
                  const displayPrice = product ? product.product.priceString : plan.price;
                  
                  return (
                    <TouchableOpacity
                      key={plan.billingCycle}
                      style={[
                        styles.planCard,
                        {
                          borderColor: isSelected ? colors.primary : colors.border,
                          backgroundColor: isSelected ? colors.primary + '10' : colors.card,
                        },
                        isSelected && styles.selectedPlanCard,
                      ]}
                      onPress={() => setSelectedBillingCycle(plan.billingCycle)}
                      activeOpacity={0.8}>

                      {plan.popular && (
                        <View style={[styles.popularBadge, { backgroundColor: colors.accent }]}>
                          <Star size={12} color="#FFFFFF" />
                          <Text style={styles.popularText}>MOST POPULAR</Text>
                        </View>
                      )}

                      {plan.badge && (
                        <View style={[
                          styles.trialBadge, 
                          { 
                            backgroundColor: colors.success,
                            top: plan.popular ? 40 : -12 
                          }
                        ]}>
                          <Text style={styles.badgeText}>{plan.badge}</Text>
                        </View>
                      )}

                      <View style={styles.planCardContent}>
                        <View style={styles.planInfo}>
                          <Text style={[styles.planName, { color: colors.text }]}>{plan.name}</Text>
                          <View style={styles.priceContainer}>
                            <Text style={[styles.planPrice, { color: colors.text }]}>
                              {displayPrice}
                              <Text style={[styles.planPeriod, { color: colors.textSecondary }]}>
                                {plan.period}
                              </Text>
                            </Text>
                            {plan.originalPrice && (
                              <Text style={[styles.originalPrice, { color: colors.textSecondary }]}>
                                {plan.originalPrice}
                              </Text>
                            )}
                          </View>
                          {plan.savings && (
                            <View style={[styles.savingsBadge, { backgroundColor: colors.success + '20' }]}>
                              <Text style={[styles.savingsText, { color: colors.success }]}>
                                {plan.savings}
                              </Text>
                            </View>
                          )}
                        </View>

                        <View style={[
                          styles.radioButton,
                          { borderColor: isSelected ? colors.primary : colors.border }
                        ]}>
                          {isSelected && (
                            <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Features Toggle */}
            <TouchableOpacity
              style={[styles.featuresToggle, { backgroundColor: colors.card }]}
              onPress={() => setShowFeatures(!showFeatures)}>
              <Text style={[styles.featuresToggleText, { color: colors.primary }]}>
                {showFeatures ? 'Hide Features' : 'See All Features'}
              </Text>
              <ChevronRight
                size={20}
                color={colors.primary}
                style={{ transform: [{ rotate: showFeatures ? '90deg' : '0deg' }] }}
              />
            </TouchableOpacity>

            {showFeatures && <FeatureComparison colors={colors} />}

            {/* Trust Indicators */}
            <View style={styles.trustSection}>
              <View style={[styles.trustItem, { backgroundColor: colors.card }]}>
                <Shield size={20} color={colors.success} />
                <Text style={[styles.trustText, { color: colors.text }]}>Cancel anytime, no commitments</Text>
              </View>
              <View style={[styles.trustItem, { backgroundColor: colors.card }]}>
                <AlertTriangle size={20} color={colors.accent} />
                <Text style={[styles.trustText, { color: colors.text }]}>Secure payment via App Store</Text>
              </View>
            </View>
          </ScrollView>

          {/* Action Footer */}
          <View style={[styles.actionFooter, { backgroundColor: colors.background }]}>
            <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
              <TouchableOpacity
                style={[
                  styles.subscribeButton,
                  { backgroundColor: colors.primary },
                  isUpgrading && styles.subscribeButtonDisabled
                ]}
                onPress={handleSubscription}
                disabled={isUpgrading}
                activeOpacity={0.8}>
                <LinearGradient
                  colors={[colors.primary, colors.accent]}
                  style={styles.subscribeButtonGradient}>
                  {isUpgrading ? (
                    <Text style={styles.subscribeButtonText}>Processing...</Text>
                  ) : (
                    <>
                      <Crown size={20} color="#FFFFFF" />
                      <Text style={styles.subscribeButtonText}>
                        {selectedPlan?.buttonLabel}
                      </Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            <Text style={[styles.disclaimer, { color: colors.textSecondary }]}>
              {selectedPlan?.disclaimer}
            </Text>

            <Text style={[styles.terms, { color: colors.textSecondary }]}>
              By subscribing, you agree to our Terms of Service and Privacy Policy. 
              Subscription automatically renews unless canceled at least 24 hours before the end of the current period.
            </Text>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    flex: 1,
    marginTop: 50,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  restoreButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  restoreText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },

  // Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Hero Section
  heroSection: {
    margin: 20,
    borderRadius: 20,
    padding: 24,
  },
  heroContent: {
    alignItems: 'center',
    gap: 12,
  },
  heroTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    lineHeight: 30,
  },
  heroSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 22,
  },

  // Timeline
  timelineContainer: {
    margin: 20,
  },
  timelineHeader: {
    marginBottom: 20,
  },
  timelineHeaderGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 16,
    gap: 8,
  },
  timelineHeaderText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  timelineContent: {
    gap: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  timelineIconContainer: {
    alignItems: 'center',
  },
  timelineIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  timelineConnector: {
    width: 2,
    flex: 1,
    minHeight: 20,
  },
  timelineTextContainer: {
    flex: 1,
    paddingTop: 8,
  },
  timelineTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  timelineDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },

  // Features
  featuresContainer: {
    margin: 20,
  },
  featuresTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 16,
    alignItems: 'flex-start',
    gap: 16,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 8,
  },
  featureComparison: {
    flexDirection: 'row',
    gap: 16,
  },
  comparisonItem: {
    flex: 1,
  },
  comparisonLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginBottom: 2,
  },
  comparisonValue: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
  },

  // Plans
  planSection: {
    margin: 20,
  },
  planSectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  planContainer: {
    gap: 16,
  },
  planCard: {
    borderRadius: 16,
    borderWidth: 2,
    padding: 20,
    position: 'relative',
  },
  selectedPlanCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  popularBadge: {
    position: 'absolute',
    top: -12,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
    zIndex: 1,
  },
  popularText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  trialBadge: {
    position: 'absolute',
    alignSelf: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    zIndex: 1,
  },
  badgeText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  planCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 8,
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
  },
  planPeriod: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  originalPrice: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    textDecorationLine: 'line-through',
  },
  savingsBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  savingsText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },

  // Features Toggle
  featuresToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 16,
    borderRadius: 16,
    gap: 8,
  },
  featuresToggleText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },

  // Trust Section
  trustSection: {
    marginHorizontal: 20,
    gap: 12,
  },
  trustItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  trustText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },

  // Action Footer
  actionFooter: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  subscribeButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 12,
  },
  subscribeButtonDisabled: {
    opacity: 0.6,
  },
  subscribeButtonGradient: {
    flexDirection: 'row',    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  subscribeButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  disclaimer: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 16,
  },
  terms: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 14,
  },
});
