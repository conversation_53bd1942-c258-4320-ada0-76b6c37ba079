// ========== TYPESCRIPT DEFINITIONS ==========
interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{ text: string }>;
    };
  }>;
}

/* ---------- ENHANCED: comprehensive categories ---------- */
export type Category =
  | 'Animal'
  | 'Plant'
  | 'Food'
  | 'Insect'
  | 'Rock / Mineral'
  | 'Coin / Currency'
  | 'Wine'
  | 'Product'
  | 'Electronics'
  | 'Clothing'
  | 'Book'
  | 'Medicine'
  | 'Tool'
  | 'Art'
  | 'Unknown';

/* ---------- ENHANCED: Food and Nutritional Information ---------- */
export interface Ingredient {
  name: string;
  quantity: string;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  estimatedWeight: number; // in grams
}

export interface ServingInfo {
  servingSize: string;
  servingsPerContainer?: number;
  totalWeight: string;
  estimatedPortions: number;
}

export interface HealthMetrics {
  healthScore: number; // 1-10 scale
  glycemicIndex?: number;
  inflammatoryScore?: number;
  nutrientDensity: number;
  processingLevel: 'Minimal' | 'Moderate' | 'Highly Processed';
}

export interface NutritionalInfo {
  // Basic macros
  totalCalories: number;
  caloriesPerServing: number;
  protein: number; // grams
  carbs: number; // grams
  fat: number; // grams
  
  // Detailed nutrients
  fiber: number;
  sugar: number;
  addedSugar?: number;
  sodium: number; // mg
  cholesterol: number; // mg
  saturatedFat: number;
  transFat: number;
  monounsaturatedFat: number;
  polyunsaturatedFat: number;
  
  // Vitamins (as % daily value)
  vitaminA?: number;
  vitaminC?: number;
  vitaminD?: number;
  vitaminE?: number;
  vitaminK?: number;
  vitaminB6?: number;
  vitaminB12?: number;
  folate?: number;
  thiamine?: number;
  riboflavin?: number;
  niacin?: number;
  
  // Minerals (as % daily value)
  calcium?: number;
  iron?: number;
  magnesium?: number;
  phosphorus?: number;
  potassium?: number;
  zinc?: number;
  
  // Food-specific
  allergens: string[];
  dietaryRestrictions: string[]; // vegan, keto, gluten-free, etc.
  ingredients: Ingredient[];
  servingInfo: ServingInfo;
  healthMetrics: HealthMetrics;
}

// In @/components/GeminiService file:
export interface HealthMetrics {
  healthScore: number;
  sugarLevel: 'Low' | 'Moderate' | 'High';
  sodiumLevel: 'Low' | 'Moderate' | 'High';
  fiberLevel: 'Low' | 'Moderate' | 'Good' | 'High';
  processedFoodScore: number; // Add this line
}

export interface FoodAnalysis {
  dishName: string;
  cuisine?: string;
  mealType?: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snack' | 'Dessert';
  preparationMethod?: string;
  estimatedCookingTime?: string;
  difficulty?: 'Easy' | 'Medium' | 'Hard';
  
  // Ingredient breakdown
  mainIngredients: string[];
  seasonings: string[];
  cookingOils?: string[];
  
  // Nutritional insights
  calorieBreakdown: {
    fromProtein: number; // percentage
    fromCarbs: number;
    fromFats: number;
  };
  
  // Health insights
  healthBenefits: string[];
  potentialConcerns: string[];
  nutritionalHighlights: string[];
  
  // Alternatives and modifications
  healthierAlternatives?: Array<{
    suggestion: string;
    benefit: string;
    calorieReduction?: number;
  }>;
  
  // Similar foods
  similarFoods?: Array<{
    name: string;
    calories: number;
    healthScore: number;
    reason: string;
  }>;
}

/* ---------- EXISTING INTERFACES (keeping for backward compatibility) ---------- */
export interface ProductInfo {
  brand: string;
  model: string;
  category: string;
  estimatedPrice: {
    currency: string;
    min: number;
    max: number;
    average: number;
  };
  availability: {
    inStock: boolean;
    stores: string[];
    onlineRetailers: string[];
  };
  specifications: Record<string, string>;
  features: string[];
  pros: string[];
  cons: string[];
  alternatives: Array<{
    name: string;
    brand: string;
    price: number;
    rating: number;
    reason: string;
  }>;
  reviews: {
    averageRating: number;
    totalReviews: number;
    summary: string;
  };
  sustainability: {
    ecoFriendly: boolean;
    recyclable: boolean;
    carbonFootprint: string;
  };
}

export interface WineInfo {
  region: string;
  vintage?: string;
  grapeVariety: string[];
  tastingNotes: string[];
  abv: string;
  pairing: string[];
  winery: string;
  appellation?: string;
  style: string;
  servingTemp: string;
  agingPotential: string;
  priceRange: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface MineralInfo {
  mohsHardness: string;
  chemicalFormula: string;
  crystalSystem: string;
  streakColour: string;
  lustre: string;
  density: string;
  formation: string;
  uses: string[];
  rarity: 'Common' | 'Uncommon' | 'Rare' | 'Very Rare';
  locations: string[];
  collectibleValue?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface CoinInfo {
  country: string;
  denomination: string;
  year: string;
  composition: string;
  diameter: string;
  weight: string;
  mintage?: string;
  designer?: string;
  mint?: string;
  condition?: string;
  rarity: string;
  numismaticValue: {
    min: number;
    max: number;
    currency: string;
  };
  historicalSignificance?: string;
  numistaId?: string;
}

// Define PhysicalTraits here as well for clarity and explicit typing
export interface PhysicalTraits {
  size?: string;
  weight?: string;
  lifespan?: string;
  diet?: string;
}

/* ---------- MAIN RESULT ---------- */
export interface IdentificationResult {
  species: any;
  details: {};
  name: string;
  scientificName: string;
  confidence: number;
  category: Category;
  description: string;
  habitat?: string;
  conservationStatus?: string;
  facts?: string[]; // Made optional and explicitly array of strings
  physicalTraits?: PhysicalTraits; // Explicitly use the PhysicalTraits interface
  alternatives?: Array<{ name: string; scientificName: string; confidence: number }>; // Made optional and explicitly array of objects

  /* Enhanced food-specific info */
  nutritionalInfo?: NutritionalInfo;
  foodAnalysis?: FoodAnalysis;
  
  /* Other category-specific info */
  wineInfo?: WineInfo;
  rockInfo?: MineralInfo;
  coinInfo?: CoinInfo;
  productInfo?: ProductInfo;

  /* Enhanced general fields */
  behavior?: string;
  reproduction?: string;
  distinguishingFeatures?: string[]; // Made optional and explicitly array of strings
  ecosystemRole?: string;
  threats?: string[]; // Made optional and explicitly array of strings
  
  /* New fields for enhanced scanning */
  usage?: string[];
  maintenance?: string[];
  safety?: string[];
  relatedItems?: string[];
  marketTrends?: {
    trending: boolean;
    popularityScore: number;
    seasonality?: string;
  };
  environmentalImpact?: {
    carbonFootprint: string;
    recyclability: string;
    sustainability: string;
  };
}

// ========== ENHANCED SERVICE CLASS ==========
export class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent';

  constructor() {
    this.apiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';
    if (!this.apiKey) {
      console.warn('Gemini API key not found. Ensure EXPO_PUBLIC_GEMINI_API_KEY is set.');
    }
  }

  async identifySpecimen(imageBase64: string, additionalContext?: string): Promise<IdentificationResult> {
    if (!this.apiKey) {
      throw new Error('AI model API key is not configured. Please set EXPO_PUBLIC_GEMINI_API_KEY.');
    }

    const prompt = this.buildEnhancedFoodPrompt(additionalContext);

    try {
      const payload = {
        contents: [{
          parts: [
            { text: prompt },
            { inlineData: { mimeType: 'image/jpeg', data: imageBase64 } }
          ]
        }],
        generationConfig: { 
          temperature: 0.1, 
          topK: 32, 
          topP: 1, 
          maxOutputTokens: 8192 
        }
      };

      const apiUrl = `${this.baseUrl}?key=${this.apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorBody = await response.json().catch(() => ({}));
        console.error('AI model API error:', response.status, errorBody);
        throw new Error(`AI model error: ${errorBody.error?.message || response.statusText || 'Unknown error'}`);
      }

      const data: GeminiResponse = await response.json();
      const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!text) {
        throw new Error('Empty or malformed response from AI model');
      }
      return this.parseEnhancedResult(text);
    } catch (error) {
      console.error('Error in identifySpecimen:', error);
      throw error;
    }
  }

  private buildEnhancedFoodPrompt(ctx?: string): string {
    return `
You are an expert nutritionist and food analyst with comprehensive knowledge of:
- Food identification and nutrition analysis
- Ingredient recognition and portion estimation
- Calorie calculation and macronutrient breakdown
- Health assessment and dietary recommendations
- Food preparation and cooking methods

Analyze the image and provide detailed nutritional and food information. Return **only** JSON.

${ctx ? `User context: ${ctx}` : ''}

For FOOD items, provide comprehensive analysis including:
- Accurate calorie estimation based on visible portions
- Detailed macronutrient breakdown (protein, carbs, fats)
- Individual ingredient identification with estimated quantities
- Micronutrient content (vitamins, minerals)
- Health score assessment (1-10 scale)
- Allergen identification
- Dietary restriction compatibility
- Healthier alternatives and modifications
- Cooking method analysis
- Portion size estimation

CRITICAL: For food analysis, be precise with calorie calculations. Consider:
- Visible portion sizes
- Cooking methods (affects calories)
- Added fats, oils, sauces
- Preparation style
- Typical serving sizes

{
  "name": "Dish name or food item",
  "scientificName": "N/A for prepared foods, species name for whole foods",
  "confidence": 0-100,
  "category": "Food|Animal|Plant|Product|etc",
  "description": "Detailed description of the food, preparation method, and visual characteristics",
  
  "nutritionalInfo": {
    "totalCalories": 450,
    "caloriesPerServing": 450,
    "protein": 35.2,
    "carbs": 28.5,
    "fat": 22.1,
    "fiber": 4.2,
    "sugar": 8.3,
    "addedSugar": 2.1,
    "sodium": 650,
    "cholesterol": 75,
    "saturatedFat": 8.2,
    "transFat": 0,
    "monounsaturatedFat": 9.8,
    "polyunsaturatedFat": 4.1,
    
    "vitaminA": 25,
    "vitaminC": 45,
    "vitaminD": 8,
    "calcium": 15,
    "iron": 12,
    "potassium": 18,
    
    "allergens": ["dairy", "gluten"],
    "dietaryRestrictions": ["keto-friendly", "high-protein"],
    
    "ingredients": [
      {
        "name": "salmon fillet",
        "quantity": "150",
        "unit": "g",
        "calories": 280,
        "protein": 25.0,
        "carbs": 0,
        "fat": 18.0,
        "estimatedWeight": 150
      },
      {
        "name": "broccoli",
        "quantity": "100",
        "unit": "g", 
        "calories": 35,
        "protein": 3.0,
        "carbs": 7.0,
        "fat": 0.4,
        "fiber": 2.6,
        "estimatedWeight": 100
      }
    ],
    
    "servingInfo": {
      "servingSize": "1 plate",
      "totalWeight": "300g",
      "estimatedPortions": 1
    },
    
    "healthMetrics": {
      "healthScore": 8,
      "glycemicIndex": 35,
      "nutrientDensity": 7,
      "processingLevel": "Minimal"
    }
  },
  
  "foodAnalysis": {
    "dishName": "Grilled Salmon with Roasted Broccoli",
    "cuisine": "Mediterranean",
    "mealType": "Dinner",
    "preparationMethod": "Grilled and roasted",
    "estimatedCookingTime": "25 minutes",
    "difficulty": "Easy",
    
    "mainIngredients": ["salmon", "broccoli"],
    "seasonings": ["olive oil", "salt", "pepper", "herbs"],
    "cookingOils": ["olive oil"],
    
    "calorieBreakdown": {
      "fromProtein": 35,
      "fromCarbs": 25,
      "fromFats": 40
    },
    
    "healthBenefits": [
      "High in omega-3 fatty acids for heart health",
      "Rich in vitamin C and fiber from broccoli",
      "Complete protein source",
      "Anti-inflammatory properties"
    ],
    
    "potentialConcerns": [
      "High sodium content if over-seasoned",
      "Mercury content in salmon (moderate levels)"
    ],
    
    "nutritionalHighlights": [
      "Excellent protein-to-calorie ratio",
      "Low glycemic index meal",
      "Rich in essential fatty acids",
      "High nutrient density"
    ],
    
    "healthierAlternatives": [
      {
        "suggestion": "Steam broccoli instead of roasting with oil",
        "benefit": "Reduces calories from added fats",
        "calorieReduction": 50
      },
      {
        "suggestion": "Add quinoa for complex carbs",
        "benefit": "Increases fiber and provides sustained energy"
      }
    ],
    
    "similarFoods": [
      {
        "name": "Grilled Chicken with Asparagus",
        "calories": 320,
        "healthScore": 8,
        "reason": "Similar preparation method and nutritional profile"
      }
    ]
  },
  
  "facts": ["Salmon provides all essential amino acids", "Broccoli is a cruciferous vegetable with cancer-fighting compounds"],
  "physicalTraits": {},
  "alternatives": [],
  "distinguishingFeatures": ["Flaky fish texture", "Bright green vegetables", "Minimal processing"],
  
  "usage": ["Post-workout meal", "Heart-healthy dinner option", "Weight management"],
  "safety": ["Ensure salmon is cooked to 145°F internal temperature", "Wash vegetables thoroughly"],
  
  "environmentalImpact": {
    "carbonFootprint": "Medium - wild-caught salmon has lower impact than farmed",
    "sustainability": "Choose sustainably sourced salmon, local/seasonal vegetables"
  }
}

IMPORTANT: 
- For complex dishes, break down each visible ingredient
- Estimate portions based on plate size and visual cues
- Consider cooking methods in calorie calculations
- Provide actionable health insights
- Be conservative with health claims
- Include confidence levels for nutritional estimates
`.trim();
  }

  private parseEnhancedResult(raw: string): IdentificationResult {
    try {
      const cleaned = raw.replace(/^```(?:json)?\s*|```\s*$/g, '').trim();
      const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');
      const p = JSON.parse(jsonMatch[0]);

      const result: IdentificationResult = {
        name: p.name || 'Unidentified',
        scientificName: p.scientificName || 'N/A',
        confidence: Math.min(100, Math.max(0, p.confidence ?? 0)),
        category: (p.category || 'Unknown') as Category,
        description: p.description || 'No description available.',
        habitat: p.habitat,
        conservationStatus: p.conservationStatus,
        facts: Array.isArray(p.facts) ? p.facts : [], // Ensure it's an array
        physicalTraits: p.physicalTraits || {}, // Ensure it's an object
        alternatives: Array.isArray(p.alternatives) ? p.alternatives : [], // Ensure it's an array
        
        // Enhanced fields
        usage: Array.isArray(p.usage) ? p.usage : [],
        maintenance: Array.isArray(p.maintenance) ? p.maintenance : [],
        safety: Array.isArray(p.safety) ? p.safety : [],
        relatedItems: Array.isArray(p.relatedItems) ? p.relatedItems : [],
        marketTrends: p.marketTrends,
        environmentalImpact: p.environmentalImpact,
        
        // Enhanced food-specific info
        nutritionalInfo: p.nutritionalInfo,
        foodAnalysis: p.foodAnalysis,
        
        // Other category-specific info
        wineInfo: p.wineInfo,
        rockInfo: p.rockInfo,
        coinInfo: p.coinInfo,
        productInfo: p.productInfo,
        
        behavior: p.behavior,
        reproduction: p.reproduction,
        distinguishingFeatures: Array.isArray(p.distinguishingFeatures) ? p.distinguishingFeatures : [], // Ensure it's an array
        ecosystemRole: p.ecosystemRole,
        threats: Array.isArray(p.threats) ? p.threats : [] // Ensure it's an array
      };
      return result;
    } catch (err) {
      console.error('Parse error:', err);
      const errorResult: IdentificationResult = {
        name: 'Parsing Failed',
        scientificName: 'N/A',
        confidence: 0,
        category: 'Unknown',
        description: 'Could not parse AI model response. Please try again.',
        facts: [],
        physicalTraits: {},
        alternatives: [],
        distinguishingFeatures: [],
      };
      return errorResult;
    }
  }
}

export default GeminiService;