import GeminiService from '@/components/GeminiService';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface ApiStatus {
  connected: boolean;
  lastChecked: Date | null;
  responseTime: number | null;
  error: string | null;
}

interface ApiUsage {
  requestsToday: number;
  requestsThisMonth: number;
  dailyLimit: number;
  monthlyLimit: number;
}

export default function ApiConfigScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [apiKeyConfigured, setApiKeyConfigured] = useState(false);

  // Helper functions for colors
  const getCardColor = () => {
    return colorScheme === 'dark' ? '#2C2C2E' : '#F2F2F7';
  };

  const [apiStatus, setApiStatus] = useState<ApiStatus>({
    connected: false,
    lastChecked: null,
    responseTime: null,
    error: null,
  });
  const [apiUsage, setApiUsage] = useState<ApiUsage>({
    requestsToday: 0,
    requestsThisMonth: 0,
    dailyLimit: 1000,
    monthlyLimit: 30000,
  });

  useEffect(() => {
    // Check if API key is configured in environment
    const envApiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';
    setApiKeyConfigured(!!envApiKey);

    // Load mock usage data
    setApiUsage({
      requestsToday: 47,
      requestsThisMonth: 1250,
      dailyLimit: 1000,
      monthlyLimit: 30000,
    });

    // Auto-test connection if API key is configured
    if (envApiKey) {
      testApiConnection();
    }
  }, []);

  const testApiConnection = async () => {
    const envApiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';

    if (!envApiKey) {
      Alert.alert('Error', 'API key not configured. Please contact the app administrator.');
      setApiStatus({
        connected: false,
        lastChecked: new Date(),
        responseTime: null,
        error: 'API key not configured',
      });
      return;
    }

    setIsTestingConnection(true);

    try {
      // Use GeminiService to test connection
      const geminiService = new GeminiService();
      const result = await geminiService.testConnection();

      setApiStatus({
        connected: result.success,
        lastChecked: new Date(),
        responseTime: result.responseTime,
        error: result.error || null,
      });

      if (result.success) {
        Alert.alert(
          'Connection Successful!',
          `API responded in ${result.responseTime}ms\n\nConnection test passed successfully.`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Connection Failed', result.error || 'Unknown error occurred');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setApiStatus({
        connected: false,
        lastChecked: new Date(),
        responseTime: null,
        error: errorMessage,
      });

      Alert.alert('Connection Error', errorMessage);
    } finally {
      setIsTestingConnection(false);
    }
  };



  const getStatusColor = () => {
    if (apiStatus.connected) return '#4CAF50';
    if (apiStatus.error) return '#F44336';
    return '#9E9E9E';
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };



  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.back()}
        >
          <IconSymbol size={24} name="arrow.left" color={Colors[colorScheme ?? 'light'].text} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>API Configuration</ThemedText>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* API Configuration Status */}
        <ThemedView style={[styles.section, { backgroundColor: getCardColor() }]}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={20} name="key.fill" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.sectionTitle}>API Configuration</ThemedText>
          </View>

          <View style={styles.statusContainer}>
            <View style={styles.statusRow}>
              <ThemedText style={styles.statusLabel}>API Key Status:</ThemedText>
              <View style={styles.statusIndicator}>
                <View style={[styles.statusDot, { backgroundColor: apiKeyConfigured ? '#4CAF50' : '#F44336' }]} />
                <ThemedText style={[styles.statusText, { color: apiKeyConfigured ? '#4CAF50' : '#F44336' }]}>
                  {apiKeyConfigured ? 'Configured' : 'Not Configured'}
                </ThemedText>
              </View>
            </View>

            <View style={styles.statusRow}>
              <ThemedText style={styles.statusLabel}>Model:</ThemedText>
              <ThemedText style={styles.statusValue}>Gemini 2.5 Flash</ThemedText>
            </View>

            <View style={styles.statusRow}>
              <ThemedText style={styles.statusLabel}>Provider:</ThemedText>
              <ThemedText style={styles.statusValue}>Google AI</ThemedText>
            </View>
          </View>

          <ThemedText style={styles.helpText}>
            API configuration is managed by the system administrator. Contact support if you experience issues.
          </ThemedText>
        </ThemedView>

        {/* Connection Status */}
        <ThemedView style={[styles.section, { backgroundColor: getCardColor() }]}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={20} name="network" color={getStatusColor()} />
            <ThemedText style={styles.sectionTitle}>Connection Status</ThemedText>
          </View>
          
          <View style={styles.statusContainer}>
            <View style={styles.statusRow}>
              <ThemedText style={styles.statusLabel}>Status:</ThemedText>
              <View style={styles.statusIndicator}>
                <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
                <ThemedText style={[styles.statusText, { color: getStatusColor() }]}>
                  {apiStatus.connected ? 'Connected' : apiStatus.error ? 'Error' : 'Not tested'}
                </ThemedText>
              </View>
            </View>
            
            {apiStatus.lastChecked && (
              <View style={styles.statusRow}>
                <ThemedText style={styles.statusLabel}>Last checked:</ThemedText>
                <ThemedText style={styles.statusValue}>
                  {apiStatus.lastChecked.toLocaleTimeString()}
                </ThemedText>
              </View>
            )}
            
            {apiStatus.responseTime && (
              <View style={styles.statusRow}>
                <ThemedText style={styles.statusLabel}>Response time:</ThemedText>
                <ThemedText style={styles.statusValue}>
                  {apiStatus.responseTime}ms
                </ThemedText>
              </View>
            )}
            
            {apiStatus.error && (
              <View style={styles.errorContainer}>
                <ThemedText style={styles.errorText}>
                  {apiStatus.error}
                </ThemedText>
              </View>
            )}
          </View>
          
          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
            onPress={testApiConnection}
            disabled={isTestingConnection}
          >
            {isTestingConnection ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <IconSymbol size={20} name="play.circle.fill" color="white" />
            )}
            <Text style={styles.testButtonText}>
              {isTestingConnection ? 'Testing...' : 'Test Connection'}
            </Text>
          </TouchableOpacity>
        </ThemedView>

        {/* Usage Statistics */}
        <ThemedView style={[styles.section, { backgroundColor: getCardColor() }]}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={20} name="chart.bar.fill" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.sectionTitle}>Usage Statistics</ThemedText>
          </View>
          
          <View style={styles.usageContainer}>
            <View style={styles.usageItem}>
              <ThemedText style={styles.usageLabel}>Today</ThemedText>
              <View style={styles.usageBar}>
                <View 
                  style={[
                    styles.usageProgress, 
                    { 
                      width: `${getUsagePercentage(apiUsage.requestsToday, apiUsage.dailyLimit)}%`,
                      backgroundColor: Colors[colorScheme ?? 'light'].tint 
                    }
                  ]} 
                />
              </View>
              <ThemedText style={styles.usageText}>
                {apiUsage.requestsToday} / {apiUsage.dailyLimit}
              </ThemedText>
            </View>
            
            <View style={styles.usageItem}>
              <ThemedText style={styles.usageLabel}>This Month</ThemedText>
              <View style={styles.usageBar}>
                <View 
                  style={[
                    styles.usageProgress, 
                    { 
                      width: `${getUsagePercentage(apiUsage.requestsThisMonth, apiUsage.monthlyLimit)}%`,
                      backgroundColor: Colors[colorScheme ?? 'light'].tint 
                    }
                  ]} 
                />
              </View>
              <ThemedText style={styles.usageText}>
                {apiUsage.requestsThisMonth} / {apiUsage.monthlyLimit}
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* API Information */}
        <ThemedView style={[styles.section, { backgroundColor: getCardColor() }]}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={20} name="info.circle.fill" color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.sectionTitle}>API Information</ThemedText>
          </View>
          
          <View style={styles.infoContainer}>
            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Model:</ThemedText>
              <ThemedText style={styles.infoValue}>Gemini 2.5 Flash</ThemedText>
            </View>
            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Endpoint:</ThemedText>
              <ThemedText style={styles.infoValue} numberOfLines={2}>
                generativelanguage.googleapis.com
              </ThemedText>
            </View>
            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Version:</ThemedText>
              <ThemedText style={styles.infoValue}>v1beta</ThemedText>
            </View>
          </View>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  section: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  helpText: {
    fontSize: 12,
    opacity: 0.7,
    lineHeight: 16,
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  errorText: {
    color: '#C62828',
    fontSize: 12,
    lineHeight: 16,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  usageContainer: {
    gap: 16,
  },
  usageItem: {
    gap: 8,
  },
  usageLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  usageBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  usageProgress: {
    height: '100%',
    borderRadius: 4,
  },
  usageText: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'right',
  },
  infoContainer: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    opacity: 0.7,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
});
