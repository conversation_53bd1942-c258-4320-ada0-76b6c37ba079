import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface TaskReminder {
  id: string;
  title: string;
  body: string;
  scheduledTime: Date;
  taskId: string;
  type: 'task' | 'scan_reminder' | 'daily_goal' | 'streak_reminder';
  data?: any;
}

export class NotificationManager {
  private static instance: NotificationManager;
  private notificationToken: string | null = null;

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  /**
   * Initialize notification system and request permissions
   */
  async initialize(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        console.warn('Notifications only work on physical devices');
        return false;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permission not granted');
        return false;
      }

      // Get push token
      this.notificationToken = (await Notifications.getExpoPushTokenAsync()).data;
      console.log('Push token:', this.notificationToken);

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6366F1',
        });

        await Notifications.setNotificationChannelAsync('tasks', {
          name: 'Task Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#10B981',
        });

        await Notifications.setNotificationChannelAsync('goals', {
          name: 'Daily Goals',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250],
          lightColor: '#F59E0B',
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
      return false;
    }
  }

  /**
   * Schedule a task reminder notification
   */
  async scheduleTaskReminder(reminder: TaskReminder): Promise<string | null> {
    try {
      if (!reminder || !reminder.id || !reminder.title || !reminder.body || !reminder.scheduledTime || !reminder.taskId || !reminder.type) {
        throw new Error('Invalid reminder object');
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: reminder.title,
          body: reminder.body,
          data: {
            type: reminder.type,
            taskId: reminder.taskId,
            ...reminder.data,
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: reminder.scheduledTime.getTime(),
        },
      });

      // Store reminder for management
      await this.storeReminder(reminder.id, {
        ...reminder,
        notificationId,
      });

      return notificationId;
    } catch (error) {
      console.error('Failed to schedule task reminder:', error);
      return null;
    }
  }

  /**
   * Schedule daily scan reminder
   */
  async scheduleDailyScanReminder(hour: number = 10, minute: number = 0): Promise<string | null> {
    try {
      if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        throw new Error('Invalid hour or minute value');
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🔍 Time to Scan!',
          body: 'Discover new species today. Keep your scanning streak alive!',
          data: {
            type: 'scan_reminder',
            action: 'open_camera',
          },
          sound: true,
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.CALENDAR,
          hour,
          minute,
          repeats: true,
        },
      });

      await AsyncStorage.setItem('daily_scan_reminder', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Failed to schedule daily scan reminder:', error);
      return null;
    }
  }

  /**
   * Schedule daily goal reminder
   */
  async scheduleDailyGoalReminder(goalType: string, targetTime: Date): Promise<string | null> {
    try {
      if (!goalType || !targetTime) {
        throw new Error('Invalid goalType or targetTime');
      }

      const titles = {
        scans: '🎯 Daily Scan Goal',
        water: '💧 Hydration Reminder',
        streak: '🔥 Keep Your Streak!',
      };

      const bodies = {
        scans: 'You\'re close to reaching your daily scan goal!',
        water: 'Time to drink some water and stay hydrated.',
        streak: 'Don\'t break your scanning streak - scan something today!',
      };

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: titles[goalType as keyof typeof titles] || '🎯 Daily Goal',
          body: bodies[goalType as keyof typeof bodies] || 'Time to work on your daily goal!',
          data: {
            type: 'daily_goal',
            goalType,
          },
          sound: true,
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: targetTime.getTime(),
        },
      });

      return notificationId;
    } catch (error) {
      console.error('Failed to schedule daily goal reminder:', error);
      return null;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      if (!notificationId) {
        throw new Error('Invalid notificationId');
      }

      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all notifications for a specific task
   */
  async cancelTaskNotifications(taskId: string): Promise<void> {
    try {
      if (!taskId) {
        throw new Error('Invalid taskId');
      }

      const reminders = await this.getStoredReminders();
      const taskReminders = reminders.filter(r => r.taskId === taskId);

      for (const reminder of taskReminders) {
        if (reminder.notificationId) {
          await this.cancelNotification(reminder.notificationId);
        }
      }

      // Remove from storage
      const updatedReminders = reminders.filter(r => r.taskId !== taskId);
      await AsyncStorage.setItem('task_reminders', JSON.stringify(updatedReminders));
    } catch (error) {
      console.error('Failed to cancel task notifications:', error);
    }
  }

  /**
   * Send immediate notification (for testing or instant alerts)
   */
  async sendImmediateNotification(title: string, body: string, data?: any): Promise<void> {
    try {
      if (!title || !body) {
        throw new Error('Invalid title or body');
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: true,
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Store reminder data for management
   */
  private async storeReminder(id: string, reminder: any): Promise<void> {
    try {
      if (!id || !reminder) {
        throw new Error('Invalid id or reminder');
      }

      const existing = await this.getStoredReminders();
      const updated = [...existing.filter(r => r.id !== id), reminder];
      await AsyncStorage.setItem('task_reminders', JSON.stringify(updated));
    } catch (error) {
      console.error('Failed to store reminder:', error);
    }
  }

  /**
   * Get stored reminders
   */
  private async getStoredReminders(): Promise<any[]> {
    try {
      const stored = await AsyncStorage.getItem('task_reminders');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored reminders:', error);
      return [];
    }
  }

  /**
   * Handle notification response (when user taps notification)
   */
  static handleNotificationResponse(response: Notifications.NotificationResponse) {
    try {
      if (!response || !response.notification || !response.notification.request || !response.notification.request.content || !response.notification.request.content.data) {
        throw new Error('Invalid notification response');
      }

      const data = response.notification.request.content.data;

      switch (data.type) {
        case 'task':
          // Navigate to todo screen with specific task
          // This would be handled by your navigation system
          console.log('Open task:', data.taskId);
          break;
        case 'scan_reminder':
          // Navigate to camera screen
          console.log('Open camera for scanning');
          break;
        case 'daily_goal':
          // Navigate to appropriate screen based on goal type
          console.log('Open goal screen:', data.goalType);
          break;
        default:
          console.log('Handle notification:', data);
      }
    } catch (error) {
      console.error('Failed to handle notification response:', error);
    }
  }
}

export default NotificationManager;
