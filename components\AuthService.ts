import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  isAnonymous: boolean;
  createdAt: Date;
  lastLoginAt: Date;
}

export class AuthService {
  private static instance: AuthService;
  private currentUser: FirebaseAuthTypes.User | null = null;
  private authStateListeners: ((user: FirebaseAuthTypes.User | null) => void)[] = [];

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  constructor() {
    this.initializeAuth();
  }

  /**
   * Initialize Firebase Auth and Google Sign-In
   */
  private async initializeAuth(): Promise<void> {
    try {
      // Configure Google Sign-In
      GoogleSignin.configure({
        webClientId: '************-88vibjoi03m5ddm3b7gsfoqrgv7itsvp.apps.googleusercontent.com', // From your google-services.json
        offlineAccess: true,
        hostedDomain: '',
        forceCodeForRefreshToken: true,
      });

      // Listen to auth state changes
      auth().onAuthStateChanged((user) => {
        this.currentUser = user;
        this.notifyAuthStateListeners(user);
        
        if (user) {
          this.saveUserProfile(user);
        } else {
          this.clearUserProfile();
        }
      });

      console.log('✅ AuthService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize AuthService:', error);
    }
  }

  /**
   * Add auth state change listener
   */
  addAuthStateListener(listener: (user: FirebaseAuthTypes.User | null) => void): () => void {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all auth state listeners
   */
  private notifyAuthStateListeners(user: FirebaseAuthTypes.User | null): void {
    this.authStateListeners.forEach(listener => {
      try {
        listener(user);
      } catch (error) {
        console.error('Error in auth state listener:', error);
      }
    });
  }

  /**
   * Sign in with Google
   */
  async signInWithGoogle(): Promise<{ success: boolean; user?: UserProfile; error?: string }> {
    try {
      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      
      // Get user info from Google
      const signInResult = await GoogleSignin.signIn();
      const idToken = signInResult.data?.idToken;

      if (!idToken) {
        throw new Error('No ID token received from Google Sign-In');
      }

      // Create Firebase credential
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Sign in to Firebase
      const userCredential = await auth().signInWithCredential(googleCredential);
      const user = userCredential.user;

      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        isAnonymous: user.isAnonymous,
        createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : new Date(),
      };

      console.log('✅ Google Sign-In successful:', userProfile.email);
      
      return {
        success: true,
        user: userProfile,
      };
    } catch (error: any) {
      console.error('❌ Google Sign-In failed:', error);
      
      // Handle specific error cases
      if (error.code === 'auth/account-exists-with-different-credential') {
        return {
          success: false,
          error: 'An account already exists with this email address but different sign-in method.',
        };
      } else if (error.code === 'auth/invalid-credential') {
        return {
          success: false,
          error: 'Invalid credentials. Please try again.',
        };
      } else if (error.code === 'auth/operation-not-allowed') {
        return {
          success: false,
          error: 'Google Sign-In is not enabled. Please contact support.',
        };
      } else if (error.code === 'auth/user-disabled') {
        return {
          success: false,
          error: 'This account has been disabled. Please contact support.',
        };
      }
      
      return {
        success: false,
        error: error.message || 'Failed to sign in with Google. Please try again.',
      };
    }
  }

  /**
   * Sign in anonymously
   */
  async signInAnonymously(): Promise<{ success: boolean; user?: UserProfile; error?: string }> {
    try {
      const userCredential = await auth().signInAnonymously();
      const user = userCredential.user;

      const userProfile: UserProfile = {
        uid: user.uid,
        email: null,
        displayName: null,
        photoURL: null,
        isAnonymous: true,
        createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : new Date(),
      };

      console.log('✅ Anonymous sign-in successful');
      
      return {
        success: true,
        user: userProfile,
      };
    } catch (error: any) {
      console.error('❌ Anonymous sign-in failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to sign in anonymously.',
      };
    }
  }

  /**
   * Sign out
   */
  async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      // Sign out from Google
      if (await GoogleSignin.isSignedIn()) {
        await GoogleSignin.signOut();
      }
      
      // Sign out from Firebase
      await auth().signOut();
      
      console.log('✅ Sign-out successful');
      
      return { success: true };
    } catch (error: any) {
      console.error('❌ Sign-out failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to sign out.',
      };
    }
  }

  /**
   * Get current user
   */
  getCurrentUser(): FirebaseAuthTypes.User | null {
    return this.currentUser;
  }

  /**
   * Get current user profile
   */
  async getCurrentUserProfile(): Promise<UserProfile | null> {
    if (!this.currentUser) {
      return null;
    }

    return {
      uid: this.currentUser.uid,
      email: this.currentUser.email,
      displayName: this.currentUser.displayName,
      photoURL: this.currentUser.photoURL,
      isAnonymous: this.currentUser.isAnonymous,
      createdAt: this.currentUser.metadata.creationTime ? new Date(this.currentUser.metadata.creationTime) : new Date(),
      lastLoginAt: this.currentUser.metadata.lastSignInTime ? new Date(this.currentUser.metadata.lastSignInTime) : new Date(),
    };
  }

  /**
   * Check if user is signed in
   */
  isSignedIn(): boolean {
    return this.currentUser !== null;
  }

  /**
   * Check if user is anonymous
   */
  isAnonymous(): boolean {
    return this.currentUser?.isAnonymous || false;
  }

  /**
   * Get user ID for RevenueCat
   */
  getRevenueCatUserId(): string | null {
    return this.currentUser?.uid || null;
  }

  /**
   * Save user profile to local storage
   */
  private async saveUserProfile(user: FirebaseAuthTypes.User): Promise<void> {
    try {
      const profile: UserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        isAnonymous: user.isAnonymous,
        createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : new Date(),
      };

      await AsyncStorage.setItem('user_profile', JSON.stringify(profile));
    } catch (error) {
      console.error('Failed to save user profile:', error);
    }
  }

  /**
   * Clear user profile from local storage
   */
  private async clearUserProfile(): Promise<void> {
    try {
      await AsyncStorage.removeItem('user_profile');
    } catch (error) {
      console.error('Failed to clear user profile:', error);
    }
  }

  /**
   * Link anonymous account with Google
   */
  async linkWithGoogle(): Promise<{ success: boolean; user?: UserProfile; error?: string }> {
    try {
      if (!this.currentUser || !this.currentUser.isAnonymous) {
        throw new Error('No anonymous user to link');
      }

      // Get Google credentials
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      const signInResult = await GoogleSignin.signIn();
      const idToken = signInResult.data?.idToken;

      if (!idToken) {
        throw new Error('No ID token received from Google Sign-In');
      }

      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Link the accounts
      const userCredential = await this.currentUser.linkWithCredential(googleCredential);
      const user = userCredential.user;

      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        isAnonymous: user.isAnonymous,
        createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : new Date(),
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : new Date(),
      };

      console.log('✅ Account linking successful');
      
      return {
        success: true,
        user: userProfile,
      };
    } catch (error: any) {
      console.error('❌ Account linking failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to link accounts.',
      };
    }
  }
}

export default AuthService;
