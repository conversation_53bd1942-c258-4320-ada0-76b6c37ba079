import WidgetKit
import SwiftUI

struct QuickScanProvider: TimelineProvider {
    func placeholder(in context: Context) -> QuickScanEntry {
        QuickScanEntry(date: Date(), remainingScans: 5, isUnlimited: false, todayScans: 0)
    }

    func getSnapshot(in context: Context, completion: @escaping (QuickScanEntry) -> ()) {
        let entry = QuickScanEntry(date: Date(), remainingScans: 3, isUnlimited: false, todayScans: 2)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [QuickScanEntry] = []

        let currentDate = Date()
        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = QuickScanEntry(
                date: entryDate,
                remainingScans: getRemainingScans(),
                isUnlimited: getIsUnlimited(),
                todayScans: getTodayScans()
            )
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
    
    private func getRemainingScans() -> Int {
        // In real implementation, fetch from shared storage
        return Int.random(in: 0...5)
    }
    
    private func getIsUnlimited() -> Bool {
        // Check subscription status from shared storage
        return Bool.random()
    }
    
    private func getTodayScans() -> Int {
        return Int.random(in: 0...10)
    }
}

struct QuickScanEntry: TimelineEntry {
    let date: Date
    let remainingScans: Int
    let isUnlimited: Bool
    let todayScans: Int
}

struct QuickScanWidgetEntryView: View {
    var entry: QuickScanProvider.Entry

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "camera.fill")
                    .foregroundColor(.white)
                    .font(.title2)
                
                Text("Quick Scan")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Spacer()
            
            // Scan Button
            VStack(spacing: 8) {
                Button(action: {
                    // This will be handled by the app when widget is tapped
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "viewfinder")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text("Scan Now")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.white.opacity(0.2))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Scan count info
                HStack {
                    if entry.isUnlimited {
                        Text("∞ Unlimited")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    } else {
                        Text("\(entry.remainingScans) scans left")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    Text("\(entry.todayScans) today")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue, Color.purple]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(16)
    }
}

struct QuickScanWidget: Widget {
    let kind: String = "QuickScanWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: QuickScanProvider()) { entry in
            QuickScanWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Quick Scan")
        .description("Fast access to camera scanning with remaining scan count.")
        .supportedFamilies([.systemSmall])
    }
}

struct QuickScanWidget_Previews: PreviewProvider {
    static var previews: some View {
        QuickScanWidgetEntryView(entry: QuickScanEntry(
            date: Date(),
            remainingScans: 3,
            isUnlimited: false,
            todayScans: 2
        ))
        .previewContext(WidgetPreviewContext(family: .systemSmall))
    }
}
