import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { IconSymbol } from './ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from './AuthContext';

interface SignInScreenProps {
  onDismiss?: () => void;
  showAnonymousOption?: boolean;
  title?: string;
  subtitle?: string;
}

export const SignInScreen: React.FC<SignInScreenProps> = ({
  onDismiss,
  showAnonymousOption = true,
  title = "Welcome to Biosapex",
  subtitle = "Sign in to sync your data across devices and unlock premium features",
}) => {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { signInWithGoogle, signInAnonymously, isLoading } = useAuth();
  const [signingIn, setSigningIn] = useState<'google' | 'anonymous' | null>(null);

  const handleGoogleSignIn = async () => {
    setSigningIn('google');
    try {
      const result = await signInWithGoogle();
      
      if (result.success) {
        Alert.alert(
          'Welcome!',
          'You\'ve successfully signed in. Your data will now sync across all your devices.',
          [
            {
              text: 'OK',
              onPress: () => {
                onDismiss?.();
              },
            },
          ]
        );
      } else {
        Alert.alert('Sign In Failed', result.error || 'Please try again.');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setSigningIn(null);
    }
  };

  const handleAnonymousSignIn = async () => {
    setSigningIn('anonymous');
    try {
      const result = await signInAnonymously();
      
      if (result.success) {
        onDismiss?.();
      } else {
        Alert.alert('Error', result.error || 'Please try again.');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setSigningIn(null);
    }
  };

  const isButtonDisabled = isLoading || signingIn !== null;

  return (
    <ThemedView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
      <LinearGradient
        colors={[
          Colors[colorScheme ?? 'light'].tint + '20',
          Colors[colorScheme ?? 'light'].background,
        ]}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          {onDismiss && (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onDismiss}
              disabled={isButtonDisabled}
            >
              <IconSymbol size={24} name="xmark" color={Colors[colorScheme ?? 'light'].text} />
            </TouchableOpacity>
          )}
        </View>

        {/* Content */}
        <View style={styles.content}>
          {/* App Icon/Logo */}
          <View style={[styles.logoContainer, { backgroundColor: Colors[colorScheme ?? 'light'].tint + '20' }]}>
            <IconSymbol size={60} name="leaf.fill" color={Colors[colorScheme ?? 'light'].tint} />
          </View>

          {/* Title and Subtitle */}
          <ThemedText style={styles.title}>{title}</ThemedText>
          <ThemedText style={[styles.subtitle, { color: Colors[colorScheme ?? 'light'].text + 'CC' }]}>
            {subtitle}
          </ThemedText>

          {/* Benefits */}
          <View style={styles.benefitsContainer}>
            <View style={styles.benefitItem}>
              <IconSymbol size={20} name="icloud.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.benefitText}>Sync across all devices</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol size={20} name="shield.checkered" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.benefitText}>Secure data backup</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol size={20} name="star.fill" color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.benefitText}>Premium features</ThemedText>
            </View>
          </View>

          {/* Sign In Buttons */}
          <View style={styles.buttonsContainer}>
            {/* Google Sign In */}
            <TouchableOpacity
              style={[
                styles.googleButton,
                { backgroundColor: Colors[colorScheme ?? 'light'].background },
                isButtonDisabled && styles.disabledButton,
              ]}
              onPress={handleGoogleSignIn}
              disabled={isButtonDisabled}
            >
              {signingIn === 'google' ? (
                <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].text} />
              ) : (
                <>
                  <Image
                    source={{ uri: 'https://developers.google.com/identity/images/g-logo.png' }}
                    style={styles.googleIcon}
                  />
                  <ThemedText style={styles.googleButtonText}>Continue with Google</ThemedText>
                </>
              )}
            </TouchableOpacity>

            {/* Anonymous Sign In */}
            {showAnonymousOption && (
              <TouchableOpacity
                style={[
                  styles.anonymousButton,
                  { borderColor: Colors[colorScheme ?? 'light'].text + '40' },
                  isButtonDisabled && styles.disabledButton,
                ]}
                onPress={handleAnonymousSignIn}
                disabled={isButtonDisabled}
              >
                {signingIn === 'anonymous' ? (
                  <ActivityIndicator size="small" color={Colors[colorScheme ?? 'light'].text} />
                ) : (
                  <ThemedText style={[styles.anonymousButtonText, { color: Colors[colorScheme ?? 'light'].text + 'CC' }]}>
                    Continue without account
                  </ThemedText>
                )}
              </TouchableOpacity>
            )}
          </View>

          {/* Privacy Note */}
          <ThemedText style={[styles.privacyNote, { color: Colors[colorScheme ?? 'light'].text + '80' }]}>
            By continuing, you agree to our Terms of Service and Privacy Policy. Your data is encrypted and secure.
          </ThemedText>
        </View>
      </LinearGradient>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
    maxWidth: 300,
  },
  benefitsContainer: {
    marginBottom: 40,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 16,
    marginLeft: 12,
  },
  buttonsContainer: {
    width: '100%',
    maxWidth: 300,
    marginBottom: 30,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: 12,
  },
  googleButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  anonymousButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
  },
  anonymousButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.6,
  },
  privacyNote: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
    maxWidth: 280,
  },
});

export default SignInScreen;
