import React from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { useTheme } from '@/components/ThemeContext'; // Assuming you have a ThemeContext
import { X } from 'lucide-react-native'; // For a close icon

interface ThemeSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTheme: (theme: string) => void;
  currentTheme: string;
}

const ThemeSelectionModal: React.FC<ThemeSelectionModalProps> = ({
  visible,
  onClose,
  onSelectTheme,
  currentTheme,
}) => {
  const { colors } = useTheme(); // Get current theme colors

  // Define your available themes
  const themes = [
    { id: 'light', name: 'Light' },
    { id: 'dark', name: 'Dark' },
    { id: 'system', name: 'System Default' }, // Example
    // Add more themes as needed
  ];

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={[styles.modalView, { backgroundColor: colors.card }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Theme</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.themeOptionsContainer}>
            {themes.map((theme) => (
              <TouchableOpacity
                key={theme.id}
                style={[
                  styles.themeOption,
                  { borderBottomColor: colors.border },
                  currentTheme === theme.id && styles.selectedThemeOption,
                ]}
                onPress={() => {
                  onSelectTheme(theme.id);
                  onClose();
                }}
              >
                <Text style={[styles.themeOptionText, { color: colors.text }]}>
                  {theme.name}
                </Text>
                {currentTheme === theme.id && (
                  <Text style={{ color: colors.primary }}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'flex-end', // Or 'center' depending on desired modal presentation
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    width: '100%',
    maxHeight: '80%', // Adjust as needed
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
  },
  closeButton: {
    padding: 5,
  },
  themeOptionsContainer: {
    // Styles for the container of theme options
  },
  themeOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  selectedThemeOption: {
    // Add styles for the selected theme option if needed
  },
  themeOptionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
});

export default ThemeSelectionModal;
