import React, { useEffect, useState } from 'react';
import { View, Alert, ActivityIndicator, StyleSheet } from 'react-native';
import { PaywallView, PaywallViewProps } from 'react-native-purchases-ui';
import { useRouter } from 'expo-router';
import { useSubscription } from './SubscriptionContext';
import RevenueCatService from './RevenueCatService';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface RevenueCatPaywallProps {
  onDismiss?: () => void;
  onPurchaseCompleted?: () => void;
  onRestoreCompleted?: () => void;
  offering?: string; // Optional offering identifier
}

export const RevenueCatPaywall: React.FC<RevenueCatPaywallProps> = ({
  onDismiss,
  onPurchaseCompleted,
  onRestoreCompleted,
  offering = 'default',
}) => {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { subscription } = useSubscription();
  const [isLoading, setIsLoading] = useState(true);
  const [hasOffering, setHasOffering] = useState(false);
  const revenueCatService = RevenueCatService.getInstance();

  useEffect(() => {
    checkOfferingAvailability();
  }, []);

  const checkOfferingAvailability = async () => {
    try {
      if (!revenueCatService.isRevenueCatInitialized()) {
        console.warn('RevenueCat not initialized, falling back to custom paywall');
        setHasOffering(false);
        setIsLoading(false);
        return;
      }

      const offerings = await revenueCatService.loadOfferings();
      setHasOffering(!!offerings);
    } catch (error) {
      console.error('Error checking offering availability:', error);
      setHasOffering(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePurchaseStarted = () => {
    console.log('Purchase started');
  };

  const handlePurchaseCompleted = () => {
    console.log('Purchase completed successfully');
    Alert.alert(
      'Success!',
      'Your subscription has been activated. Welcome to premium features!',
      [
        {
          text: 'OK',
          onPress: () => {
            onPurchaseCompleted?.();
            onDismiss?.();
          },
        },
      ]
    );
  };

  const handlePurchaseError = (error: any) => {
    console.error('Purchase error:', error);
    
    // Don't show error for user cancellation
    if (error.userCancelled) {
      return;
    }

    Alert.alert(
      'Purchase Failed',
      error.message || 'Unable to complete purchase. Please try again.',
      [{ text: 'OK' }]
    );
  };

  const handleRestoreStarted = () => {
    console.log('Restore started');
  };

  const handleRestoreCompleted = () => {
    console.log('Restore completed successfully');
    Alert.alert(
      'Restored!',
      'Your purchases have been restored successfully.',
      [
        {
          text: 'OK',
          onPress: () => {
            onRestoreCompleted?.();
            onDismiss?.();
          },
        },
      ]
    );
  };

  const handleRestoreError = (error: any) => {
    console.error('Restore error:', error);
    Alert.alert(
      'Restore Failed',
      'No previous purchases found or unable to restore. Please try again.',
      [{ text: 'OK' }]
    );
  };

  const handleDismiss = () => {
    console.log('Paywall dismissed');
    onDismiss?.();
  };

  const fallbackToCustomPaywall = () => {
    // Navigate to custom subscription screen
    router.push('/subscription');
    onDismiss?.();
  };

  if (isLoading) {
    return (
      <ThemedView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].tint} />
          <ThemedText style={styles.loadingText}>Loading subscription options...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // If RevenueCat is not available or no offering, fall back to custom paywall
  if (!hasOffering) {
    return (
      <ThemedView style={[styles.container, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
        <View style={styles.fallbackContainer}>
          <ThemedText style={styles.fallbackText}>
            Loading subscription options...
          </ThemedText>
          <ThemedText style={styles.fallbackSubtext}>
            If this takes too long, we'll show you our subscription plans.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  const paywallProps: PaywallViewProps = {
    offering,
    onPurchaseStarted: handlePurchaseStarted,
    onPurchaseCompleted: handlePurchaseCompleted,
    onPurchaseError: handlePurchaseError,
    onRestoreStarted: handleRestoreStarted,
    onRestoreCompleted: handleRestoreCompleted,
    onRestoreError: handleRestoreError,
    onDismiss: handleDismiss,
  };

  return (
    <View style={styles.paywallContainer}>
      <PaywallView {...paywallProps} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  fallbackContainer: {
    alignItems: 'center',
    padding: 20,
    maxWidth: 300,
  },
  fallbackText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  fallbackSubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
  },
  paywallContainer: {
    flex: 1,
  },
});

export default RevenueCatPaywall;
