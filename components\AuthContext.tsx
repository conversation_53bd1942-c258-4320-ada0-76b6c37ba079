import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { FirebaseAuthTypes } from '@react-native-firebase/auth';
import AuthService, { UserProfile } from './AuthService';

interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  isSignedIn: boolean;
  isAnonymous: boolean;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInAnonymously: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>;
  linkWithGoogle: () => Promise<{ success: boolean; error?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const authService = AuthService.getInstance();

  useEffect(() => {
    // Listen to auth state changes
    const unsubscribe = authService.addAuthStateListener(async (firebaseUser) => {
      setUser(firebaseUser);
      
      if (firebaseUser) {
        const profile = await authService.getCurrentUserProfile();
        setUserProfile(profile);
      } else {
        setUserProfile(null);
      }
      
      setIsLoading(false);
    });

    // Initialize with current user
    const initializeAuth = async () => {
      const currentUser = authService.getCurrentUser();
      setUser(currentUser);
      
      if (currentUser) {
        const profile = await authService.getCurrentUserProfile();
        setUserProfile(profile);
      }
      
      setIsLoading(false);
    };

    initializeAuth();

    return unsubscribe;
  }, []);

  const signInWithGoogle = async (): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    try {
      const result = await authService.signInWithGoogle();
      return { success: result.success, error: result.error };
    } finally {
      setIsLoading(false);
    }
  };

  const signInAnonymously = async (): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    try {
      const result = await authService.signInAnonymously();
      return { success: result.success, error: result.error };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    try {
      const result = await authService.signOut();
      return { success: result.success, error: result.error };
    } finally {
      setIsLoading(false);
    }
  };

  const linkWithGoogle = async (): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    try {
      const result = await authService.linkWithGoogle();
      return { success: result.success, error: result.error };
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: AuthContextType = {
    user,
    userProfile,
    isLoading,
    isSignedIn: authService.isSignedIn(),
    isAnonymous: authService.isAnonymous(),
    signInWithGoogle,
    signInAnonymously,
    signOut,
    linkWithGoogle,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
