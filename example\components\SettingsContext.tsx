import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native';
import HapticService from '@/components/HapticService'; // Ensure path is correct

// A simple utility for deep merging objects, crucial for updating nested settings.
const isObject = (item: any): item is Object => {
  return (item && typeof item === 'object' && !Array.isArray(item));
};

const deepMerge = <T extends object>(target: T, ...sources: Partial<T>[]): T => {
  if (!sources.length) {
    return target;
  }
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) {
          Object.assign(target, { [key]: {} });
        }
        deepMerge(target[key] as object, source[key] as Partial<object>);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
};


export interface UserSettings {
  apiKey: string;
  theme: 'light' | 'dark' | 'auto';
  textSize: 'small' | 'normal' | 'large' | 'extra-large';
  layout: {
    navigationStyle: 'auto' | 'bottom' | 'side';
    columnLayout: 'auto' | 'single' | 'multi';
    spacing: 'compact' | 'normal' | 'spacious';
  };
  notifications: {
    enabled: boolean;
    scanResults: boolean;
    achievements: boolean;
    community: boolean;
  };
  camera: {
    autoFocus: boolean;
    flashMode: 'auto' | 'on' | 'off';
    saveOriginals: boolean;
    imageQuality: 'low' | 'medium' | 'high';
  };
  identification: {
    confidenceThreshold: number;
    maxAlternatives: number;
    enableOfflineMode: boolean;
    autoSave: boolean;
  };
  privacy: {
    shareLocation: boolean;
    publicProfile: boolean;
    dataCollection: boolean;
  };
  accessibility: {
    soundEnabled: boolean;
    hapticFeedback: boolean;
    highContrast: boolean;
  };
  language: string;
  units: 'metric' | 'imperial';
}

const defaultSettings: UserSettings = {
  apiKey: '',
  theme: 'auto',
  textSize: 'normal',
  layout: {
    navigationStyle: 'auto',
    columnLayout: 'auto',
    spacing: 'normal',
  },
  notifications: {
    enabled: true,
    scanResults: true,
    achievements: true,
    community: false,
  },
  camera: {
    autoFocus: true,
    flashMode: 'auto',
    saveOriginals: true,
    imageQuality: 'high',
  },
  identification: {
    confidenceThreshold: 70,
    maxAlternatives: 3,
    enableOfflineMode: false,
    autoSave: true,
  },
  privacy: {
    shareLocation: false,
    publicProfile: false,
    dataCollection: true,
  },
  accessibility: {
    soundEnabled: true,
    hapticFeedback: true,
    highContrast: false,
  },
  language: 'en',
  units: 'metric',
};

interface SettingsContextType {
  settings: UserSettings;
  updateSettings: (newSettings: Partial<UserSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  isLoading: boolean;
  getEffectiveTheme: () => 'light' | 'dark';
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const systemTheme = Appearance.getColorScheme() ?? 'light';

  useEffect(() => {
    const loadAndSyncSettings = async () => {
      try {
        const savedSettingsJSON = await AsyncStorage.getItem('userSettings');
        const savedSettings = savedSettingsJSON ? JSON.parse(savedSettingsJSON) : {};
        
        // Merge default settings with saved settings to ensure all keys are present
        const mergedSettings = deepMerge({ ...defaultSettings }, savedSettings);
        setSettings(mergedSettings);

        // Sync haptics setting on initial load
        HapticService.getInstance().setEnabled(mergedSettings.accessibility.hapticFeedback);

      } catch (error) {
        console.error('Failed to load settings:', error);
        // Fallback to default settings in case of parsing error
        setSettings(defaultSettings);
      } finally {
        setIsLoading(false);
      }
    };

    loadAndSyncSettings();

    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
        // This will trigger a re-render in components that use getEffectiveTheme
        // by being a state change in the provider's scope, even if systemTheme isn't directly in context value
        setSettings(s => ({...s})); 
    });

    return () => subscription.remove();
  }, []);

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    try {
      // Create a new object for the state update to ensure re-render
      const newSettingsState = deepMerge({ ...settings }, newSettings);
      setSettings(newSettingsState);
      await AsyncStorage.setItem('userSettings', JSON.stringify(newSettingsState));

      // Sync haptics if it was changed
      if (newSettings.accessibility?.hapticFeedback !== undefined) {
          HapticService.getInstance().setEnabled(newSettings.accessibility.hapticFeedback);
      }

    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error; // Re-throw so UI can handle it
    }
  };

  const resetSettings = async () => {
    try {
      setSettings(defaultSettings);
      await AsyncStorage.setItem('userSettings', JSON.stringify(defaultSettings));
      HapticService.getInstance().setEnabled(defaultSettings.accessibility.hapticFeedback);
    } catch (error) {
      console.error('Failed to reset settings:', error);
      throw error;
    }
  };

  const getEffectiveTheme = () => {
    if (settings.theme === 'auto') {
      return Appearance.getColorScheme() ?? 'light';
    }
    return settings.theme;
  };

  return (
    <SettingsContext.Provider
      value={{
        settings,
        updateSettings,
        resetSettings,
        isLoading,
        getEffectiveTheme,
      }}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;
